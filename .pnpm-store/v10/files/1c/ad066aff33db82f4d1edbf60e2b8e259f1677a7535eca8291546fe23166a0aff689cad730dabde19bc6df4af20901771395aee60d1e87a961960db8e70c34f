{"name": "@chatwoot/prosemirror-schema", "version": "1.2.1", "description": "Schema setup for using prosemirror in chatwoot. Based on 👉 https://github.com/ProseMirror/prosemirror-example-setup/", "main": "dist/index.es.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "rollup -c", "prepare": "husky install"}, "repository": "git+https://github.com/chatwoot/prosemirror-schema.git", "files": ["src/*"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/chatwoot/prosemirror-schema/issues"}, "homepage": "https://github.com/chatwoot/prosemirror-schema#readme", "dependencies": {"markdown-it-sup": "^2.0.0", "prosemirror-commands": "^1.6.0", "prosemirror-dropcursor": "^1.8.1", "prosemirror-gapcursor": "^1.3.2", "prosemirror-history": "^1.4.1", "prosemirror-inputrules": "^1.4.0", "prosemirror-keymap": "^1.2.2", "prosemirror-markdown": "1.13.0", "prosemirror-menu": "^1.2.4", "prosemirror-model": "^1.22.3", "prosemirror-schema-list": "^1.4.1", "prosemirror-state": "^1.4.3", "prosemirror-tables": "^1.5.0", "prosemirror-utils": "^1.2.2", "prosemirror-view": "^1.34.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@rollup/plugin-babel": "^6.0.4", "eslint": "^9.9.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "husky": "^9.1.5", "lint-staged": "^15.2.9", "rollup": "^4.21.2"}, "lint-staged": {"*.js": "eslint --fix"}}