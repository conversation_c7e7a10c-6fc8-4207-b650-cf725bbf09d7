import { debounce } from './debounce';
import {
  formatTime,
  formatDate,
  getContrastingTextColor,
  trimContent,
  convertSecondsToTimeUnit,
  fileNameWithEllipsis,
  splitName,
  downloadFile,
  getFileInfo,
  formatNumber,
} from './helpers';

import { toURL, isSameHost, isValidDomain } from './url';

import { getRecipients } from './email';

import { parseBoolean } from './string';
import {
  sortAsc,
  quantile,
  clamp,
  getQuantileIntervals,
  calculateCenterOffset,
  applyRotationTransform,
  normalizeToPercentage,
} from './math';
import {
  getMessageVariables,
  replaceVariablesInMessage,
  getUndefinedVariablesInMessage,
} from './canned';

import { createTypingIndicator } from './typingStatus';

import { evaluateSLAStatus } from './sla';

import { coerceToDate } from './date';

export {
  clamp,
  coerceToDate,
  convertSecondsToTimeUnit,
  createTypingIndicator,
  debounce,
  evaluateSLAStatus,
  fileNameWithEllipsis,
  formatDate,
  formatTime,
  getContrastingTextColor,
  getMessageVariables,
  getQuantileIntervals,
  calculateCenterOffset,
  applyRotationTransform,
  normalizeToPercentage,
  getUndefinedVariablesInMessage,
  parseBoolean,
  quantile,
  replaceVariablesInMessage,
  sortAsc,
  splitName,
  toURL,
  isSameHost,
  isValidDomain,
  trimContent,
  downloadFile,
  getFileInfo,
  getRecipients,
  formatNumber,
};
