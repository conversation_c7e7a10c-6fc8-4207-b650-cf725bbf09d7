/*!
  * core-base v9.14.5
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var IntlifyCoreBase=function(e){"use strict";function t(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const n=/\{([0-9a-zA-Z]+)\}/g;const r=(e,t,n)=>o({l:e,k:t,s:n}),o=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),c=e=>"number"==typeof e&&isFinite(e),s=e=>"[object Date]"===k(e),a=e=>"[object RegExp]"===k(e),l=e=>O(e)&&0===Object.keys(e).length,i=Object.assign,u=Object.create,f=(e=null)=>u(e);function E(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/\//g,"&#x2F;").replace(/=/g,"&#x3D;")}function _(e){return e.replace(/&(?![a-zA-Z0-9#]{2,6};)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}const m=Object.prototype.hasOwnProperty;function p(e,t){return m.call(e,t)}const d=Array.isArray,N=e=>"function"==typeof e,T=e=>"string"==typeof e,L=e=>"boolean"==typeof e,A=e=>null!==e&&"object"==typeof e,C=e=>A(e)&&N(e.then)&&N(e.catch),h=Object.prototype.toString,k=e=>h.call(e),O=e=>{if(!A(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t.constructor===Object};function g(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}function I(e){let t=e;return()=>++t}function S(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const y={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2};const b={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17};function P(e,t,n={}){const{domain:r,messages:o,args:c}=n,s=new SyntaxError(String(e));return s.code=e,t&&(s.location=t),s.domain=r,s}function D(e){throw e}b.EXPECTED_TOKEN,b.INVALID_TOKEN_IN_PLACEHOLDER,b.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,b.UNKNOWN_ESCAPE_SEQUENCE,b.INVALID_UNICODE_ESCAPE_SEQUENCE,b.UNBALANCED_CLOSING_BRACE,b.UNTERMINATED_CLOSING_BRACE,b.EMPTY_PLACEHOLDER,b.NOT_ALLOW_NEST_PLACEHOLDER,b.INVALID_LINKED_FORMAT,b.MUST_HAVE_MESSAGES_IN_PLURAL,b.UNEXPECTED_EMPTY_LINKED_MODIFIER,b.UNEXPECTED_EMPTY_LINKED_KEY,b.UNEXPECTED_LEXICAL_ANALYSIS,b.UNHANDLED_CODEGEN_NODE_TYPE,b.UNHANDLED_MINIFIER_NODE_TYPE;const M=" ",R="\r",U="\n",v=String.fromCharCode(8232),x=String.fromCharCode(8233);function F(e){const t=e;let n=0,r=1,o=1,c=0;const s=e=>t[e]===R&&t[e+1]===U,a=e=>t[e]===x,l=e=>t[e]===v,i=e=>s(e)||(e=>t[e]===U)(e)||a(e)||l(e),u=e=>s(e)||a(e)||l(e)?U:t[e];function f(){return c=0,i(n)&&(r++,o=0),s(n)&&n++,n++,o++,t[n]}return{index:()=>n,line:()=>r,column:()=>o,peekOffset:()=>c,charAt:u,currentChar:()=>u(n),currentPeek:()=>u(n+c),next:f,peek:function(){return s(n+c)&&c++,c++,t[n+c]},reset:function(){n=0,r=1,o=1,c=0},resetPeek:function(e=0){c=e},skipToPeek:function(){const e=n+c;for(;e!==n;)f();c=0}}}const w=void 0,W="'";function X(e,t={}){const n=!1!==t.location,r=F(e),o=()=>r.index(),c=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},s=c(),a=o(),l={currentType:14,offset:a,startLoc:s,endLoc:s,lastType:14,lastOffset:a,lastStartLoc:s,lastEndLoc:s,braceNest:0,inLinked:!1,text:""},i=()=>l,{onError:u}=t;function f(e,t,r){e.endLoc=c(),e.currentType=t;const o={type:t};return n&&(o.loc=S(e.startLoc,e.endLoc)),null!=r&&(o.value=r),o}const E=e=>f(e,14);function _(e,t){return e.currentChar()===t?(e.next(),t):(b.EXPECTED_TOKEN,c(),"")}function m(e){let t="";for(;e.currentPeek()===M||e.currentPeek()===U;)t+=e.currentPeek(),e.peek();return t}function p(e){const t=m(e);return e.skipToPeek(),t}function d(e){if(e===w)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function N(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r=function(e){if(e===w)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function T(e){m(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function L(e,t=!0){const n=(t=!1,r="",o=!1)=>{const c=e.currentPeek();return"{"===c?"%"!==r&&t:"@"!==c&&c?"%"===c?(e.peek(),n(t,"%",!0)):"|"===c?!("%"!==r&&!o)||!(r===M||r===U):c===M?(e.peek(),n(!0,M,o)):c!==U||(e.peek(),n(!0,U,o)):"%"===r||t},r=n();return t&&e.resetPeek(),r}function A(e,t){const n=e.currentChar();return n===w?w:t(n)?(e.next(),n):null}function C(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function h(e){return A(e,C)}function k(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function O(e){return A(e,k)}function g(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function I(e){return A(e,g)}function y(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function P(e){return A(e,y)}function D(e){let t="",n="";for(;t=I(e);)n+=t;return n}function R(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if("%"===n){if(!L(e))break;t+=n,e.next()}else if(n===M||n===U)if(L(e))t+=n,e.next();else{if(T(e))break;t+=n,e.next()}else t+=n,e.next()}return t}function v(e){return e!==W&&e!==U}function x(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return X(e,t,4);case"U":return X(e,t,6);default:return b.UNKNOWN_ESCAPE_SEQUENCE,c(),""}}function X(e,t,n){_(e,t);let r="";for(let o=0;o<n;o++){const t=P(e);if(!t){b.INVALID_UNICODE_ESCAPE_SEQUENCE,c(),e.currentChar();break}r+=t}return`\\${t}${r}`}function $(e){return"{"!==e&&"}"!==e&&e!==M&&e!==U}function Y(e){p(e);const t=_(e,"|");return p(e),t}function K(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&(b.NOT_ALLOW_NEST_PLACEHOLDER,c()),e.next(),n=f(t,2,"{"),p(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&(b.EMPTY_PLACEHOLDER,c()),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&p(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&(b.UNTERMINATED_CLOSING_BRACE,c()),n=G(e,t)||E(t),t.braceNest=0,n;default:{let r=!0,o=!0,s=!0;if(T(e))return t.braceNest>0&&(b.UNTERMINATED_CLOSING_BRACE,c()),n=f(t,1,Y(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return b.UNTERMINATED_CLOSING_BRACE,c(),t.braceNest=0,V(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r=d(e.currentPeek());return e.resetPeek(),r}(e,t))return n=f(t,5,function(e){p(e);let t="",n="";for(;t=O(e);)n+=t;return e.currentChar()===w&&(b.UNTERMINATED_CLOSING_BRACE,c()),n}(e)),p(e),n;if(o=N(e,t))return n=f(t,6,function(e){p(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${D(e)}`):t+=D(e),e.currentChar()===w&&(b.UNTERMINATED_CLOSING_BRACE,c()),t}(e)),p(e),n;if(s=function(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r=e.currentPeek()===W;return e.resetPeek(),r}(e,t))return n=f(t,7,function(e){p(e),_(e,"'");let t="",n="";for(;t=A(e,v);)n+="\\"===t?x(e):t;const r=e.currentChar();return r===U||r===w?(b.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,c(),r===U&&(e.next(),_(e,"'")),n):(_(e,"'"),n)}(e)),p(e),n;if(!r&&!o&&!s)return n=f(t,13,function(e){p(e);let t="",n="";for(;t=A(e,$);)n+=t;return n}(e)),b.INVALID_TOKEN_IN_PLACEHOLDER,c(),n.value,p(e),n;break}}return n}function G(e,t){const{currentType:n}=t;let r=null;const o=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||o!==U&&o!==M||(b.INVALID_LINKED_FORMAT,c()),o){case"@":return e.next(),r=f(t,8,"@"),t.inLinked=!0,r;case".":return p(e),e.next(),f(t,9,".");case":":return p(e),e.next(),f(t,10,":");default:return T(e)?(r=f(t,1,Y(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(8!==n)return!1;m(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;m(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(p(e),G(e,t)):function(e,t){const{currentType:n}=t;if(9!==n)return!1;m(e);const r=d(e.currentPeek());return e.resetPeek(),r}(e,t)?(p(e),f(t,12,function(e){let t="",n="";for(;t=h(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(10!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?d(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||t===M||!t)&&(t===U?(e.peek(),r()):L(e,!1))},o=r();return e.resetPeek(),o}(e,t)?(p(e),"{"===o?K(e,t)||r:f(t,11,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"%"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===M?n:(n+=r,e.next(),t(n)):n};return t("")}(e))):(8===n&&(b.INVALID_LINKED_FORMAT,c()),t.braceNest=0,t.inLinked=!1,V(e,t))}}function V(e,t){let n={type:14};if(t.braceNest>0)return K(e,t)||E(t);if(t.inLinked)return G(e,t)||E(t);switch(e.currentChar()){case"{":return K(e,t)||E(t);case"}":return b.UNBALANCED_CLOSING_BRACE,c(),e.next(),f(t,3,"}");case"@":return G(e,t)||E(t);default:{if(T(e))return n=f(t,1,Y(e)),t.braceNest=0,t.inLinked=!1,n;const{isModulo:r,hasSpace:o}=function(e){const t=m(e),n="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:n,hasSpace:t.length>0}}(e);if(r)return o?f(t,0,R(e)):f(t,4,function(e){p(e);const t=e.currentChar();return"%"!==t&&(b.EXPECTED_TOKEN,c()),e.next(),"%"}(e));if(L(e))return f(t,0,R(e));break}}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:s}=l;return l.lastType=e,l.lastOffset=t,l.lastStartLoc=n,l.lastEndLoc=s,l.offset=o(),l.startLoc=c(),r.currentChar()===w?f(l,14):V(r,l)},currentOffset:o,currentPosition:c,context:i}}const $=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function Y(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function K(e={}){const t=!1!==e.location,{onError:n,onWarn:r}=e;function o(e,n,r){const o={type:e};return t&&(o.start=n,o.end=n,o.loc={start:r,end:r}),o}function c(e,n,r,o){o&&(e.type=o),t&&(e.end=n,e.loc&&(e.loc.end=r))}function s(e,t){const n=e.context(),r=o(3,n.offset,n.startLoc);return r.value=t,c(r,e.currentOffset(),e.currentPosition()),r}function a(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:s}=n,a=o(5,r,s);return a.index=parseInt(t,10),e.nextToken(),c(a,e.currentOffset(),e.currentPosition()),a}function l(e,t,n){const r=e.context(),{lastOffset:s,lastStartLoc:a}=r,l=o(4,s,a);return l.key=t,!0===n&&(l.modulo=!0),e.nextToken(),c(l,e.currentOffset(),e.currentPosition()),l}function u(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:s}=n,a=o(9,r,s);return a.value=t.replace($,Y),e.nextToken(),c(a,e.currentOffset(),e.currentPosition()),a}function f(e){const t=e.context(),n=o(6,t.offset,t.startLoc);let r=e.nextToken();if(9===r.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:r,lastStartLoc:s}=n,a=o(8,r,s);return 12!==t.type?(b.UNEXPECTED_EMPTY_LINKED_MODIFIER,n.lastStartLoc,a.value="",c(a,r,s),{nextConsumeToken:t,node:a}):(null==t.value&&(b.UNEXPECTED_LEXICAL_ANALYSIS,n.lastStartLoc,G(t)),a.value=t.value||"",c(a,e.currentOffset(),e.currentPosition()),{node:a})}(e);n.modifier=t.node,r=t.nextConsumeToken||e.nextToken()}switch(10!==r.type&&(b.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),r=e.nextToken(),2===r.type&&(r=e.nextToken()),r.type){case 11:null==r.value&&(b.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),n.key=function(e,t){const n=e.context(),r=o(7,n.offset,n.startLoc);return r.value=t,c(r,e.currentOffset(),e.currentPosition()),r}(e,r.value||"");break;case 5:null==r.value&&(b.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),n.key=l(e,r.value||"");break;case 6:null==r.value&&(b.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),n.key=a(e,r.value||"");break;case 7:null==r.value&&(b.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),n.key=u(e,r.value||"");break;default:{b.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc;const s=e.context(),a=o(7,s.offset,s.startLoc);return a.value="",c(a,s.offset,s.startLoc),n.key=a,c(n,s.offset,s.startLoc),{nextConsumeToken:r,node:n}}}return c(n,e.currentOffset(),e.currentPosition()),{node:n}}function E(e){const t=e.context(),n=o(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let r=null,i=null;do{const o=r||e.nextToken();switch(r=null,o.type){case 0:null==o.value&&(b.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(o)),n.items.push(s(e,o.value||""));break;case 6:null==o.value&&(b.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(o)),n.items.push(a(e,o.value||""));break;case 4:i=!0;break;case 5:null==o.value&&(b.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(o)),n.items.push(l(e,o.value||"",!!i)),i&&(y.USE_MODULO_SYNTAX,t.lastStartLoc,G(o),i=null);break;case 7:null==o.value&&(b.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(o)),n.items.push(u(e,o.value||""));break;case 8:{const t=f(e);n.items.push(t.node),r=t.nextConsumeToken||null;break}}}while(14!==t.currentType&&1!==t.currentType);return c(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function _(e){const t=e.context(),{offset:n,startLoc:r}=t,s=E(e);return 14===t.currentType?s:function(e,t,n,r){const s=e.context();let a=0===r.items.length;const l=o(1,t,n);l.cases=[],l.cases.push(r);do{const t=E(e);a||(a=0===t.items.length),l.cases.push(t)}while(14!==s.currentType);return a&&b.MUST_HAVE_MESSAGES_IN_PLURAL,c(l,e.currentOffset(),e.currentPosition()),l}(e,n,r,s)}return{parse:function(n){const r=X(n,i({},e)),s=r.context(),a=o(0,s.offset,s.startLoc);return t&&a.loc&&(a.loc.source=n),a.body=_(r),e.onCacheKey&&(a.cacheKey=e.onCacheKey(n)),14!==s.currentType&&(b.UNEXPECTED_LEXICAL_ANALYSIS,s.lastStartLoc,n[s.offset]),c(a,r.currentOffset(),r.currentPosition()),a}}}function G(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function V(e,t){for(let n=0;n<e.length;n++)H(e[n],t)}function H(e,t){switch(e.type){case 1:V(e.cases,t),t.helper("plural");break;case 2:V(e.items,t);break;case 6:H(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function B(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&H(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function j(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=g(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function z(e){switch(e.t=e.type,e.type){case 0:{const t=e;z(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)z(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)z(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;z(t.key),t.k=t.key,delete t.key,t.modifier&&(z(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function Q(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?Q(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const o=t.cases.length;for(let n=0;n<o&&(Q(e,t.cases[n]),n!==o-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const o=t.items.length;for(let c=0;c<o&&(Q(e,t.items[c]),c!==o-1);c++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),Q(e,t.key),t.modifier?(e.push(", "),Q(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}const J=(e,t={})=>{const n=T(t.mode)?t.mode:"normal",r=T(t.filename)?t.filename:"message.intl",o=!!t.sourceMap,c=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",s=t.needIndent?t.needIndent:"arrow"!==n,a=e.helpers||[],l=function(e,t){const{sourceMap:n,filename:r,breakLineCode:o,needIndent:c}=t,s=!1!==t.location,a={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:o,needIndent:c,indentLevel:0};function l(e,t){a.code+=e}function i(e,t=!0){const n=t?o:"";l(c?n+"  ".repeat(e):n)}return s&&e.loc&&(a.source=e.loc.source),{context:()=>a,push:l,indent:function(e=!0){const t=++a.indentLevel;e&&i(t)},deindent:function(e=!0){const t=--a.indentLevel;e&&i(t)},newline:function(){i(a.indentLevel)},helper:e=>`_${e}`,needIndent:()=>a.needIndent}}(e,{mode:n,filename:r,sourceMap:o,breakLineCode:c,needIndent:s});l.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),l.indent(s),a.length>0&&(l.push(`const { ${g(a.map((e=>`${e}: _${e}`)),", ")} } = ctx`),l.newline()),l.push("return "),Q(l,e),l.deindent(s),l.push("}"),delete e.helpers;const{code:i,map:u}=l.context();return{ast:e,code:i,map:u?u.toJSON():void 0}};function Z(e,t={}){const n=i({},t),r=!!n.jit,o=!!n.minify,c=null==n.optimize||n.optimize,s=K(n).parse(e);return r?(c&&function(e){const t=e.body;2===t.type?j(t):t.cases.forEach((e=>j(e)))}(s),o&&z(s),{ast:s,code:""}):(B(s,n),J(s,n))}function q(e){return A(e)&&0===ce(e)&&(p(e,"b")||p(e,"body"))}const ee=["b","body"];const te=["c","cases"];const ne=["s","static"];const re=["i","items"];const oe=["t","type"];function ce(e){return ue(e,oe)}const se=["v","value"];function ae(e,t){const n=ue(e,se);if(null!=n)return n;throw Ee(t)}const le=["m","modifier"];const ie=["k","key"];function ue(e,t,n){for(let r=0;r<t.length;r++){const n=t[r];if(p(e,n)&&null!=e[n])return e[n]}return n}const fe=[...ee,...te,...ne,...re,...ie,...le,...se,...oe];function Ee(e){return new Error(`unhandled node type: ${e}`)}const _e=[];_e[0]={w:[0],i:[3,0],"[":[4],o:[7]},_e[1]={w:[1],".":[2],"[":[4],o:[7]},_e[2]={w:[2],i:[3,0],0:[3,0]},_e[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},_e[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},_e[5]={"'":[4,0],o:8,l:[5,0]},_e[6]={'"':[4,0],o:8,l:[6,0]};const me=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function pe(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function de(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,me.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}function Ne(e){const t=[];let n,r,o,c,s,a,l,i=-1,u=0,f=0;const E=[];function _(){const t=e[i+1];if(5===u&&"'"===t||6===u&&'"'===t)return i++,o="\\"+t,E[0](),!0}for(E[0]=()=>{void 0===r?r=o:r+=o},E[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},E[2]=()=>{E[0](),f++},E[3]=()=>{if(f>0)f--,u=4,E[0]();else{if(f=0,void 0===r)return!1;if(r=de(r),!1===r)return!1;E[1]()}};null!==u;)if(i++,n=e[i],"\\"!==n||!_()){if(c=pe(n),l=_e[u],s=l[c]||l.l||8,8===s)return;if(u=s[0],void 0!==s[1]&&(a=E[s[1]],a&&(o=n,!1===a())))return;if(7===u)return t}}const Te=new Map;function Le(e,t){return A(e)?e[t]:null}const Ae=e=>e,Ce=e=>"",he="text",ke=e=>0===e.length?"":g(e),Oe=e=>null==e?"":d(e)||O(e)&&e.toString===h?JSON.stringify(e,null,2):String(e);function ge(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function Ie(e={}){const t=e.locale,n=function(e){const t=c(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(c(e.named.count)||c(e.named.n))?c(e.named.count)?e.named.count:c(e.named.n)?e.named.n:t:t}(e),r=A(e.pluralRules)&&T(t)&&N(e.pluralRules[t])?e.pluralRules[t]:ge,o=A(e.pluralRules)&&T(t)&&N(e.pluralRules[t])?ge:void 0,s=e.list||[],a=e.named||f();c(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,a);function l(t){const n=N(e.messages)?e.messages(t):!!A(e.messages)&&e.messages[t];return n||(e.parent?e.parent.message(t):Ce)}const u=O(e.processor)&&N(e.processor.normalize)?e.processor.normalize:ke,E=O(e.processor)&&N(e.processor.interpolate)?e.processor.interpolate:Oe,_={list:e=>s[e],named:e=>a[e],plural:e=>e[r(n,e.length,o)],linked:(t,...n)=>{const[r,o]=n;let c="text",s="";1===n.length?A(r)?(s=r.modifier||s,c=r.type||c):T(r)&&(s=r||s):2===n.length&&(T(r)&&(s=r||s),T(o)&&(c=o||c));const a=l(t)(_),i="vnode"===c&&d(a)&&s?a[0]:a;return s?(u=s,e.modifiers?e.modifiers[u]:Ae)(i,c):i;var u},message:l,type:O(e.processor)&&T(e.processor.type)?e.processor.type:he,interpolate:E,normalize:u,values:i(f(),s,a)};return _}let Se=null;const ye=be("function:translate");function be(e){return t=>Se&&Se.emit(e,t)}const Pe=y.__EXTEND_POINT__,De=I(Pe),Me={NOT_FOUND_KEY:Pe,FALLBACK_TO_TRANSLATE:De(),CANNOT_FORMAT_NUMBER:De(),FALLBACK_TO_NUMBER_FORMAT:De(),CANNOT_FORMAT_DATE:De(),FALLBACK_TO_DATE_FORMAT:De(),EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:De(),__EXTEND_POINT__:De()},Re={[Me.NOT_FOUND_KEY]:"Not found '{key}' key in '{locale}' locale messages.",[Me.FALLBACK_TO_TRANSLATE]:"Fall back to translate '{key}' key with '{target}' locale.",[Me.CANNOT_FORMAT_NUMBER]:"Cannot format a number value due to not supported Intl.NumberFormat.",[Me.FALLBACK_TO_NUMBER_FORMAT]:"Fall back to number format '{key}' key with '{target}' locale.",[Me.CANNOT_FORMAT_DATE]:"Cannot format a date value due to not supported Intl.DateTimeFormat.",[Me.FALLBACK_TO_DATE_FORMAT]:"Fall back to datetime format '{key}' key with '{target}' locale.",[Me.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER]:"This project is using Custom Message Compiler, which is an experimental feature. It may receive breaking changes or be removed in the future."};const Ue=b.__EXTEND_POINT__,ve=I(Ue),xe={INVALID_ARGUMENT:Ue,INVALID_DATE_ARGUMENT:ve(),INVALID_ISO_DATE_ARGUMENT:ve(),NOT_SUPPORT_NON_STRING_MESSAGE:ve(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:ve(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:ve(),NOT_SUPPORT_LOCALE_TYPE:ve(),__EXTEND_POINT__:ve()};function Fe(e,t){return null!=t.locale?We(t.locale):We(e.locale)}let we;function We(e){if(T(e))return e;if(N(e)){if(e.resolvedOnce&&null!=we)return we;if("Function"===e.constructor.name){const t=e();if(C(t))throw Error(xe.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return we=t}throw Error(xe.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(xe.NOT_SUPPORT_LOCALE_TYPE)}function Xe(e,t,n){return[...new Set([n,...d(t)?t:A(t)?Object.keys(t):T(t)?[t]:[n]])]}function $e(e,t,n){let r=!0;for(let o=0;o<t.length&&L(r);o++){const c=t[o];T(c)&&(r=Ye(e,t[o],n))}return r}function Ye(e,t,n){let r;const o=t.split("-");do{r=Ke(e,o.join("-"),n),o.splice(-1,1)}while(o.length&&!0===r);return r}function Ke(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const o=t.replace(/!/g,"");e.push(o),(d(n)||O(n))&&n[o]&&(r=n[o])}return r}xe.INVALID_ARGUMENT,xe.INVALID_DATE_ARGUMENT,xe.INVALID_ISO_DATE_ARGUMENT,xe.NOT_SUPPORT_NON_STRING_MESSAGE,xe.NOT_SUPPORT_LOCALE_PROMISE_VALUE,xe.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION,xe.NOT_SUPPORT_LOCALE_TYPE;const Ge="9.14.5",Ve="en-US",He=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let Be,je,ze;let Qe=null;let Je=null;let Ze=0;const qe=e=>({[e]:f()});function et(e,t,n,r,o){const{missing:c,onWarn:s}=e;if(null!==c){const r=c(e,n,t,o);return T(r)?r:t}return t}function tt(e,t){return e!==t&&e.split("-")[0]===t.split("-")[0]}function nt(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let r=n+1;r<t.length;r++)if(tt(e,t[r]))return!0;return!1}function rt(e){return t=>function(e,t){const n=(r=t,ue(r,ee));var r;if(null==n)throw Ee(0);if(1===ce(n)){const t=function(e){return ue(e,te,[])}(n);return e.plural(t.reduce(((t,n)=>[...t,ot(e,n)]),[]))}return ot(e,n)}(t,e)}function ot(e,t){const n=function(e){return ue(e,ne)}(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=function(e){return ue(e,re,[])}(t).reduce(((t,n)=>[...t,ct(e,n)]),[]);return e.normalize(n)}}function ct(e,t){const n=ce(t);switch(n){case 3:case 9:case 7:case 8:return ae(t,n);case 4:{const r=t;if(p(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(p(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw Ee(n)}case 5:{const r=t;if(p(r,"i")&&c(r.i))return e.interpolate(e.list(r.i));if(p(r,"index")&&c(r.index))return e.interpolate(e.list(r.index));throw Ee(n)}case 6:{const n=t,r=function(e){return ue(e,le)}(n),o=function(e){const t=ue(e,ie);if(t)return t;throw Ee(6)}(n);return e.linked(ct(e,o),r?ct(e,r):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}const st=e=>e;let at=f();function lt(e,t={}){let n=!1;const r=t.onError||D;return t.onError=e=>{n=!0,r(e)},{...Z(e,t),detectError:n}}const it=()=>"",ut=e=>N(e);function ft(e,t,n,r,o,c){const{messages:s,onWarn:a,messageResolver:l,localeFallbacker:i}=e,u=i(e,r,n);let E,_=f(),m=null;for(let p=0;p<u.length&&(E=u[p],_=s[E]||f(),null===(m=l(_,t))&&(m=_[t]),!(T(m)||q(m)||ut(m)));p++)if(!nt(E,u)){const n=et(e,t,E,0,"translate");n!==t&&(m=n)}return[m,E,_]}function Et(e,t,n,o,c,s){const{messageCompiler:a,warnHtmlMessage:l}=e;if(ut(o)){const e=o;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==a){const e=()=>o;return e.locale=n,e.key=t,e}const i=a(o,function(e,t,n,o,c,s){return{locale:t,key:n,warnHtmlMessage:c,onError:e=>{throw s&&s(e),e},onCacheKey:e=>r(t,n,e)}}(0,n,c,0,l,s));return i.locale=n,i.key=t,i.source=o,i}function _t(...e){const[t,n,r]=e,o=f();if(!(T(t)||c(t)||ut(t)||q(t)))throw Error(xe.INVALID_ARGUMENT);const s=c(t)?String(t):(ut(t),t);return c(n)?o.plural=n:T(n)?o.default=n:O(n)&&!l(n)?o.named=n:d(n)&&(o.list=n),c(r)?o.plural=r:T(r)?o.default=r:O(r)&&i(o,r),[s,o]}const mt=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function pt(...e){const[t,n,r,o]=e,a=f();let l,i=f();if(T(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(xe.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();l=new Date(n);try{l.toISOString()}catch(u){throw Error(xe.INVALID_ISO_DATE_ARGUMENT)}}else if(s(t)){if(isNaN(t.getTime()))throw Error(xe.INVALID_DATE_ARGUMENT);l=t}else{if(!c(t))throw Error(xe.INVALID_ARGUMENT);l=t}return T(n)?a.key=n:O(n)&&Object.keys(n).forEach((e=>{mt.includes(e)?i[e]=n[e]:a[e]=n[e]})),T(r)?a.locale=r:O(r)&&(i=r),O(o)&&(i=o),[a.key||"",l,a,i]}const dt=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Nt(...e){const[t,n,r,o]=e,s=f();let a=f();if(!c(t))throw Error(xe.INVALID_ARGUMENT);const l=t;return T(n)?s.key=n:O(n)&&Object.keys(n).forEach((e=>{dt.includes(e)?a[e]=n[e]:s[e]=n[e]})),T(r)?s.locale=r:O(r)&&(a=r),O(o)&&(a=o),[s.key||"",l,s,a]}return e.AST_NODE_PROPS_KEYS=fe,e.CompileErrorCodes=b,e.CoreErrorCodes=xe,e.CoreWarnCodes=Me,e.DATETIME_FORMAT_OPTIONS_KEYS=mt,e.DEFAULT_LOCALE=Ve,e.DEFAULT_MESSAGE_DATA_TYPE=he,e.MISSING_RESOLVE_VALUE="",e.NOT_REOSLVED=-1,e.NUMBER_FORMAT_OPTIONS_KEYS=dt,e.VERSION=Ge,e.clearCompileCache=function(){at=f()},e.clearDateTimeFormat=function(e,t,n){const r=e;for(const o in n){const e=`${t}__${o}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}},e.clearNumberFormat=function(e,t,n){const r=e;for(const o in n){const e=`${t}__${o}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}},e.compile=function(e,t){if(T(e)){!L(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||st)(e),r=at[n];if(r)return r;const{ast:o,detectError:c}=lt(e,{...t,location:!1,jit:!0}),s=rt(o);return c?s:at[n]=s}{const t=e.cacheKey;if(t){const n=at[t];return n||(at[t]=rt(e))}return rt(e)}},e.compileToFunction=(e,t)=>{if(!T(e))throw Error(xe.NOT_SUPPORT_NON_STRING_MESSAGE);{!L(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||st)(e),r=at[n];if(r)return r;const{code:o,detectError:c}=lt(e,t),s=new Function(`return ${o}`)();return c?s:at[n]=s}},e.createCompileError=P,e.createCoreContext=function(e={}){const n=N(e.onWarn)?e.onWarn:t,r=T(e.version)?e.version:Ge,o=T(e.locale)||N(e.locale)?e.locale:Ve,c=N(o)?Ve:o,s=d(e.fallbackLocale)||O(e.fallbackLocale)||T(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:c,l=O(e.messages)?e.messages:qe(c),u=O(e.datetimeFormats)?e.datetimeFormats:qe(c),E=O(e.numberFormats)?e.numberFormats:qe(c),_=i(f(),e.modifiers,{upper:(e,t)=>"text"===t&&T(e)?e.toUpperCase():"vnode"===t&&A(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&T(e)?e.toLowerCase():"vnode"===t&&A(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&T(e)?He(e):"vnode"===t&&A(e)&&"__v_isVNode"in e?He(e.children):e}),m=e.pluralRules||f(),p=N(e.missing)?e.missing:null,C=!L(e.missingWarn)&&!a(e.missingWarn)||e.missingWarn,h=!L(e.fallbackWarn)&&!a(e.fallbackWarn)||e.fallbackWarn,k=!!e.fallbackFormat,g=!!e.unresolving,I=N(e.postTranslation)?e.postTranslation:null,S=O(e.processor)?e.processor:null,y=!L(e.warnHtmlMessage)||e.warnHtmlMessage,b=!!e.escapeParameter,P=N(e.messageCompiler)?e.messageCompiler:Be,D=N(e.messageResolver)?e.messageResolver:je||Le,M=N(e.localeFallbacker)?e.localeFallbacker:ze||Xe,R=A(e.fallbackContext)?e.fallbackContext:void 0,U=e,v=A(U.__datetimeFormatters)?U.__datetimeFormatters:new Map,x=A(U.__numberFormatters)?U.__numberFormatters:new Map,F=A(U.__meta)?U.__meta:{};Ze++;const w={version:r,cid:Ze,locale:o,fallbackLocale:s,messages:l,modifiers:_,pluralRules:m,missing:p,missingWarn:C,fallbackWarn:h,fallbackFormat:k,unresolving:g,postTranslation:I,processor:S,warnHtmlMessage:y,escapeParameter:b,messageCompiler:P,messageResolver:D,localeFallbacker:M,fallbackContext:R,onWarn:n,__meta:F};return w.datetimeFormats=u,w.numberFormats=E,w.__datetimeFormatters=v,w.__numberFormatters=x,w},e.createCoreError=function(e){return P(e,null,void 0)},e.createMessageContext=Ie,e.datetime=function(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:o,onWarn:c,localeFallbacker:s}=e,{__datetimeFormatters:a}=e,[u,f,E,_]=pt(...t);L(E.missingWarn)?E.missingWarn:e.missingWarn,L(E.fallbackWarn)?E.fallbackWarn:e.fallbackWarn;const m=!!E.part,p=Fe(e,E),d=s(e,o,p);if(!T(u)||""===u)return new Intl.DateTimeFormat(p,_).format(f);let N,A={},C=null;for(let l=0;l<d.length&&(N=d[l],A=n[N]||{},C=A[u],!O(C));l++)et(e,u,N,0,"datetime format");if(!O(C)||!T(N))return r?-1:u;let h=`${N}__${u}`;l(_)||(h=`${h}__${JSON.stringify(_)}`);let k=a.get(h);return k||(k=new Intl.DateTimeFormat(N,i({},C,_)),a.set(h,k)),m?k.formatToParts(f):k.format(f)},e.fallbackWithLocaleChain=function(e,t,n){const r=T(n)?n:Ve,o=e;o.__localeChainCache||(o.__localeChainCache=new Map);let c=o.__localeChainCache.get(r);if(!c){c=[];let e=[n];for(;d(e);)e=$e(c,e,t);const s=d(t)||!O(t)?t:t.default?t.default:null;e=T(s)?[s]:s,d(e)&&$e(c,e,!1),o.__localeChainCache.set(r,c)}return c},e.fallbackWithSimple=Xe,e.getAdditionalMeta=()=>Qe,e.getDevToolsHook=function(){return Se},e.getFallbackContext=()=>Je,e.getLocale=Fe,e.getWarnMessage=function(e,...t){return function(e,...t){return 1===t.length&&A(t[0])&&(t=t[0]),t&&t.hasOwnProperty||(t={}),e.replace(n,((e,n)=>t.hasOwnProperty(n)?t[n]:""))}(Re[e],...t)},e.handleMissing=et,e.initI18nDevTools=function(e,t,n){Se&&Se.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})},e.isAlmostSameLocale=tt,e.isImplicitFallback=nt,e.isMessageAST=q,e.isMessageFunction=ut,e.isTranslateFallbackWarn=function(e,t){return e instanceof RegExp?e.test(t):e},e.isTranslateMissingWarn=function(e,t){return e instanceof RegExp?e.test(t):e},e.number=function(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:o,onWarn:c,localeFallbacker:s}=e,{__numberFormatters:a}=e,[u,f,E,_]=Nt(...t);L(E.missingWarn)?E.missingWarn:e.missingWarn,L(E.fallbackWarn)?E.fallbackWarn:e.fallbackWarn;const m=!!E.part,p=Fe(e,E),d=s(e,o,p);if(!T(u)||""===u)return new Intl.NumberFormat(p,_).format(f);let N,A={},C=null;for(let l=0;l<d.length&&(N=d[l],A=n[N]||{},C=A[u],!O(C));l++)et(e,u,N,0,"number format");if(!O(C)||!T(N))return r?-1:u;let h=`${N}__${u}`;l(_)||(h=`${h}__${JSON.stringify(_)}`);let k=a.get(h);return k||(k=new Intl.NumberFormat(N,i({},C,_)),a.set(h,k)),m?k.formatToParts(f):k.format(f)},e.parse=Ne,e.parseDateTimeArgs=pt,e.parseNumberArgs=Nt,e.parseTranslateArgs=_t,e.registerLocaleFallbacker=function(e){ze=e},e.registerMessageCompiler=function(e){Be=e},e.registerMessageResolver=function(e){je=e},e.resolveLocale=We,e.resolveValue=function(e,t){if(!A(e))return null;let n=Te.get(t);if(n||(n=Ne(t),n&&Te.set(t,n)),!n)return null;const r=n.length;let o=e,c=0;for(;c<r;){const e=n[c];if(fe.includes(e)&&q(o))return null;const t=o[e];if(void 0===t)return null;if(N(o))return null;o=t,c++}return o},e.resolveWithKeyValue=Le,e.setAdditionalMeta=e=>{Qe=e},e.setDevToolsHook=function(e){Se=e},e.setFallbackContext=e=>{Je=e},e.translate=function(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:o,messageCompiler:s,fallbackLocale:a,messages:l}=e,[i,u]=_t(...t),m=L(u.missingWarn)?u.missingWarn:e.missingWarn,p=L(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,N=L(u.escapeParameter)?u.escapeParameter:e.escapeParameter,C=!!u.resolvedMessage,h=T(u.default)||L(u.default)?L(u.default)?s?i:()=>i:u.default:n?s?i:()=>i:"",k=n||""!==h,O=Fe(e,u);N&&function(e){d(e.list)?e.list=e.list.map((e=>T(e)?E(e):e)):A(e.named)&&Object.keys(e.named).forEach((t=>{T(e.named[t])&&(e.named[t]=E(e.named[t]))}))}(u);let[g,I,S]=C?[i,O,l[O]||f()]:ft(e,i,O,a,p,m),y=g,b=i;if(C||T(y)||q(y)||ut(y)||k&&(y=h,b=y),!(C||(T(y)||q(y)||ut(y))&&T(I)))return o?-1:i;let P=!1;const D=ut(y)?y:Et(e,i,I,y,b,(()=>{P=!0}));if(P)return y;const M=function(e,t,n,r){const{modifiers:o,pluralRules:s,messageResolver:a,fallbackLocale:l,fallbackWarn:i,missingWarn:u,fallbackContext:f}=e,E=r=>{let o=a(n,r);if(null==o&&f){const[,,e]=ft(f,r,t,l,i,u);o=a(e,r)}if(T(o)||q(o)){let n=!1;const c=Et(e,r,t,o,r,(()=>{n=!0}));return n?it:c}return ut(o)?o:it},_={locale:t,modifiers:o,pluralRules:s,messages:E};e.processor&&(_.processor=e.processor);r.list&&(_.list=r.list);r.named&&(_.named=r.named);c(r.plural)&&(_.pluralIndex=r.plural);return _}(e,I,S,u),R=function(e,t,n){const r=t(n);return r}(0,D,Ie(M));let U=r?r(R,i):R;var v;return N&&T(U)&&(v=(v=(v=U).replace(/(\w+)\s*=\s*"([^"]*)"/g,((e,t,n)=>`${t}="${_(n)}"`))).replace(/(\w+)\s*=\s*'([^']*)'/g,((e,t,n)=>`${t}='${_(n)}'`)),/\s*on\w+\s*=\s*["']?[^"'>]+["']?/gi.test(v)&&(v=v.replace(/(\s+)(on)(\w+\s*=)/gi,"$1&#111;n$3")),[/(\s+(?:href|src|action|formaction)\s*=\s*["']?)\s*javascript:/gi,/(style\s*=\s*["'][^"']*url\s*\(\s*)javascript:/gi].forEach((e=>{v=v.replace(e,"$1javascript&#58;")})),U=v),U},e.translateDevTools=ye,e.updateFallbackLocale=function(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)},e}({});
