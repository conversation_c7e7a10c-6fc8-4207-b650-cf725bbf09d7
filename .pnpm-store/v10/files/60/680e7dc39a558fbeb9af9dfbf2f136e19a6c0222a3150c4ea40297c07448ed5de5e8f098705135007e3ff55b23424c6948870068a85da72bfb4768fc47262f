{"name": "@jridgewell/source-map", "version": "0.3.10", "description": "Packages @jridgewell/trace-mapping and @jridgewell/gen-mapping into the familiar source-map API", "keywords": ["sourcemap", "source", "map"], "main": "dist/source-map.umd.js", "module": "dist/source-map.mjs", "types": "types/source-map.d.cts", "files": ["dist", "src", "types"], "exports": {".": [{"import": {"types": "./types/source-map.d.mts", "default": "./dist/source-map.mjs"}, "require": {"types": "./types/source-map.d.cts", "default": "./dist/source-map.umd.js"}, "browser": {"types": "./types/source-map.d.cts", "default": "./dist/source-map.umd.js"}}, "./dist/source-map.umd.js"], "./package.json": "./package.json"}, "scripts": {"benchmark": "run-s build:code benchmark:*", "benchmark:install": "cd benchmark && npm install", "benchmark:only": "node --expose-gc benchmark/index.js", "build": "run-s -n build:code build:types", "build:code": "node ../../esbuild.mjs source-map.ts", "build:types": "run-s build:types:force build:types:emit build:types:mts", "build:types:force": "rimraf tsconfig.build.tsbuildinfo", "build:types:emit": "tsc --project tsconfig.build.json", "build:types:mts": "node ../../mts-types.mjs", "clean": "run-s -n clean:code clean:types", "clean:code": "tsc --build --clean tsconfig.build.json", "clean:types": "rimraf dist types", "test": "run-s -n test:types test:only test:format", "test:format": "prettier --check '{src,test}/**/*.ts'", "test:only": "mocha", "test:types": "eslint '{src,test}/**/*.ts'", "lint": "run-s -n lint:types lint:format", "lint:format": "npm run test:format -- --write", "lint:types": "npm run test:types -- --fix", "prepublishOnly": "npm run-s -n build test"}, "homepage": "https://github.com/jridgewell/sourcemaps/tree/main/packages/source-map", "repository": {"type": "git", "url": "git+https://github.com/jridgewell/sourcemaps.git", "directory": "packages/source-map"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}