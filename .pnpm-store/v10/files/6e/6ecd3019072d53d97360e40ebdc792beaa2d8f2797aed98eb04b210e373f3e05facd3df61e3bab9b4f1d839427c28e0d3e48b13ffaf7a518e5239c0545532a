{"version": 3, "file": "utils.cjs.production.min.js", "sources": ["../src/types/message.ts", "../node_modules/regenerator-runtime/runtime.js", "../src/helpers.ts", "../src/url.ts", "../src/math.ts", "../src/canned.ts", "../src/sla.ts", "../src/date.ts", "../src/typingStatus.ts", "../src/debounce.ts", "../src/email.ts", "../src/string.ts"], "sourcesContent": ["export type EmailAttributes = {\n  bcc: string[] | null;\n  cc: string[] | null;\n  content_type: string;\n  date: string;\n  from: string[] | null;\n  html_content: {\n    full: string;\n    reply: string;\n    quoted: string;\n  };\n  in_reply_to: null;\n  message_id: string;\n  multipart: boolean;\n  number_of_attachments: number;\n  subject: string;\n  text_content: {\n    full: string;\n    reply: string;\n    quoted: string;\n  };\n  to: string[] | null;\n};\n\nexport type IncomingContentAttribute = {\n  email: EmailAttributes | null;\n};\n\nexport type OutgoingContentAttribute = {\n  cc_emails: string[] | null;\n  bcc_emails: string[] | null;\n  to_emails: string[] | null;\n  external_error: string;\n};\n\nexport type MessageContentAttributes =\n  | IncomingContentAttribute\n  | OutgoingContentAttribute;\n\nexport type MessageConversation = {\n  id: number;\n  assignee_id: number;\n  custom_attributes: Record<string, any>;\n  first_reply_created_at: number;\n  waiting_since: number;\n  status: string;\n  unread_count: number;\n  last_activity_at: number;\n  contact_inbox: { source_id: string };\n};\n\nexport type MessageAttachment = {\n  id: number;\n  message_id: number;\n  file_type: string;\n  account_id: number;\n  extension: null;\n  data_url: string;\n  thumb_url: string;\n  file_size: number;\n  width: null;\n  height: null;\n};\n\nexport type MessageSender = {\n  custom_attributes: {};\n  email: null;\n  id: number;\n  identifier: null;\n  name: string;\n  phone_number: null;\n  thumbnail: string;\n  type: string;\n};\n\nexport enum MessageType {\n  INCOMING = 0,\n  OUTGOING = 1,\n  ACTIVITY = 2,\n  TEMPLATE = 3,\n}\n\nexport type BaseEmailMessage = {\n  id: number;\n  content: null;\n  account_id: number;\n  inbox_id: number;\n  conversation_id: number;\n  message_type: MessageType;\n  created_at: number;\n  updated_at: string;\n  private: boolean;\n  status: string;\n  source_id: null;\n  content_type: string;\n  content_attributes: MessageContentAttributes;\n  sender_type: string;\n  sender_id: number;\n  external_source_ids: {};\n  additional_attributes: {};\n  processed_message_content: null;\n  sentiment: {};\n  conversation: MessageConversation;\n  attachments: MessageAttachment[];\n  sender: MessageSender;\n};\n\nexport type IncomingEmailMessage = BaseEmailMessage & {\n  message_type: MessageType.INCOMING;\n  content_attributes: IncomingContentAttribute;\n};\n\nexport type OutgoingEmailMessage = BaseEmailMessage & {\n  message_type: MessageType.OUTGOING;\n  content_attributes: OutgoingContentAttribute;\n};\n\nexport type EmailMessage = IncomingEmailMessage | OutgoingEmailMessage;\n", "/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar runtime = (function (exports) {\n  \"use strict\";\n\n  var Op = Object.prototype;\n  var hasOwn = Op.hasOwnProperty;\n  var undefined; // More compressible than void 0.\n  var $Symbol = typeof Symbol === \"function\" ? Symbol : {};\n  var iteratorSymbol = $Symbol.iterator || \"@@iterator\";\n  var asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\";\n  var toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n\n  function define(obj, key, value) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n    return obj[key];\n  }\n  try {\n    // IE 8 has a broken Object.defineProperty that only works on DOM objects.\n    define({}, \"\");\n  } catch (err) {\n    define = function(obj, key, value) {\n      return obj[key] = value;\n    };\n  }\n\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;\n    var generator = Object.create(protoGenerator.prototype);\n    var context = new Context(tryLocsList || []);\n\n    // The ._invoke method unifies the implementations of the .next,\n    // .throw, and .return methods.\n    generator._invoke = makeInvokeMethod(innerFn, self, context);\n\n    return generator;\n  }\n  exports.wrap = wrap;\n\n  // Try/catch helper to minimize deoptimizations. Returns a completion\n  // record like context.tryEntries[i].completion. This interface could\n  // have been (and was previously) designed to take a closure to be\n  // invoked without arguments, but in all the cases we care about we\n  // already have an existing method we want to call, so there's no need\n  // to create a new function object. We can even get away with assuming\n  // the method takes exactly one argument, since that happens to be true\n  // in every case, so we don't have to touch the arguments object. The\n  // only additional allocation required is the completion record, which\n  // has a stable shape and so hopefully should be cheap to allocate.\n  function tryCatch(fn, obj, arg) {\n    try {\n      return { type: \"normal\", arg: fn.call(obj, arg) };\n    } catch (err) {\n      return { type: \"throw\", arg: err };\n    }\n  }\n\n  var GenStateSuspendedStart = \"suspendedStart\";\n  var GenStateSuspendedYield = \"suspendedYield\";\n  var GenStateExecuting = \"executing\";\n  var GenStateCompleted = \"completed\";\n\n  // Returning this object from the innerFn has the same effect as\n  // breaking out of the dispatch switch statement.\n  var ContinueSentinel = {};\n\n  // Dummy constructor functions that we use as the .constructor and\n  // .constructor.prototype properties for functions that return Generator\n  // objects. For full spec compliance, you may wish to configure your\n  // minifier not to mangle the names of these two functions.\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n\n  // This is a polyfill for %IteratorPrototype% for environments that\n  // don't natively support it.\n  var IteratorPrototype = {};\n  IteratorPrototype[iteratorSymbol] = function () {\n    return this;\n  };\n\n  var getProto = Object.getPrototypeOf;\n  var NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  if (NativeIteratorPrototype &&\n      NativeIteratorPrototype !== Op &&\n      hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {\n    // This environment has a native %IteratorPrototype%; use it instead\n    // of the polyfill.\n    IteratorPrototype = NativeIteratorPrototype;\n  }\n\n  var Gp = GeneratorFunctionPrototype.prototype =\n    Generator.prototype = Object.create(IteratorPrototype);\n  GeneratorFunction.prototype = Gp.constructor = GeneratorFunctionPrototype;\n  GeneratorFunctionPrototype.constructor = GeneratorFunction;\n  GeneratorFunction.displayName = define(\n    GeneratorFunctionPrototype,\n    toStringTagSymbol,\n    \"GeneratorFunction\"\n  );\n\n  // Helper for defining the .next, .throw, and .return methods of the\n  // Iterator interface in terms of a single ._invoke method.\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      define(prototype, method, function(arg) {\n        return this._invoke(method, arg);\n      });\n    });\n  }\n\n  exports.isGeneratorFunction = function(genFun) {\n    var ctor = typeof genFun === \"function\" && genFun.constructor;\n    return ctor\n      ? ctor === GeneratorFunction ||\n        // For the native GeneratorFunction constructor, the best we can\n        // do is to check its .name property.\n        (ctor.displayName || ctor.name) === \"GeneratorFunction\"\n      : false;\n  };\n\n  exports.mark = function(genFun) {\n    if (Object.setPrototypeOf) {\n      Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);\n    } else {\n      genFun.__proto__ = GeneratorFunctionPrototype;\n      define(genFun, toStringTagSymbol, \"GeneratorFunction\");\n    }\n    genFun.prototype = Object.create(Gp);\n    return genFun;\n  };\n\n  // Within the body of any async function, `await x` is transformed to\n  // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test\n  // `hasOwn.call(value, \"__await\")` to determine if the yielded value is\n  // meant to be awaited.\n  exports.awrap = function(arg) {\n    return { __await: arg };\n  };\n\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (record.type === \"throw\") {\n        reject(record.arg);\n      } else {\n        var result = record.arg;\n        var value = result.value;\n        if (value &&\n            typeof value === \"object\" &&\n            hasOwn.call(value, \"__await\")) {\n          return PromiseImpl.resolve(value.__await).then(function(value) {\n            invoke(\"next\", value, resolve, reject);\n          }, function(err) {\n            invoke(\"throw\", err, resolve, reject);\n          });\n        }\n\n        return PromiseImpl.resolve(value).then(function(unwrapped) {\n          // When a yielded Promise is resolved, its final value becomes\n          // the .value of the Promise<{value,done}> result for the\n          // current iteration.\n          result.value = unwrapped;\n          resolve(result);\n        }, function(error) {\n          // If a rejected Promise was yielded, throw the rejection back\n          // into the async generator function so it can be handled there.\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n    }\n\n    var previousPromise;\n\n    function enqueue(method, arg) {\n      function callInvokeWithMethodAndArg() {\n        return new PromiseImpl(function(resolve, reject) {\n          invoke(method, arg, resolve, reject);\n        });\n      }\n\n      return previousPromise =\n        // If enqueue has been called before, then we want to wait until\n        // all previous Promises have been resolved before calling invoke,\n        // so that results are always delivered in the correct order. If\n        // enqueue has not been called before, then it is important to\n        // call invoke immediately, without waiting on a callback to fire,\n        // so that the async generator function has the opportunity to do\n        // any necessary setup in a predictable way. This predictability\n        // is why the Promise constructor synchronously invokes its\n        // executor callback, and why async functions synchronously\n        // execute code before the first await. Since we implement simple\n        // async functions in terms of async generators, it is especially\n        // important to get this right, even though it requires care.\n        previousPromise ? previousPromise.then(\n          callInvokeWithMethodAndArg,\n          // Avoid propagating failures to Promises returned by later\n          // invocations of the iterator.\n          callInvokeWithMethodAndArg\n        ) : callInvokeWithMethodAndArg();\n    }\n\n    // Define the unified helper method that is used to implement .next,\n    // .throw, and .return (see defineIteratorMethods).\n    this._invoke = enqueue;\n  }\n\n  defineIteratorMethods(AsyncIterator.prototype);\n  AsyncIterator.prototype[asyncIteratorSymbol] = function () {\n    return this;\n  };\n  exports.AsyncIterator = AsyncIterator;\n\n  // Note that simple async functions are implemented on top of\n  // AsyncIterator objects; they just return a Promise for the value of\n  // the final result produced by the iterator.\n  exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    if (PromiseImpl === void 0) PromiseImpl = Promise;\n\n    var iter = new AsyncIterator(\n      wrap(innerFn, outerFn, self, tryLocsList),\n      PromiseImpl\n    );\n\n    return exports.isGeneratorFunction(outerFn)\n      ? iter // If outerFn is a generator, return the full iterator.\n      : iter.next().then(function(result) {\n          return result.done ? result.value : iter.next();\n        });\n  };\n\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = GenStateSuspendedStart;\n\n    return function invoke(method, arg) {\n      if (state === GenStateExecuting) {\n        throw new Error(\"Generator is already running\");\n      }\n\n      if (state === GenStateCompleted) {\n        if (method === \"throw\") {\n          throw arg;\n        }\n\n        // Be forgiving, per 25.3.3.3.3 of the spec:\n        // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume\n        return doneResult();\n      }\n\n      context.method = method;\n      context.arg = arg;\n\n      while (true) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n\n        if (context.method === \"next\") {\n          // Setting context._sent for legacy support of Babel's\n          // function.sent implementation.\n          context.sent = context._sent = context.arg;\n\n        } else if (context.method === \"throw\") {\n          if (state === GenStateSuspendedStart) {\n            state = GenStateCompleted;\n            throw context.arg;\n          }\n\n          context.dispatchException(context.arg);\n\n        } else if (context.method === \"return\") {\n          context.abrupt(\"return\", context.arg);\n        }\n\n        state = GenStateExecuting;\n\n        var record = tryCatch(innerFn, self, context);\n        if (record.type === \"normal\") {\n          // If an exception is thrown from innerFn, we leave state ===\n          // GenStateExecuting and loop back for another invocation.\n          state = context.done\n            ? GenStateCompleted\n            : GenStateSuspendedYield;\n\n          if (record.arg === ContinueSentinel) {\n            continue;\n          }\n\n          return {\n            value: record.arg,\n            done: context.done\n          };\n\n        } else if (record.type === \"throw\") {\n          state = GenStateCompleted;\n          // Dispatch the exception by looping back around to the\n          // context.dispatchException(context.arg) call above.\n          context.method = \"throw\";\n          context.arg = record.arg;\n        }\n      }\n    };\n  }\n\n  // Call delegate.iterator[context.method](context.arg) and handle the\n  // result, either by returning a { value, done } result from the\n  // delegate iterator, or by modifying context.method and context.arg,\n  // setting context.delegate to null, and returning the ContinueSentinel.\n  function maybeInvokeDelegate(delegate, context) {\n    var method = delegate.iterator[context.method];\n    if (method === undefined) {\n      // A .throw or .return when the delegate iterator has no .throw\n      // method always terminates the yield* loop.\n      context.delegate = null;\n\n      if (context.method === \"throw\") {\n        // Note: [\"return\"] must be used for ES3 parsing compatibility.\n        if (delegate.iterator[\"return\"]) {\n          // If the delegate iterator has a return method, give it a\n          // chance to clean up.\n          context.method = \"return\";\n          context.arg = undefined;\n          maybeInvokeDelegate(delegate, context);\n\n          if (context.method === \"throw\") {\n            // If maybeInvokeDelegate(context) changed context.method from\n            // \"return\" to \"throw\", let that override the TypeError below.\n            return ContinueSentinel;\n          }\n        }\n\n        context.method = \"throw\";\n        context.arg = new TypeError(\n          \"The iterator does not provide a 'throw' method\");\n      }\n\n      return ContinueSentinel;\n    }\n\n    var record = tryCatch(method, delegate.iterator, context.arg);\n\n    if (record.type === \"throw\") {\n      context.method = \"throw\";\n      context.arg = record.arg;\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    var info = record.arg;\n\n    if (! info) {\n      context.method = \"throw\";\n      context.arg = new TypeError(\"iterator result is not an object\");\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    if (info.done) {\n      // Assign the result of the finished delegate to the temporary\n      // variable specified by delegate.resultName (see delegateYield).\n      context[delegate.resultName] = info.value;\n\n      // Resume execution at the desired location (see delegateYield).\n      context.next = delegate.nextLoc;\n\n      // If context.method was \"throw\" but the delegate handled the\n      // exception, let the outer generator proceed normally. If\n      // context.method was \"next\", forget context.arg since it has been\n      // \"consumed\" by the delegate iterator. If context.method was\n      // \"return\", allow the original .return call to continue in the\n      // outer generator.\n      if (context.method !== \"return\") {\n        context.method = \"next\";\n        context.arg = undefined;\n      }\n\n    } else {\n      // Re-yield the result returned by the delegate method.\n      return info;\n    }\n\n    // The delegate iterator is finished, so forget it and continue with\n    // the outer generator.\n    context.delegate = null;\n    return ContinueSentinel;\n  }\n\n  // Define Generator.prototype.{next,throw,return} in terms of the\n  // unified ._invoke helper method.\n  defineIteratorMethods(Gp);\n\n  define(Gp, toStringTagSymbol, \"Generator\");\n\n  // A Generator should always return itself as the iterator object when the\n  // @@iterator function is called on it. Some browsers' implementations of the\n  // iterator prototype chain incorrectly implement this, causing the Generator\n  // object to not be returned from this call. This ensures that doesn't happen.\n  // See https://github.com/facebook/regenerator/issues/274 for more details.\n  Gp[iteratorSymbol] = function() {\n    return this;\n  };\n\n  Gp.toString = function() {\n    return \"[object Generator]\";\n  };\n\n  function pushTryEntry(locs) {\n    var entry = { tryLoc: locs[0] };\n\n    if (1 in locs) {\n      entry.catchLoc = locs[1];\n    }\n\n    if (2 in locs) {\n      entry.finallyLoc = locs[2];\n      entry.afterLoc = locs[3];\n    }\n\n    this.tryEntries.push(entry);\n  }\n\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\";\n    delete record.arg;\n    entry.completion = record;\n  }\n\n  function Context(tryLocsList) {\n    // The root entry object (effectively a try statement without a catch\n    // or a finally block) gives us a place to store values thrown from\n    // locations where there is no enclosing try statement.\n    this.tryEntries = [{ tryLoc: \"root\" }];\n    tryLocsList.forEach(pushTryEntry, this);\n    this.reset(true);\n  }\n\n  exports.keys = function(object) {\n    var keys = [];\n    for (var key in object) {\n      keys.push(key);\n    }\n    keys.reverse();\n\n    // Rather than returning an object with a next method, we keep\n    // things simple and return the next function itself.\n    return function next() {\n      while (keys.length) {\n        var key = keys.pop();\n        if (key in object) {\n          next.value = key;\n          next.done = false;\n          return next;\n        }\n      }\n\n      // To avoid creating an additional object, we just hang the .value\n      // and .done properties off the next function object itself. This\n      // also ensures that the minifier will not anonymize the function.\n      next.done = true;\n      return next;\n    };\n  };\n\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) {\n        return iteratorMethod.call(iterable);\n      }\n\n      if (typeof iterable.next === \"function\") {\n        return iterable;\n      }\n\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next() {\n          while (++i < iterable.length) {\n            if (hasOwn.call(iterable, i)) {\n              next.value = iterable[i];\n              next.done = false;\n              return next;\n            }\n          }\n\n          next.value = undefined;\n          next.done = true;\n\n          return next;\n        };\n\n        return next.next = next;\n      }\n    }\n\n    // Return an iterator with no values.\n    return { next: doneResult };\n  }\n  exports.values = values;\n\n  function doneResult() {\n    return { value: undefined, done: true };\n  }\n\n  Context.prototype = {\n    constructor: Context,\n\n    reset: function(skipTempReset) {\n      this.prev = 0;\n      this.next = 0;\n      // Resetting context._sent for legacy support of Babel's\n      // function.sent implementation.\n      this.sent = this._sent = undefined;\n      this.done = false;\n      this.delegate = null;\n\n      this.method = \"next\";\n      this.arg = undefined;\n\n      this.tryEntries.forEach(resetTryEntry);\n\n      if (!skipTempReset) {\n        for (var name in this) {\n          // Not sure about the optimal order of these conditions:\n          if (name.charAt(0) === \"t\" &&\n              hasOwn.call(this, name) &&\n              !isNaN(+name.slice(1))) {\n            this[name] = undefined;\n          }\n        }\n      }\n    },\n\n    stop: function() {\n      this.done = true;\n\n      var rootEntry = this.tryEntries[0];\n      var rootRecord = rootEntry.completion;\n      if (rootRecord.type === \"throw\") {\n        throw rootRecord.arg;\n      }\n\n      return this.rval;\n    },\n\n    dispatchException: function(exception) {\n      if (this.done) {\n        throw exception;\n      }\n\n      var context = this;\n      function handle(loc, caught) {\n        record.type = \"throw\";\n        record.arg = exception;\n        context.next = loc;\n\n        if (caught) {\n          // If the dispatched exception was caught by a catch block,\n          // then let that catch block handle the exception normally.\n          context.method = \"next\";\n          context.arg = undefined;\n        }\n\n        return !! caught;\n      }\n\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        var record = entry.completion;\n\n        if (entry.tryLoc === \"root\") {\n          // Exception thrown outside of any try block that could handle\n          // it, so set the completion value of the entire function to\n          // throw the exception.\n          return handle(\"end\");\n        }\n\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\");\n          var hasFinally = hasOwn.call(entry, \"finallyLoc\");\n\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            } else if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            }\n\n          } else if (hasFinally) {\n            if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else {\n            throw new Error(\"try statement without catch or finally\");\n          }\n        }\n      }\n    },\n\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev &&\n            hasOwn.call(entry, \"finallyLoc\") &&\n            this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n\n      if (finallyEntry &&\n          (type === \"break\" ||\n           type === \"continue\") &&\n          finallyEntry.tryLoc <= arg &&\n          arg <= finallyEntry.finallyLoc) {\n        // Ignore the finally entry if control is not jumping to a\n        // location outside the try/catch block.\n        finallyEntry = null;\n      }\n\n      var record = finallyEntry ? finallyEntry.completion : {};\n      record.type = type;\n      record.arg = arg;\n\n      if (finallyEntry) {\n        this.method = \"next\";\n        this.next = finallyEntry.finallyLoc;\n        return ContinueSentinel;\n      }\n\n      return this.complete(record);\n    },\n\n    complete: function(record, afterLoc) {\n      if (record.type === \"throw\") {\n        throw record.arg;\n      }\n\n      if (record.type === \"break\" ||\n          record.type === \"continue\") {\n        this.next = record.arg;\n      } else if (record.type === \"return\") {\n        this.rval = this.arg = record.arg;\n        this.method = \"return\";\n        this.next = \"end\";\n      } else if (record.type === \"normal\" && afterLoc) {\n        this.next = afterLoc;\n      }\n\n      return ContinueSentinel;\n    },\n\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) {\n          this.complete(entry.completion, entry.afterLoc);\n          resetTryEntry(entry);\n          return ContinueSentinel;\n        }\n      }\n    },\n\n    \"catch\": function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (record.type === \"throw\") {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n\n      // The context.catch method must only be called with a location\n      // argument that corresponds to a known catch block.\n      throw new Error(\"illegal catch attempt\");\n    },\n\n    delegateYield: function(iterable, resultName, nextLoc) {\n      this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      };\n\n      if (this.method === \"next\") {\n        // Deliberately forget the last sent value so that we don't\n        // accidentally pass it on to the delegate.\n        this.arg = undefined;\n      }\n\n      return ContinueSentinel;\n    }\n  };\n\n  // Regardless of whether this script is executing as a CommonJS module\n  // or not, return the runtime object so that we can declare the variable\n  // regeneratorRuntime in the outer scope, which allows this module to be\n  // injected easily by `bin/regenerator --include-runtime script.js`.\n  return exports;\n\n}(\n  // If this script is executing as a CommonJS module, use module.exports\n  // as the regeneratorRuntime namespace. Otherwise create a new empty\n  // object. Either way, the resulting object will be used to initialize\n  // the regeneratorRuntime variable at the top of this file.\n  typeof module === \"object\" ? module.exports : {}\n));\n\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  // This module should not be running in strict mode, so the above\n  // assignment should always work unless something is misconfigured. Just\n  // in case runtime.js accidentally runs in strict mode, we can escape\n  // strict mode using a global Function call. This could conceivably fail\n  // if a Content Security Policy forbids using Function, but in that case\n  // the proper solution is to fix the accidental strict mode problem. If\n  // you've misconfigured your bundler to force strict mode and applied a\n  // CSP to forbid Function, and you're not willing to fix either of those\n  // problems, please detail your unique predicament in a GitHub issue.\n  Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n}\n", "import isToday from 'date-fns/isToday';\nimport isYesterday from 'date-fns/isYesterday';\n/**\n * @name Get contrasting text color\n * @description Get contrasting text color from a text color\n * @param bgColor  Background color of text.\n * @returns contrasting text color\n */\nexport const getContrastingTextColor = (bgColor: string): string => {\n  const color = bgColor.replace('#', '');\n  const r = parseInt(color.slice(0, 2), 16);\n  const g = parseInt(color.slice(2, 4), 16);\n  const b = parseInt(color.slice(4, 6), 16);\n  // http://stackoverflow.com/a/3943023/112731\n  return r * 0.299 + g * 0.587 + b * 0.114 > 186 ? '#000000' : '#FFFFFF';\n};\n\n/**\n * @name Get formatted date\n * @description Get date in today, yesterday or any other date format\n * @param date  date\n * @param todayText  Today text\n * @param yesterdayText  Yesterday text\n * @returns formatted date\n */\nexport const formatDate = ({\n  date,\n  todayText,\n  yesterdayText,\n}: {\n  date: string;\n  todayText: string;\n  yesterdayText: string;\n}) => {\n  const dateValue = new Date(date);\n  if (isToday(dateValue)) return todayText;\n  if (isYesterday(dateValue)) return yesterdayText;\n  return date;\n};\n\n/**\n * @name formatTime\n * @description Format time to Hour, Minute and Second\n * @param timeInSeconds  number\n * @returns formatted time\n */\n\nexport const formatTime = (timeInSeconds: number) => {\n  let formattedTime = '';\n  if (timeInSeconds >= 60 && timeInSeconds < 3600) {\n    const minutes = Math.floor(timeInSeconds / 60);\n    formattedTime = `${minutes} Min`;\n    const seconds = minutes === 60 ? 0 : Math.floor(timeInSeconds % 60);\n    return formattedTime + `${seconds > 0 ? ' ' + seconds + ' Sec' : ''}`;\n  }\n  if (timeInSeconds >= 3600 && timeInSeconds < 86400) {\n    const hours = Math.floor(timeInSeconds / 3600);\n    formattedTime = `${hours} Hr`;\n    const minutes =\n      timeInSeconds % 3600 < 60 || hours === 24\n        ? 0\n        : Math.floor((timeInSeconds % 3600) / 60);\n    return formattedTime + `${minutes > 0 ? ' ' + minutes + ' Min' : ''}`;\n  }\n  if (timeInSeconds >= 86400) {\n    const days = Math.floor(timeInSeconds / 86400);\n    formattedTime = `${days} Day`;\n    const hours =\n      timeInSeconds % 86400 < 3600 || days >= 364\n        ? 0\n        : Math.floor((timeInSeconds % 86400) / 3600);\n    return formattedTime + `${hours > 0 ? ' ' + hours + ' Hr' : ''}`;\n  }\n  return `${Math.floor(timeInSeconds)} Sec`;\n};\n\n/**\n * @name trimContent\n * @description Trim a string to max length\n * @param content String to trim\n * @param maxLength Length of the string to trim, default 1024\n * @param ellipsis Boolean to add dots at the end of the string, default false\n * @returns trimmed string\n */\nexport const trimContent = (\n  content: string = '',\n  maxLength: number = 1024,\n  ellipsis: boolean = false\n) => {\n  let trimmedContent = content;\n  if (content.length > maxLength) {\n    trimmedContent = content.substring(0, maxLength);\n  }\n  if (ellipsis) {\n    trimmedContent = trimmedContent + '...';\n  }\n  return trimmedContent;\n};\n\n/**\n * @name convertSecondsToTimeUnit\n * @description Convert seconds to time unit\n * @param seconds  number\n * @param unitNames  object\n * @returns time and unit\n * @example\n * convertToUnit(60, { minute: 'm', hour: 'h', day: 'd' }); // { time: 1, unit: 'm' }\n * convertToUnit(60, { minute: 'Minutes', hour: 'Hours', day: 'Days' }); // { time: 1, unit: 'Minutes' }\n */\n\nexport const convertSecondsToTimeUnit = (\n  seconds: number,\n  unitNames: { minute: string; hour: string; day: string }\n) => {\n  if (seconds === null || seconds === 0) return { time: '', unit: '' };\n  if (seconds < 3600)\n    return { time: Number((seconds / 60).toFixed(1)), unit: unitNames.minute };\n  if (seconds < 86400)\n    return { time: Number((seconds / 3600).toFixed(1)), unit: unitNames.hour };\n  return { time: Number((seconds / 86400).toFixed(1)), unit: unitNames.day };\n};\n\n/**\n * @name fileNameWithEllipsis\n * @description Truncates a filename while preserving the extension\n * @param {Object} file - File object containing filename or name property\n * @param {number} [maxLength=26] - Maximum length of the filename (excluding extension)\n * @param {string} [ellipsis='…'] - Character to use for truncation\n * @returns {string} Truncated filename with extension\n * @example\n * fileNameWithEllipsis({ filename: 'very-long-filename.pdf' }, 10) // 'very-long-f….pdf'\n * fileNameWithEllipsis({ name: 'short.txt' }, 10) // 'short.txt'\n */\nexport const fileNameWithEllipsis = (\n  file: { filename?: string; name?: string },\n  maxLength: number = 26,\n  ellipsis: string = '…'\n): string => {\n  const fullName = file?.filename ?? file?.name ?? 'Untitled';\n\n  const dotIndex = fullName.lastIndexOf('.');\n  if (dotIndex === -1) return fullName;\n\n  const [name, extension] = [\n    fullName.slice(0, dotIndex),\n    fullName.slice(dotIndex),\n  ];\n\n  if (name.length <= maxLength) return fullName;\n\n  return `${name.slice(0, maxLength)}${ellipsis}${extension}`;\n};\n\n/**\n * @name splitName\n * @description Splits a full name into firstName and lastName\n * @param {string} name - Full name of the user\n * @returns {Object} Object with firstName and lastName\n * @example\n * splitName('Mary Jane Smith') // { firstName: 'Mary Jane', lastName: 'Smith' }\n * splitName('Alice') // { firstName: 'Alice', lastName: '' }\n * splitName('John Doe') // { firstName: 'John', lastName: 'Doe' }\n * splitName('') // { firstName: '', lastName: '' }\n */\nexport const splitName = (\n  fullName: string\n): { firstName: string; lastName: string } => {\n  const trimmedName = fullName.trim();\n  if (!trimmedName) {\n    return {\n      firstName: '',\n      lastName: '',\n    };\n  }\n\n  // Split the name by spaces\n  const nameParts = trimmedName.split(/\\s+/);\n\n  // If only one word, treat it as firstName\n  if (nameParts.length === 1) {\n    return {\n      firstName: nameParts[0],\n      lastName: '',\n    };\n  }\n\n  // Last element is lastName, everything else is firstName\n  const lastName = nameParts.pop() || '';\n  const firstName = nameParts.join(' ');\n\n  return { firstName, lastName };\n};\n\ninterface DownloadFileOptions {\n  url: string;\n  type: string;\n  extension?: string | null;\n}\n/**\n * Downloads a file from a URL with proper file type handling\n * @name downloadFile\n * @description Downloads file from URL with proper type handling and cleanup\n * @param {Object} options Download configuration options\n * @param {string} options.url File URL to download\n * @param {string} options.type File type identifier\n * @param {string} [options.extension] Optional file extension\n * @returns {Promise<boolean>} Returns true if download successful, false otherwise\n */\nexport const downloadFile = async ({\n  url,\n  type,\n  extension = null,\n}: DownloadFileOptions): Promise<void> => {\n  if (!url || !type) {\n    throw new Error('Invalid download parameters');\n  }\n\n  try {\n    const response = await fetch(url, { cache: 'no-store' });\n\n    if (!response.ok) {\n      throw new Error(`Download failed: ${response.status}`);\n    }\n\n    const blobData = await response.blob();\n\n    const contentType = response.headers.get('content-type');\n\n    const fileExtension =\n      extension || (contentType ? contentType.split('/')[1] : type);\n\n    const dispositionHeader = response.headers.get('content-disposition');\n    const filenameMatch = dispositionHeader?.match(/filename=\"(.*?)\"/);\n\n    const filename =\n      filenameMatch?.[1] ?? `attachment_${Date.now()}.${fileExtension}`;\n\n    const blobUrl = URL.createObjectURL(blobData);\n    const link = Object.assign(document.createElement('a'), {\n      href: blobUrl,\n      download: filename,\n      style: 'display: none',\n    });\n\n    document.body.append(link);\n    link.click();\n    link.remove();\n    URL.revokeObjectURL(blobUrl);\n  } catch (error) {\n    throw error instanceof Error ? error : new Error('Download failed');\n  }\n};\n\ninterface FileInfo {\n  name: string; // Full filename with extension\n  type: string; // File extension only\n  base: string; // Filename without extension\n}\n/**\n * Extracts file information from a URL or file path.\n *\n * @param {string} url - The URL or file path to process\n * @returns {FileInfo} Object containing file information\n *\n * @example\n * getFileInfo('https://example.com/path/Document%20Name.PDF')\n * returns {\n *   name: 'Document Name.PDF',\n *   type: 'pdf',\n *   base: 'Document Name'\n * }\n *\n * getFileInfo('invalid/url')\n * returns {\n *   name: 'Unknown File',\n *   type: '',\n *   base: 'Unknown File'\n * }\n */\nexport const getFileInfo = (url: string): FileInfo => {\n  const defaultInfo: FileInfo = {\n    name: 'Unknown File',\n    type: '',\n    base: 'Unknown File',\n  };\n\n  if (!url || typeof url !== 'string') {\n    return defaultInfo;\n  }\n\n  try {\n    // Handle both URL and file path cases\n    const cleanUrl = url\n      .split(/[?#]/)[0] // Remove query params and hash\n      .replace(/\\\\/g, '/'); // Normalize path separators\n\n    const encodedFilename = cleanUrl.split('/').pop();\n    if (!encodedFilename) {\n      return defaultInfo;\n    }\n\n    const fileName = decodeURIComponent(encodedFilename);\n\n    // Handle hidden files (starting with dot)\n    if (fileName.startsWith('.') && !fileName.includes('.', 1)) {\n      return { name: fileName, type: '', base: fileName };\n    }\n\n    // last index is where the file extension starts\n    // This will handle cases where the file name has multiple dots\n    const lastDotIndex = fileName.lastIndexOf('.');\n    if (lastDotIndex === -1 || lastDotIndex === 0) {\n      return { name: fileName, type: '', base: fileName };\n    }\n\n    const base = fileName.slice(0, lastDotIndex);\n    const type = fileName.slice(lastDotIndex + 1).toLowerCase();\n\n    return { name: fileName, type, base };\n  } catch (error) {\n    console.error('Error processing file info:', error);\n    return defaultInfo;\n  }\n};\n\n/**\n * Formats a number with K/M/B/T suffixes using Intl.NumberFormat\n * @param {number | string | null | undefined} num - The number to format\n * @returns {string} Formatted string (e.g., \"1.2K\", \"2.3M\", \"999\")\n * @example\n * formatNumber(1234)     // \"1.2K\"\n * formatNumber(1000000)  // \"1M\"\n * formatNumber(999)      // \"999\"\n * formatNumber(12344)    // \"12.3K\"\n */\nexport const formatNumber = (\n  num: number | string | null | undefined\n): string => {\n  const n = Number(num) || 0;\n  return new Intl.NumberFormat('en', {\n    notation: 'compact',\n    maximumFractionDigits: 1,\n  } as Intl.NumberFormatOptions).format(n);\n};\n", "/**\n * URL related helper functions\n */\n\n/**\n * Converts various input formats to URL objects.\n * Handles URL objects, domain strings, relative paths, and full URLs.\n * @param {string|URL} input - Input to convert to URL object\n * @returns {URL|null} URL object or null if input is invalid\n */\nexport const toURL = (input: string | URL | null | undefined): URL | null => {\n  if (!input) return null;\n  if (input instanceof URL) return input;\n\n  if (\n    typeof input === 'string' &&\n    !input.includes('://') &&\n    !input.startsWith('/')\n  ) {\n    return new URL(`https://${input}`);\n  }\n\n  if (typeof input === 'string' && input.startsWith('/')) {\n    return new URL(input, window.location.origin);\n  }\n\n  return new URL(input as string);\n};\n\n/**\n * Determines if two URLs belong to the same host by comparing their normalized URL objects.\n * Handles various input formats including URL objects, domain strings, relative paths, and full URLs.\n * Returns false if either URL cannot be parsed or normalized.\n * @param {string|URL} url1 - First URL to compare\n * @param {string|URL} url2 - Second URL to compare\n * @returns {boolean} True if both URLs have the same host, false otherwise\n */\nexport const isSameHost = (\n  url1: string | URL | null | undefined,\n  url2: string | URL | null | undefined\n): boolean => {\n  try {\n    const urlObj1 = toURL(url1);\n    const urlObj2 = toURL(url2);\n\n    if (!urlObj1 || !urlObj2) return false;\n\n    return urlObj1.hostname === urlObj2.hostname;\n  } catch (error) {\n    return false;\n  }\n};\n\n/**\n * Check if a string is a valid domain name.\n * An empty string is allowed and considered valid.\n *\n * @param domain Domain to validate.\n * @returns Whether the domain matches the rules.\n */\nexport const isValidDomain = (domain: string): boolean => {\n  if (domain === '') return true;\n\n  const domainRegex = /^(?!-)(?!.*--)[\\p{L}0-9-]{1,63}(?<!-)(?:\\.(?!-)(?!.*--)[\\p{L}0-9-]{1,63}(?<!-))*\\.[\\p{L}]{2,63}$/u;\n\n  return domainRegex.test(domain) && domain.length <= 253;\n};\n", "/**\n * Sorts an array of numbers in ascending order.\n * @param {number[]} arr - The array of numbers to be sorted.\n * @returns {number[]} - The sorted array.\n */\nexport function sortAsc(arr: number[]) {\n  // .slice() is used to create a copy of the array so that the original array is not mutated\n  return arr.slice().sort((a, b) => a - b);\n}\n\n/**\n * Calculates the quantile value of an array at a specified percentile.\n * @param {number[]} arr - The array of numbers to calculate the quantile value from.\n * @param {number} q - The percentile to calculate the quantile value for.\n * @returns {number} - The quantile value.\n */\nexport function quantile(arr: number[], q: number) {\n  const sorted = sortAsc(arr); // Sort the array in ascending order\n  return _quantileForSorted(sorted, q); // Calculate the quantile value\n}\n\n/**\n * Clamps a value between a minimum and maximum range.\n * @param {number} min - The minimum range.\n * @param {number} max - The maximum range.\n * @param {number} value - The value to be clamped.\n * @returns {number} - The clamped value.\n */\nexport function clamp(min: number, max: number, value: number) {\n  if (value < min) {\n    return min;\n  }\n  if (value > max) {\n    return max;\n  }\n  return value;\n}\n\n/**\n * This method assumes the the array provided is already sorted in ascending order.\n * It's a helper method for the quantile method and should not be exported as is.\n *\n * @param {number[]} arr - The array of numbers to calculate the quantile value from.\n * @param {number} q - The percentile to calculate the quantile value for.\n * @returns {number} - The quantile value.\n */\nfunction _quantileForSorted(sorted: number[], q: number) {\n  const clamped = clamp(0, 1, q); // Clamp the percentile between 0 and 1\n  const pos = (sorted.length - 1) * clamped; // Calculate the index of the element at the specified percentile\n  const base = Math.floor(pos); // Find the index of the closest element to the specified percentile\n  const rest = pos - base; // Calculate the decimal value between the closest elements\n\n  // Interpolate the quantile value between the closest elements\n  // Most libraries don't to the interpolation, but I'm just having fun here\n  // also see https://en.wikipedia.org/wiki/Quantile#Estimating_quantiles_from_a_sample\n  if (sorted[base + 1] !== undefined) {\n    // in case the position was a integer, the rest will be 0 and the interpolation will be skipped\n    return sorted[base] + rest * (sorted[base + 1] - sorted[base]);\n  }\n\n  // Return the closest element if there is no interpolation possible\n  return sorted[base];\n}\n\n/**\n * Calculates the quantile values for an array of intervals.\n * @param {number[]} data - The array of numbers to calculate the quantile values from.\n * @param {number[]} intervals - The array of intervals to calculate the quantile values for.\n * @returns {number[]} - The array of quantile values for the intervals.\n */\nexport const getQuantileIntervals = (data: number[], intervals: number[]) => {\n  // Sort the array in ascending order before looping through the intervals.\n  // depending on the size of the array and the number of intervals, this can speed up the process by at least twice\n  // for a random array of 100 numbers and 5 intervals, the speedup is 3x\n  const sorted = sortAsc(data);\n\n  return intervals.map(interval => {\n    return _quantileForSorted(sorted, interval);\n  });\n};\n\n/**\n * Calculates the relative position of a point from the center of an element\n *\n * @param {number} mouseX - The x-coordinate of the mouse pointer\n * @param {number} mouseY - The y-coordinate of the mouse pointer\n * @param {DOMRect} rect - The bounding client rectangle of the target element\n * @returns {{relativeX: number, relativeY: number}} Object containing x and y distances from center\n */\nexport const calculateCenterOffset = (\n  mouseX: number,\n  mouseY: number,\n  rect: DOMRect\n) => {\n  const centerX = rect.left + rect.width / 2;\n  const centerY = rect.top + rect.height / 2;\n\n  return {\n    relativeX: mouseX - centerX,\n    relativeY: mouseY - centerY,\n  };\n};\n\n/**\n * Applies a rotation matrix to coordinates\n * Used to adjust mouse coordinates based on the current rotation of the image\n * This function implements a standard 2D rotation matrix transformation:\n * [x']   [cos(θ) -sin(θ)] [x]\n * [y'] = [sin(θ)  cos(θ)] [y]\n *\n * @see {@link https://mathworld.wolfram.com/RotationMatrix.html} for mathematical derivation\n *\n * @param {number} relativeX - X-coordinate relative to center before rotation\n * @param {number} relativeY - Y-coordinate relative to center before rotation\n * @param {number} angle - Rotation angle in degrees\n * @returns {{rotatedX: number, rotatedY: number}} Coordinates after applying rotation matrix\n */\nexport const applyRotationTransform = (\n  relativeX: number,\n  relativeY: number,\n  angle: number\n) => {\n  const radians = (angle * Math.PI) / 180;\n  const cos = Math.cos(-radians);\n  const sin = Math.sin(-radians);\n\n  return {\n    rotatedX: relativeX * cos - relativeY * sin,\n    rotatedY: relativeX * sin + relativeY * cos,\n  };\n};\n\n/**\n * Converts absolute rotated coordinates to percentage values relative to image dimensions\n * Ensures values are clamped between 0-100% for valid CSS transform-origin properties\n *\n * @param {number} rotatedX - X-coordinate after rotation transformation\n * @param {number} rotatedY - Y-coordinate after rotation transformation\n * @param {number} width - Width of the target element\n * @param {number} height - Height of the target element\n * @returns {{x: number, y: number}} Normalized coordinates as percentages (0-100%)\n */\nexport const normalizeToPercentage = (\n  rotatedX: number,\n  rotatedY: number,\n  width: number,\n  height: number\n) => {\n  // Convert to percentages (0-100%) relative to image dimensions\n  // 50% represents the center point\n  // The division by (width/2) maps the range [-width/2, width/2] to [-50%, 50%]\n  // Adding 50% shifts this to [0%, 100%]\n  return {\n    x: Math.max(0, Math.min(100, 50 + (rotatedX / (width / 2)) * 50)),\n    y: Math.max(0, Math.min(100, 50 + (rotatedY / (height / 2)) * 50)),\n  };\n};\n", "import {\n  Conversation,\n  Sender,\n  Variables,\n  CustomAttributes,\n  Contact,\n  Inbox,\n} from './types/conversation';\nconst MESSAGE_VARIABLES_REGEX = /{{(.*?)}}/g;\n\nconst skipCodeBlocks = (str: string) => str.replace(/```(?:.|\\n)+?```/g, '');\n\nexport const capitalizeName = (name: string | null): string => {\n  if (!name) return ''; // Return empty string for null or undefined input\n\n  return name\n    .split(' ') // Split the name into words based on spaces\n    .map(word => {\n      if (!word) return ''; // Handle empty strings that might result from multiple spaces\n\n      // Capitalize only the first character, leaving the rest unchanged\n      // This correctly handles accented characters like 'í' in 'Aríel'\n      return word.charAt(0).toUpperCase() + word.slice(1);\n    })\n    .join(' '); // Rejoin the words with spaces\n};\n\nexport const getFirstName = ({ user }: { user: Sender }) => {\n  const firstName = user?.name ? user.name.split(' ').shift() : '';\n  return capitalizeName(firstName as string);\n};\n\nexport const getLastName = ({ user }: { user: Sender }) => {\n  if (user && user.name) {\n    const lastName =\n      user.name.split(' ').length > 1 ? user.name.split(' ').pop() : '';\n    return capitalizeName(lastName as string);\n  }\n  return '';\n};\n\nexport const getMessageVariables = ({\n  conversation,\n  contact,\n  inbox,\n}: {\n  conversation: Conversation;\n  contact?: Contact;\n  inbox?: Inbox;\n}) => {\n  const {\n    meta: { assignee, sender },\n    id,\n    custom_attributes: conversationCustomAttributes = {},\n  } = conversation;\n  const { custom_attributes: contactCustomAttributes } = contact || {};\n\n  const standardVariables = {\n    'contact.name': capitalizeName(sender?.name || ''),\n    'contact.first_name': getFirstName({ user: sender }),\n    'contact.last_name': getLastName({ user: sender }),\n    'contact.email': sender?.email,\n    'contact.phone': sender?.phone_number,\n    'contact.id': sender?.id,\n    'conversation.id': id,\n    'inbox.id': inbox?.id,\n    'inbox.name': inbox?.name,\n    'agent.name': capitalizeName(assignee?.name || ''),\n    'agent.first_name': getFirstName({ user: assignee }),\n    'agent.last_name': getLastName({ user: assignee }),\n    'agent.email': assignee?.email ?? '',\n  };\n  const conversationCustomAttributeVariables = Object.entries(\n    conversationCustomAttributes ?? {}\n  ).reduce((acc: CustomAttributes, [key, value]) => {\n    acc[`conversation.custom_attribute.${key}`] = value;\n    return acc;\n  }, {});\n\n  const contactCustomAttributeVariables = Object.entries(\n    contactCustomAttributes ?? {}\n  ).reduce((acc: CustomAttributes, [key, value]) => {\n    acc[`contact.custom_attribute.${key}`] = value;\n    return acc;\n  }, {});\n\n  const variables = {\n    ...standardVariables,\n    ...conversationCustomAttributeVariables,\n    ...contactCustomAttributeVariables,\n  };\n\n  return variables;\n};\n\nexport const replaceVariablesInMessage = ({\n  message,\n  variables,\n}: {\n  message: string;\n  variables: Variables;\n}) => {\n  // @ts-ignore\n  return message?.replace(MESSAGE_VARIABLES_REGEX, (_, replace) => {\n    return variables[replace.trim()]\n      ? variables[replace.trim().toLowerCase()]\n      : '';\n  });\n};\n\nexport const getUndefinedVariablesInMessage = ({\n  message,\n  variables,\n}: {\n  message: string;\n  variables: Variables;\n}) => {\n  const messageWithOutCodeBlocks = skipCodeBlocks(message);\n  const matches = messageWithOutCodeBlocks.match(MESSAGE_VARIABLES_REGEX);\n  if (!matches) return [];\n\n  return matches\n    .map(match => {\n      return match\n        .replace('{{', '')\n        .replace('}}', '')\n        .trim();\n    })\n    .filter(variable => {\n      return variables[variable] === undefined;\n    });\n};\n", "import { Conversation } from './types/conversation';\nimport { AppliedSla, SLAStatus } from './types/sla';\n\n/**\n * Calculates the threshold for an SLA based on the current time and the provided threshold.\n * @param timeOffset - The time offset in seconds.\n * @param threshold - The threshold in seconds or null if not applicable.\n * @returns The calculated threshold in seconds or null if the threshold is null.\n */\nconst calculateThreshold = (\n  timeOffset: number,\n  threshold: number | null\n): number | null => {\n  // Calculate the time left for the SLA to breach or the time since the SLA has missed\n  if (threshold === null) return null;\n  const currentTime = Math.floor(Date.now() / 1000);\n  return timeOffset + threshold - currentTime;\n};\n\n/**\n * Finds the most urgent SLA status based on the threshold.\n * @param SLAStatuses - An array of SLAStatus objects.\n * @returns The most urgent SLAStatus object.\n */\nconst findMostUrgentSLAStatus = (SLAStatuses: SLAStatus[]): SLAStatus => {\n  // Sort the SLAs based on the threshold and return the most urgent SLA\n  SLAStatuses.sort(\n    (sla1, sla2) => Math.abs(sla1.threshold) - Math.abs(sla2.threshold)\n  );\n  return SLAStatuses[0];\n};\n\n/**\n * Formats the SLA time in a human-readable format.\n * @param seconds - The time in seconds.\n * @returns A formatted string representing the time.\n */\nconst formatSLATime = (seconds: number): string => {\n  const units: { [key: string]: number } = {\n    y: 31536000, // 60 * 60 * 24 * 365\n    mo: 2592000, // 60 * 60 * 24 * 30\n    d: 86400, // 60 * 60 * 24\n    h: 3600, // 60 * 60\n    m: 60,\n  };\n\n  if (seconds < 60) {\n    return '1m';\n  }\n\n  // we will only show two parts, two max granularity's, h-m, y-d, d-h, m, but no seconds\n  const parts: string[] = [];\n\n  Object.keys(units).forEach(unit => {\n    const value = Math.floor(seconds / units[unit]);\n    if (seconds < 60 && parts.length > 0) return;\n    if (parts.length === 2) return;\n    if (value > 0) {\n      parts.push(value + unit);\n      seconds -= value * units[unit];\n    }\n  });\n  return parts.join(' ');\n};\n\n/**\n * Creates an SLA object based on the type, applied SLA, and chat details.\n * @param type - The type of SLA (FRT, NRT, RT).\n * @param appliedSla - The applied SLA details.\n * @param chat - The chat details.\n * @returns An object containing the SLA status or null if conditions are not met.\n */\nconst createSLAObject = (\n  type: string,\n  appliedSla: AppliedSla,\n  chat: Conversation\n): { threshold: number | null; type: string; condition: boolean } | null => {\n  const {\n    sla_first_response_time_threshold: frtThreshold,\n    sla_next_response_time_threshold: nrtThreshold,\n    sla_resolution_time_threshold: rtThreshold,\n    created_at: createdAt,\n  } = appliedSla;\n\n  const {\n    first_reply_created_at: firstReplyCreatedAt,\n    waiting_since: waitingSince,\n    status,\n  } = chat;\n\n  const SLATypes: {\n    [key: string]: { threshold: number | null; condition: boolean };\n  } = {\n    FRT: {\n      threshold: calculateThreshold(createdAt, frtThreshold),\n      //   Check FRT only if threshold is not null and first reply hasn't been made\n      condition:\n        frtThreshold !== null &&\n        (!firstReplyCreatedAt || firstReplyCreatedAt === 0),\n    },\n    NRT: {\n      threshold: calculateThreshold(waitingSince, nrtThreshold),\n      // Check NRT only if threshold is not null, first reply has been made and we are waiting since\n      condition:\n        nrtThreshold !== null && !!firstReplyCreatedAt && !!waitingSince,\n    },\n    RT: {\n      threshold: calculateThreshold(createdAt, rtThreshold),\n      // Check RT only if the conversation is open and threshold is not null\n      condition: status === 'open' && rtThreshold !== null,\n    },\n  };\n\n  const SLAStatus = SLATypes[type];\n  return SLAStatus ? { ...SLAStatus, type } : null;\n};\n\n/**\n * Evaluates SLA conditions and returns an array of SLAStatus objects.\n * @param appliedSla - The applied SLA details.\n * @param chat - The chat details.\n * @returns An array of SLAStatus objects.\n */\nconst evaluateSLAConditions = (\n  appliedSla: AppliedSla,\n  chat: Conversation\n): {\n  threshold: number;\n  type: string;\n  icon: string;\n  isSlaMissed: boolean;\n}[] => {\n  // Filter out the SLA based on conditions and update the object with the breach status(icon, isSlaMissed)\n  const SLATypes = ['FRT', 'NRT', 'RT'];\n  return SLATypes.map(type => createSLAObject(type, appliedSla, chat))\n    .filter(\n      (\n        SLAStatus\n      ): SLAStatus is { threshold: number; type: string; condition: boolean } =>\n        !!SLAStatus && SLAStatus.condition\n    )\n    .map(SLAStatus => ({\n      ...SLAStatus,\n      icon: SLAStatus.threshold <= 0 ? 'flame' : 'alarm',\n      isSlaMissed: SLAStatus.threshold <= 0,\n    }));\n};\n\n/**\n * Evaluates the SLA status for a given chat and applied SLA.\n * @param {Object} params - The parameters object.\n * @param params.appliedSla - The applied SLA details.\n * @param params.chat - The chat details.\n * @returns An object containing the most urgent SLA status.\n */\nexport const evaluateSLAStatus = ({\n  appliedSla,\n  chat,\n}: {\n  appliedSla: AppliedSla;\n  chat: Conversation;\n}): { type: string; threshold: string; icon: string; isSlaMissed: boolean } => {\n  if (!appliedSla || !chat)\n    return { type: '', threshold: '', icon: '', isSlaMissed: false };\n\n  // Filter out the SLA and create the object for each breach\n  const SLAStatuses = evaluateSLAConditions(appliedSla, chat) as SLAStatus[];\n\n  // Return the most urgent SLA which is latest to breach or has missed\n  const mostUrgent = findMostUrgentSLAStatus(SLAStatuses);\n  return mostUrgent\n    ? {\n        type: mostUrgent?.type,\n        threshold: formatSLATime(\n          mostUrgent.threshold <= 0\n            ? -mostUrgent.threshold\n            : mostUrgent.threshold\n        ),\n        icon: mostUrgent.icon,\n        isSlaMissed: mostUrgent.isSlaMissed,\n      }\n    : { type: '', threshold: '', icon: '', isSlaMissed: false };\n};\n", "/**\n * Parses various date formats into a JavaScript Date object\n *\n * This function handles different date input formats commonly found in conversation data:\n * - 10-digit timestamps (Unix seconds) - automatically converted to milliseconds\n * - 13-digit timestamps (Unix milliseconds) - used directly\n * - String representations of timestamps\n * - ISO date strings (e.g., \"2025-06-01T12:30:00Z\")\n * - Simple date strings (e.g., \"2025-06-01\") - time defaults to 00:00:00\n * - Date strings with space-separated time (e.g., \"2025-06-01 12:30:00\")\n *\n * Note: This function follows JavaScript Date constructor behavior for date parsing.\n * Some invalid dates like \"2025-02-30\" auto-correct to valid dates (becomes \"2025-03-02\"),\n * while malformed strings like \"2025-13-01\" or \"2025-06-01T25:00:00\" return null.\n *\n * @example\n * coerceToDate('2025-06-01') // Returns Date object set to 2025-06-01 00:00:00\n * coerceToDate('2025-06-01T12:30:00Z') // Returns Date object with specified time\n * coerceToDate(1748834578) // Returns Date object (10-digit timestamp in seconds)\n * coerceToDate(1748834578000) // Returns Date object (13-digit timestamp in milliseconds)\n * coerceToDate('1748834578') // Returns Date object (string timestamp converted)\n * coerceToDate(null) // Returns null\n * coerceToDate('invalid-date') // Returns null\n */\nexport const coerceToDate = (\n  dateInput: string | number | null | undefined\n): Date | null => {\n  if (dateInput == null) return null;\n\n  let timestamp = typeof dateInput === 'number' ? dateInput : null;\n\n  // Handle string inputs that represent numeric timestamps\n  if (\n    timestamp === null &&\n    typeof dateInput === 'string' &&\n    /^\\d+$/.test(dateInput)\n  ) {\n    timestamp = Number(dateInput);\n  }\n\n  // Process numeric timestamps\n  if (timestamp !== null) {\n    // Convert 10-digit timestamps (seconds) to milliseconds\n    const timestampMs =\n      timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;\n    return new Date(timestampMs);\n  }\n\n  // Process string date inputs\n  if (typeof dateInput === 'string') {\n    const dateObj = new Date(dateInput);\n\n    // Return null for invalid dates\n    if (Number.isNaN(dateObj.getTime())) return null;\n\n    // If no time component is specified, set time to 00:00:00\n    // this is because by default JS will set the time to midnight UTC for that date\n    const hasTimeComponent =\n      /T\\d{2}:\\d{2}(:\\d{2})?/.test(dateInput) || /\\d{2}:\\d{2}/.test(dateInput);\n    if (!hasTimeComponent) {\n      dateObj.setHours(0, 0, 0, 0);\n    }\n\n    return dateObj;\n  }\n\n  return null;\n};\n", "/**\n * Creates a typing indicator utility.\n * @param onStartTyping Callback function to be called when typing starts\n * @param onStopTyping Callback function to be called when typing stops after delay\n * @param idleTime Delay for idle time in ms before considering typing stopped\n * @returns An object with start and stop methods for typing indicator\n */\n\ntype CallbackFunction = () => void;\ntype Timeout = ReturnType<typeof setTimeout>;\n\nexport const createTypingIndicator = (\n  onStartTyping: CallbackFunction,\n  onStopTyping: CallbackFunction,\n  idleTime: number\n) => {\n  let timer: Timeout | null = null;\n\n  const start = (): void => {\n    if (!timer) {\n      onStartTyping();\n    }\n    reset();\n  };\n\n  const stop = (): void => {\n    if (timer) {\n      clearTimeout(timer as Timeout);\n      timer = null;\n      onStopTyping();\n    }\n  };\n\n  const reset = (): void => {\n    if (timer) {\n      clearTimeout(timer as Timeout);\n    }\n    timer = setTimeout(() => {\n      stop();\n    }, idleTime) as Timeout;\n  };\n\n  return { start, stop };\n};\n", "/**\n * Creates a debounced version of a function that delays invoking the provided function\n * until after a specified wait time has elapsed since the last time it was invoked.\n *\n * @param {Function} func - The function to debounce. Will receive any arguments passed to the debounced function.\n * @param {number} wait - The number of milliseconds to delay execution after the last call.\n * @param {boolean} [immediate] - If true, the function will execute immediately on the first call,\n *                               then start the debounce behavior for subsequent calls.\n * @param {number} [maxWait] - The maximum time the function can be delayed before it's forcibly executed.\n *                            If specified, the function will be called after this many milliseconds\n *                            have passed since its last execution, regardless of the debounce wait time.\n *\n * @returns {Function} A debounced version of the original function that has the following behavior:\n *   - Delays execution until `wait` milliseconds have passed since the last call\n *   - If `immediate` is true, executes on the leading edge of the first call\n *   - If `maxWait` is provided, ensures the function is called at least once every `maxWait` milliseconds\n *   - Preserves the `this` context and arguments of the most recent call\n *   - Cancels pending executions when called again within the wait period\n *\n * @example\n * // Basic debounce\n * const debouncedSearch = debounce(searchAPI, 300);\n *\n * // With immediate execution\n * const debouncedSave = debounce(saveData, 1000, true);\n *\n * // With maximum wait time\n * const debouncedUpdate = debounce(updateUI, 200, false, 1000);\n */\nexport const debounce = (\n  func: (...args: any[]) => void,\n  wait: number,\n  immediate?: boolean,\n  maxWait?: number\n) => {\n  let timeout: ReturnType<typeof setTimeout> | null = null;\n  let lastInvokeTime = 0;\n\n  return function(this: any, ...args: any[]) {\n    const time = Date.now();\n    const isFirstCall = lastInvokeTime === 0;\n\n    // Check if this is the first call and immediate execution is requested\n    if (isFirstCall && immediate) {\n      lastInvokeTime = time;\n      func.apply(this, args);\n      return;\n    }\n\n    // Clear any existing timeout\n    if (timeout !== null) {\n      clearTimeout(timeout);\n      timeout = null;\n    }\n\n    // Calculate if maxWait threshold has been reached\n    const timeSinceLastInvoke = time - lastInvokeTime;\n    const shouldInvokeNow =\n      maxWait !== undefined && timeSinceLastInvoke >= maxWait;\n\n    if (shouldInvokeNow) {\n      lastInvokeTime = time;\n      func.apply(this, args);\n      return;\n    }\n\n    // Set a new timeout\n    timeout = setTimeout(() => {\n      lastInvokeTime = Date.now();\n      timeout = null;\n      func.apply(this, args);\n    }, wait);\n  };\n};\n", "import {\n  EmailMessage,\n  MessageType,\n  IncomingEmailMessage,\n  OutgoingEmailMessage,\n} from './types/message';\n\nexport function getRecipients(\n  lastEmail: EmailMessage,\n  conversationContact: string,\n  inboxEmail: string,\n  forwardToEmail: string\n) {\n  let to = [] as string[];\n  let cc = [] as string[];\n  let bcc = [] as string[];\n\n  // Reset emails if there's no lastEmail\n  if (!lastEmail) {\n    return { to, cc, bcc };\n  }\n\n  // Extract values from lastEmail and current conversation context\n  const { message_type: messageType } = lastEmail;\n\n  const isIncoming = messageType === MessageType.INCOMING;\n\n  let emailAttributes = {} as {\n    cc: string[] | null;\n    bcc: string[] | null;\n    from: string[] | null;\n    to: string[] | null;\n  };\n\n  if (isIncoming) {\n    const {\n      content_attributes: contentAttributes,\n    } = lastEmail as IncomingEmailMessage;\n    const email = contentAttributes.email;\n    emailAttributes = {\n      cc: email?.cc || [],\n      bcc: email?.bcc || [],\n      from: email?.from || [],\n      to: [],\n    };\n  } else {\n    const {\n      content_attributes: contentAttributes,\n    } = lastEmail as OutgoingEmailMessage;\n\n    const {\n      cc_emails: ccEmails = [],\n      bcc_emails: bccEmails = [],\n      to_emails: toEmails = [],\n    } = contentAttributes ?? {};\n\n    emailAttributes = {\n      cc: ccEmails,\n      bcc: bccEmails,\n      to: toEmails,\n      from: [],\n    };\n  }\n\n  let isLastEmailFromContact = false;\n  // this will be false anyway if the last email was outgoing\n  isLastEmailFromContact =\n    isIncoming && (emailAttributes.from ?? []).includes(conversationContact);\n\n  if (isIncoming) {\n    // Reply to sender if incoming\n    to.push(...(emailAttributes.from ?? []));\n  } else {\n    // Otherwise, reply to the last recipient (for outgoing message)\n    // If there is no to_emails, reply to the conversation contact\n    to.push(...(emailAttributes.to ?? [conversationContact]));\n  }\n\n  // Start building the cc list, including additional recipients\n  // If the email had multiple recipients, include them in the cc list\n  cc = emailAttributes.cc ? [...emailAttributes.cc] : [];\n  // Only include 'to' recipients in cc for incoming emails, not for outgoing\n  if (Array.isArray(emailAttributes.to) && isIncoming) {\n    cc.push(...emailAttributes.to);\n  }\n\n  // Add the conversation contact to cc if the last email wasn't sent by them\n  // Ensure the message is an incoming one\n  if (!isLastEmailFromContact && isIncoming) {\n    cc.push(conversationContact);\n  }\n\n  // Process BCC: Remove conversation contact from bcc as it is already in cc\n  bcc = (emailAttributes.bcc || []).filter(\n    emailAddress => emailAddress !== conversationContact\n  );\n\n  // Filter out undesired emails from cc:\n  // - Remove conversation contact from cc if they sent the last email\n  // - Remove inbox and forward-to email to prevent loops\n  // - Remove emails matching the reply UUID pattern\n  const replyUUIDPattern = /^reply\\+([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/i;\n  cc = cc.filter(email => {\n    if (email === conversationContact && isLastEmailFromContact) {\n      return false;\n    }\n    if (email === inboxEmail || email === forwardToEmail) {\n      return false;\n    }\n    if (replyUUIDPattern.test(email)) {\n      return false;\n    }\n    return true;\n  });\n\n  bcc = bcc.filter(email => {\n    if (\n      email === inboxEmail ||\n      email === forwardToEmail ||\n      replyUUIDPattern.test(email)\n    ) {\n      return false;\n    }\n\n    return true;\n  });\n\n  // Deduplicate each recipient list by converting to a Set then back to an array\n  to = Array.from(new Set(to));\n  cc = Array.from(new Set(cc));\n  bcc = Array.from(new Set(bcc));\n\n  return {\n    to,\n    cc,\n    bcc,\n  };\n}\n", "/**\n * Function that parses a string boolean value and returns the corresponding boolean value\n * @param {string | number} candidate - The string boolean value to be parsed\n * @return {boolean} - The parsed boolean value\n */\n\nexport function parseBoolean(candidate: string | number) {\n  try {\n    // lowercase the string, so TRUE becomes true\n    const candidateString = String(candidate).toLowerCase();\n\n    // wrap in boolean to ensure that the return value\n    // is a boolean even if values like 0 or 1 are passed\n    return Boolean(JSON.parse(candidateString));\n  } catch (error) {\n    return false;\n  }\n}\n"], "names": ["MessageType", "runtime", "exports", "Op", "Object", "prototype", "hasOwn", "hasOwnProperty", "$Symbol", "Symbol", "iteratorSymbol", "iterator", "asyncIteratorSymbol", "asyncIterator", "toStringTagSymbol", "toStringTag", "define", "obj", "key", "value", "defineProperty", "enumerable", "configurable", "writable", "err", "wrap", "innerFn", "outerFn", "self", "tryLocsList", "generator", "create", "Generator", "context", "Context", "_invoke", "state", "method", "arg", "Error", "undefined", "done", "delegate", "delegate<PERSON><PERSON><PERSON>", "maybeInvokeDelegate", "ContinueSentinel", "sent", "_sent", "dispatchException", "abrupt", "record", "tryCatch", "type", "makeInvokeMethod", "fn", "call", "GeneratorFunction", "GeneratorFunctionPrototype", "IteratorPrototype", "this", "getProto", "getPrototypeOf", "NativeIteratorPrototype", "values", "Gp", "defineIteratorMethods", "for<PERSON>ach", "AsyncIterator", "PromiseImpl", "previousPromise", "callInvokeWithMethodAndArg", "resolve", "reject", "invoke", "result", "__await", "then", "unwrapped", "error", "TypeError", "info", "resultName", "next", "nextLoc", "pushTryEntry", "locs", "entry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "iterable", "iteratorMethod", "isNaN", "length", "i", "doneResult", "constructor", "displayName", "isGeneratorFunction", "gen<PERSON>un", "ctor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "iter", "toString", "keys", "object", "reverse", "pop", "skip<PERSON>emp<PERSON><PERSON><PERSON>", "prev", "char<PERSON>t", "slice", "stop", "rootRecord", "rval", "exception", "handle", "loc", "caught", "hasCatch", "hasFinally", "finallyEntry", "complete", "finish", "catch", "thrown", "<PERSON><PERSON><PERSON>", "module", "regeneratorRuntime", "accidentalStrictMode", "Function", "downloadFile", "extension", "url", "fetch", "cache", "response", "ok", "status", "blob", "blobData", "contentType", "headers", "get", "fileExtension", "split", "<PERSON><PERSON><PERSON><PERSON>", "filenameMatch", "match", "filename", "Date", "now", "blobUrl", "URL", "createObjectURL", "link", "assign", "document", "createElement", "href", "download", "style", "body", "append", "click", "remove", "revokeObjectURL", "_context", "toURL", "input", "includes", "startsWith", "window", "location", "origin", "sortAsc", "arr", "sort", "a", "b", "clamp", "min", "max", "_quantileForSorted", "sorted", "q", "clamped", "pos", "base", "Math", "floor", "MESSAGE_VARIABLES_REGEX", "capitalizeName", "map", "word", "toUpperCase", "join", "getFirstName", "user", "firstName", "shift", "getLastName", "lastName", "calculateThreshold", "timeOffset", "threshold", "formatSLATime", "seconds", "units", "y", "mo", "d", "h", "m", "parts", "unit", "relativeX", "relativeY", "angle", "radians", "PI", "cos", "sin", "rotatedX", "rotatedY", "mouseX", "mouseY", "rect", "left", "width", "top", "height", "dateInput", "timestamp", "test", "Number", "timestampMs", "date<PERSON><PERSON>j", "getTime", "setHours", "unitNames", "time", "toFixed", "minute", "hour", "day", "onStartTyping", "onStopTyping", "idleTime", "timer", "clearTimeout", "start", "setTimeout", "func", "wait", "immediate", "max<PERSON><PERSON>", "timeout", "lastInvokeTime", "args", "isFirstCall", "apply", "timeSinceLastInvoke", "shouldInvokeNow", "_this", "appliedSla", "chat", "icon", "isSlaMissed", "SLAStatuses", "mostUrgent", "frtThreshold", "sla_first_response_time_threshold", "nrtThreshold", "sla_next_response_time_threshold", "rtThreshold", "sla_resolution_time_threshold", "createdAt", "created_at", "firstReplyCreatedAt", "first_reply_created_at", "waitingSince", "waiting_since", "SLAStatus", "FRT", "condition", "NRT", "RT", "createSLAObject", "filter", "evaluateSLAConditions", "sla1", "sla2", "abs", "file", "max<PERSON><PERSON><PERSON>", "ellipsis", "fullName", "dotIndex", "lastIndexOf", "date", "todayText", "yesterdayText", "dateValue", "isToday", "isYesterday", "num", "n", "Intl", "NumberFormat", "notation", "maximumFractionDigits", "format", "timeInSeconds", "formattedTime", "minutes", "hours", "days", "bgColor", "color", "replace", "parseInt", "defaultInfo", "encodedFilename", "fileName", "decodeURIComponent", "lastDotIndex", "toLowerCase", "console", "conversation", "inbox", "meta", "assignee", "sender", "id", "custom_attributes", "conversationCustomAttributes", "contactCustomAttributes", "contact", "email", "phone_number", "entries", "reduce", "acc", "data", "intervals", "interval", "lastEmail", "conversationContact", "inboxEmail", "forwardToEmail", "to", "cc", "bcc", "isLastEmailFromContact", "isIncoming", "message_type", "INCOMING", "emailAttributes", "content_attributes", "from", "contentAttributes", "cc_emails", "bcc_emails", "to_emails", "Array", "isArray", "emailAddress", "replyUUIDPattern", "Set", "variables", "matches", "message", "trim", "variable", "url1", "url2", "urlObj1", "urlObj2", "hostname", "domain", "x", "candidate", "candidate<PERSON><PERSON>", "String", "Boolean", "JSON", "parse", "_", "trimmedName", "nameParts", "content", "<PERSON><PERSON><PERSON>nt", "substring"], "mappings": "muBA2EYA,mBCpEZ,IAAIC,EAAW,SAAUC,GAGvB,IAAIC,EAAKC,OAAOC,UACZC,EAASH,EAAGI,eAEZC,EAA4B,mBAAXC,OAAwBA,OAAS,GAClDC,EAAiBF,EAAQG,UAAY,aACrCC,EAAsBJ,EAAQK,eAAiB,kBAC/CC,EAAoBN,EAAQO,aAAe,gBAE/C,SAASC,EAAOC,EAAKC,EAAKC,GAOxB,OANAf,OAAOgB,eAAeH,EAAKC,EAAK,CAC9BC,MAAOA,EACPE,YAAY,EACZC,cAAc,EACdC,UAAU,IAELN,EAAIC,GAEb,IAEEF,EAAO,GAAI,IACX,MAAOQ,GACPR,EAAS,SAASC,EAAKC,EAAKC,GAC1B,OAAOF,EAAIC,GAAOC,GAItB,SAASM,EAAKC,EAASC,EAASC,EAAMC,GAEpC,IACIC,EAAY1B,OAAO2B,QADFJ,GAAWA,EAAQtB,qBAAqB2B,EAAYL,EAAUK,GACtC3B,WACzC4B,EAAU,IAAIC,EAAQL,GAAe,IAMzC,OAFAC,EAAUK,QAsMZ,SAA0BT,EAASE,EAAMK,GACvC,IAAIG,EA/KuB,iBAiL3B,OAAO,SAAgBC,EAAQC,GAC7B,GAhLoB,cAgLhBF,EACF,MAAM,IAAIG,MAAM,gCAGlB,GAnLoB,cAmLhBH,EAA6B,CAC/B,GAAe,UAAXC,EACF,MAAMC,EAKR,MAoQG,CAAEnB,WAzfPqB,EAyfyBC,MAAM,GA9P/B,IAHAR,EAAQI,OAASA,EACjBJ,EAAQK,IAAMA,IAED,CACX,IAAII,EAAWT,EAAQS,SACvB,GAAIA,EAAU,CACZ,IAAIC,EAAiBC,EAAoBF,EAAUT,GACnD,GAAIU,EAAgB,CAClB,GAAIA,IAAmBE,EAAkB,SACzC,OAAOF,GAIX,GAAuB,SAAnBV,EAAQI,OAGVJ,EAAQa,KAAOb,EAAQc,MAAQd,EAAQK,SAElC,GAAuB,UAAnBL,EAAQI,OAAoB,CACrC,GAnNqB,mBAmNjBD,EAEF,MADAA,EAjNc,YAkNRH,EAAQK,IAGhBL,EAAQe,kBAAkBf,EAAQK,SAEN,WAAnBL,EAAQI,QACjBJ,EAAQgB,OAAO,SAAUhB,EAAQK,KAGnCF,EA5NkB,YA8NlB,IAAIc,EAASC,EAASzB,EAASE,EAAMK,GACrC,GAAoB,WAAhBiB,EAAOE,KAAmB,CAO5B,GAJAhB,EAAQH,EAAQQ,KAjOA,YAFK,iBAuOjBS,EAAOZ,MAAQO,EACjB,SAGF,MAAO,CACL1B,MAAO+B,EAAOZ,IACdG,KAAMR,EAAQQ,MAGS,UAAhBS,EAAOE,OAChBhB,EA/OgB,YAkPhBH,EAAQI,OAAS,QACjBJ,EAAQK,IAAMY,EAAOZ,OA9QPe,CAAiB3B,EAASE,EAAMK,GAE7CH,EAcT,SAASqB,EAASG,EAAIrC,EAAKqB,GACzB,IACE,MAAO,CAAEc,KAAM,SAAUd,IAAKgB,EAAGC,KAAKtC,EAAKqB,IAC3C,MAAOd,GACP,MAAO,CAAE4B,KAAM,QAASd,IAAKd,IAhBjCtB,EAAQuB,KAAOA,EAoBf,IAOIoB,EAAmB,GAMvB,SAASb,KACT,SAASwB,KACT,SAASC,KAIT,IAAIC,EAAoB,GACxBA,EAAkBhD,GAAkB,WAClC,OAAOiD,MAGT,IAAIC,EAAWxD,OAAOyD,eAClBC,EAA0BF,GAAYA,EAASA,EAASG,EAAO,MAC/DD,GACAA,IAA4B3D,GAC5BG,EAAOiD,KAAKO,EAAyBpD,KAGvCgD,EAAoBI,GAGtB,IAAIE,EAAKP,EAA2BpD,UAClC2B,EAAU3B,UAAYD,OAAO2B,OAAO2B,GAWtC,SAASO,EAAsB5D,GAC7B,CAAC,OAAQ,QAAS,UAAU6D,SAAQ,SAAS7B,GAC3CrB,EAAOX,EAAWgC,GAAQ,SAASC,GACjC,OAAOqB,KAAKxB,QAAQE,EAAQC,SAkClC,SAAS6B,EAAcrC,EAAWsC,GAgChC,IAAIC,EAgCJV,KAAKxB,QA9BL,SAAiBE,EAAQC,GACvB,SAASgC,IACP,OAAO,IAAIF,GAAY,SAASG,EAASC,IAnC7C,SAASC,EAAOpC,EAAQC,EAAKiC,EAASC,GACpC,IAAItB,EAASC,EAASrB,EAAUO,GAASP,EAAWQ,GACpD,GAAoB,UAAhBY,EAAOE,KAEJ,CACL,IAAIsB,EAASxB,EAAOZ,IAChBnB,EAAQuD,EAAOvD,MACnB,OAAIA,GACiB,iBAAVA,GACPb,EAAOiD,KAAKpC,EAAO,WACdiD,EAAYG,QAAQpD,EAAMwD,SAASC,MAAK,SAASzD,GACtDsD,EAAO,OAAQtD,EAAOoD,EAASC,MAC9B,SAAShD,GACViD,EAAO,QAASjD,EAAK+C,EAASC,MAI3BJ,EAAYG,QAAQpD,GAAOyD,MAAK,SAASC,GAI9CH,EAAOvD,MAAQ0D,EACfN,EAAQG,MACP,SAASI,GAGV,OAAOL,EAAO,QAASK,EAAOP,EAASC,MAvBzCA,EAAOtB,EAAOZ,KAiCZmC,CAAOpC,EAAQC,EAAKiC,EAASC,MAIjC,OAAOH,EAaLA,EAAkBA,EAAgBO,KAChCN,EAGAA,GACEA,KAkHV,SAAS1B,EAAoBF,EAAUT,GACrC,IAAII,EAASK,EAAS/B,SAASsB,EAAQI,QACvC,QA1TEG,IA0TEH,EAAsB,CAKxB,GAFAJ,EAAQS,SAAW,KAEI,UAAnBT,EAAQI,OAAoB,CAE9B,GAAIK,EAAS/B,SAAiB,SAG5BsB,EAAQI,OAAS,SACjBJ,EAAQK,SArUZE,EAsUII,EAAoBF,EAAUT,GAEP,UAAnBA,EAAQI,QAGV,OAAOQ,EAIXZ,EAAQI,OAAS,QACjBJ,EAAQK,IAAM,IAAIyC,UAChB,kDAGJ,OAAOlC,EAGT,IAAIK,EAASC,EAASd,EAAQK,EAAS/B,SAAUsB,EAAQK,KAEzD,GAAoB,UAAhBY,EAAOE,KAIT,OAHAnB,EAAQI,OAAS,QACjBJ,EAAQK,IAAMY,EAAOZ,IACrBL,EAAQS,SAAW,KACZG,EAGT,IAAImC,EAAO9B,EAAOZ,IAElB,OAAM0C,EAOFA,EAAKvC,MAGPR,EAAQS,EAASuC,YAAcD,EAAK7D,MAGpCc,EAAQiD,KAAOxC,EAASyC,QAQD,WAAnBlD,EAAQI,SACVJ,EAAQI,OAAS,OACjBJ,EAAQK,SAzXVE,GAmYFP,EAAQS,SAAW,KACZG,GANEmC,GA3BP/C,EAAQI,OAAS,QACjBJ,EAAQK,IAAM,IAAIyC,UAAU,oCAC5B9C,EAAQS,SAAW,KACZG,GAoDX,SAASuC,EAAaC,GACpB,IAAIC,EAAQ,CAAEC,OAAQF,EAAK,IAEvB,KAAKA,IACPC,EAAME,SAAWH,EAAK,IAGpB,KAAKA,IACPC,EAAMG,WAAaJ,EAAK,GACxBC,EAAMI,SAAWL,EAAK,IAGxB1B,KAAKgC,WAAWC,KAAKN,GAGvB,SAASO,EAAcP,GACrB,IAAIpC,EAASoC,EAAMQ,YAAc,GACjC5C,EAAOE,KAAO,gBACPF,EAAOZ,IACdgD,EAAMQ,WAAa5C,EAGrB,SAAShB,EAAQL,GAIf8B,KAAKgC,WAAa,CAAC,CAAEJ,OAAQ,SAC7B1D,EAAYqC,QAAQkB,EAAczB,MAClCA,KAAKoC,OAAM,GA8Bb,SAAShC,EAAOiC,GACd,GAAIA,EAAU,CACZ,IAAIC,EAAiBD,EAAStF,GAC9B,GAAIuF,EACF,OAAOA,EAAe1C,KAAKyC,GAG7B,GAA6B,mBAAlBA,EAASd,KAClB,OAAOc,EAGT,IAAKE,MAAMF,EAASG,QAAS,CAC3B,IAAIC,GAAK,EAAGlB,EAAO,SAASA,IAC1B,OAASkB,EAAIJ,EAASG,QACpB,GAAI7F,EAAOiD,KAAKyC,EAAUI,GAGxB,OAFAlB,EAAK/D,MAAQ6E,EAASI,GACtBlB,EAAKzC,MAAO,EACLyC,EAOX,OAHAA,EAAK/D,WAzeTqB,EA0eI0C,EAAKzC,MAAO,EAELyC,GAGT,OAAOA,EAAKA,KAAOA,GAKvB,MAAO,CAAEA,KAAMmB,GAIjB,SAASA,IACP,MAAO,CAAElF,WAzfPqB,EAyfyBC,MAAM,GA+MnC,OA5mBAe,EAAkBnD,UAAY2D,EAAGsC,YAAc7C,EAC/CA,EAA2B6C,YAAc9C,EACzCA,EAAkB+C,YAAcvF,EAC9ByC,EACA3C,EACA,qBAaFZ,EAAQsG,oBAAsB,SAASC,GACrC,IAAIC,EAAyB,mBAAXD,GAAyBA,EAAOH,YAClD,QAAOI,IACHA,IAASlD,GAG2B,uBAAnCkD,EAAKH,aAAeG,EAAKC,QAIhCzG,EAAQ0G,KAAO,SAASH,GAQtB,OAPIrG,OAAOyG,eACTzG,OAAOyG,eAAeJ,EAAQhD,IAE9BgD,EAAOK,UAAYrD,EACnBzC,EAAOyF,EAAQ3F,EAAmB,sBAEpC2F,EAAOpG,UAAYD,OAAO2B,OAAOiC,GAC1ByC,GAOTvG,EAAQ6G,MAAQ,SAASzE,GACvB,MAAO,CAAEqC,QAASrC,IAsEpB2B,EAAsBE,EAAc9D,WACpC8D,EAAc9D,UAAUO,GAAuB,WAC7C,OAAO+C,MAETzD,EAAQiE,cAAgBA,EAKxBjE,EAAQ8G,MAAQ,SAAStF,EAASC,EAASC,EAAMC,EAAauC,QACxC,IAAhBA,IAAwBA,EAAc6C,SAE1C,IAAIC,EAAO,IAAI/C,EACb1C,EAAKC,EAASC,EAASC,EAAMC,GAC7BuC,GAGF,OAAOlE,EAAQsG,oBAAoB7E,GAC/BuF,EACAA,EAAKhC,OAAON,MAAK,SAASF,GACxB,OAAOA,EAAOjC,KAAOiC,EAAOvD,MAAQ+F,EAAKhC,WAuKjDjB,EAAsBD,GAEtBhD,EAAOgD,EAAIlD,EAAmB,aAO9BkD,EAAGtD,GAAkB,WACnB,OAAOiD,MAGTK,EAAGmD,SAAW,WACZ,MAAO,sBAkCTjH,EAAQkH,KAAO,SAASC,GACtB,IAAID,EAAO,GACX,IAAK,IAAIlG,KAAOmG,EACdD,EAAKxB,KAAK1E,GAMZ,OAJAkG,EAAKE,UAIE,SAASpC,IACd,KAAOkC,EAAKjB,QAAQ,CAClB,IAAIjF,EAAMkG,EAAKG,MACf,GAAIrG,KAAOmG,EAGT,OAFAnC,EAAK/D,MAAQD,EACbgE,EAAKzC,MAAO,EACLyC,EAQX,OADAA,EAAKzC,MAAO,EACLyC,IAsCXhF,EAAQ6D,OAASA,EAMjB7B,EAAQ7B,UAAY,CAClBiG,YAAapE,EAEb6D,MAAO,SAASyB,GAcd,GAbA7D,KAAK8D,KAAO,EACZ9D,KAAKuB,KAAO,EAGZvB,KAAKb,KAAOa,KAAKZ,WApgBjBP,EAqgBAmB,KAAKlB,MAAO,EACZkB,KAAKjB,SAAW,KAEhBiB,KAAKtB,OAAS,OACdsB,KAAKrB,SAzgBLE,EA2gBAmB,KAAKgC,WAAWzB,QAAQ2B,IAEnB2B,EACH,IAAK,IAAIb,KAAQhD,KAEQ,MAAnBgD,EAAKe,OAAO,IACZpH,EAAOiD,KAAKI,KAAMgD,KACjBT,OAAOS,EAAKgB,MAAM,MACrBhE,KAAKgD,QAnhBXnE,IAyhBFoF,KAAM,WACJjE,KAAKlB,MAAO,EAEZ,IACIoF,EADYlE,KAAKgC,WAAW,GACLG,WAC3B,GAAwB,UAApB+B,EAAWzE,KACb,MAAMyE,EAAWvF,IAGnB,OAAOqB,KAAKmE,MAGd9E,kBAAmB,SAAS+E,GAC1B,GAAIpE,KAAKlB,KACP,MAAMsF,EAGR,IAAI9F,EAAU0B,KACd,SAASqE,EAAOC,EAAKC,GAYnB,OAXAhF,EAAOE,KAAO,QACdF,EAAOZ,IAAMyF,EACb9F,EAAQiD,KAAO+C,EAEXC,IAGFjG,EAAQI,OAAS,OACjBJ,EAAQK,SApjBZE,KAujBY0F,EAGZ,IAAK,IAAI9B,EAAIzC,KAAKgC,WAAWQ,OAAS,EAAGC,GAAK,IAAKA,EAAG,CACpD,IAAId,EAAQ3B,KAAKgC,WAAWS,GACxBlD,EAASoC,EAAMQ,WAEnB,GAAqB,SAAjBR,EAAMC,OAIR,OAAOyC,EAAO,OAGhB,GAAI1C,EAAMC,QAAU5B,KAAK8D,KAAM,CAC7B,IAAIU,EAAW7H,EAAOiD,KAAK+B,EAAO,YAC9B8C,EAAa9H,EAAOiD,KAAK+B,EAAO,cAEpC,GAAI6C,GAAYC,EAAY,CAC1B,GAAIzE,KAAK8D,KAAOnC,EAAME,SACpB,OAAOwC,EAAO1C,EAAME,UAAU,GACzB,GAAI7B,KAAK8D,KAAOnC,EAAMG,WAC3B,OAAOuC,EAAO1C,EAAMG,iBAGjB,GAAI0C,GACT,GAAIxE,KAAK8D,KAAOnC,EAAME,SACpB,OAAOwC,EAAO1C,EAAME,UAAU,OAG3B,CAAA,IAAI4C,EAMT,MAAM,IAAI7F,MAAM,0CALhB,GAAIoB,KAAK8D,KAAOnC,EAAMG,WACpB,OAAOuC,EAAO1C,EAAMG,gBAU9BxC,OAAQ,SAASG,EAAMd,GACrB,IAAK,IAAI8D,EAAIzC,KAAKgC,WAAWQ,OAAS,EAAGC,GAAK,IAAKA,EAAG,CACpD,IAAId,EAAQ3B,KAAKgC,WAAWS,GAC5B,GAAId,EAAMC,QAAU5B,KAAK8D,MACrBnH,EAAOiD,KAAK+B,EAAO,eACnB3B,KAAK8D,KAAOnC,EAAMG,WAAY,CAChC,IAAI4C,EAAe/C,EACnB,OAIA+C,IACU,UAATjF,GACS,aAATA,IACDiF,EAAa9C,QAAUjD,GACvBA,GAAO+F,EAAa5C,aAGtB4C,EAAe,MAGjB,IAAInF,EAASmF,EAAeA,EAAavC,WAAa,GAItD,OAHA5C,EAAOE,KAAOA,EACdF,EAAOZ,IAAMA,EAET+F,GACF1E,KAAKtB,OAAS,OACdsB,KAAKuB,KAAOmD,EAAa5C,WAClB5C,GAGFc,KAAK2E,SAASpF,IAGvBoF,SAAU,SAASpF,EAAQwC,GACzB,GAAoB,UAAhBxC,EAAOE,KACT,MAAMF,EAAOZ,IAcf,MAXoB,UAAhBY,EAAOE,MACS,aAAhBF,EAAOE,KACTO,KAAKuB,KAAOhC,EAAOZ,IACM,WAAhBY,EAAOE,MAChBO,KAAKmE,KAAOnE,KAAKrB,IAAMY,EAAOZ,IAC9BqB,KAAKtB,OAAS,SACdsB,KAAKuB,KAAO,OACa,WAAhBhC,EAAOE,MAAqBsC,IACrC/B,KAAKuB,KAAOQ,GAGP7C,GAGT0F,OAAQ,SAAS9C,GACf,IAAK,IAAIW,EAAIzC,KAAKgC,WAAWQ,OAAS,EAAGC,GAAK,IAAKA,EAAG,CACpD,IAAId,EAAQ3B,KAAKgC,WAAWS,GAC5B,GAAId,EAAMG,aAAeA,EAGvB,OAFA9B,KAAK2E,SAAShD,EAAMQ,WAAYR,EAAMI,UACtCG,EAAcP,GACPzC,IAKb2F,MAAS,SAASjD,GAChB,IAAK,IAAIa,EAAIzC,KAAKgC,WAAWQ,OAAS,EAAGC,GAAK,IAAKA,EAAG,CACpD,IAAId,EAAQ3B,KAAKgC,WAAWS,GAC5B,GAAId,EAAMC,SAAWA,EAAQ,CAC3B,IAAIrC,EAASoC,EAAMQ,WACnB,GAAoB,UAAhB5C,EAAOE,KAAkB,CAC3B,IAAIqF,EAASvF,EAAOZ,IACpBuD,EAAcP,GAEhB,OAAOmD,GAMX,MAAM,IAAIlG,MAAM,0BAGlBmG,cAAe,SAAS1C,EAAUf,EAAYE,GAa5C,OAZAxB,KAAKjB,SAAW,CACd/B,SAAUoD,EAAOiC,GACjBf,WAAYA,EACZE,QAASA,GAGS,SAAhBxB,KAAKtB,SAGPsB,KAAKrB,SA7rBPE,GAgsBOK,IAQJ3C,GAOsByI,EAAOzI,SAGtC,IACE0I,mBAAqB3I,EACrB,MAAO4I,GAUPC,SAAS,IAAK,yBAAdA,CAAwC7I,gCC1hB7B8I,6BAAe,gHAE1B3F,IAAAA,KACA4F,gBAAAA,WAAY,QAFZC,IAAAA,MAIa7F,wBACL,IAAIb,MAAM,+DAIO2G,MAAMD,EAAK,CAAEE,MAAO,wBAArCC,UAEQC,yBACN,IAAI9G,0BAA0B6G,EAASE,gCAGxBF,EAASG,eAA1BC,SAEAC,EAAcL,EAASM,QAAQC,IAAI,gBAEnCC,EACJZ,IAAcS,EAAcA,EAAYI,MAAM,KAAK,GAAKzG,GAEpD0G,EAAoBV,EAASM,QAAQC,IAAI,uBACzCI,QAAgBD,SAAAA,EAAmBE,MAAM,oBAEzCC,iBACJF,SAAAA,EAAgB,oBAAoBG,KAAKC,UAASP,EAE9CQ,EAAUC,IAAIC,gBAAgBd,GAC9Be,EAAOnK,OAAOoK,OAAOC,SAASC,cAAc,KAAM,CACtDC,KAAMP,EACNQ,SAAUX,EACVY,MAAO,kBAGTJ,SAASK,KAAKC,OAAOR,GACrBA,EAAKS,QACLT,EAAKU,SACLZ,IAAIa,gBAAgBd,2DAEde,gBAAiB5I,WAAgB,IAAIA,MAAM,mIC/OxC6I,EAAQ,SAACC,UACfA,EACDA,aAAiBhB,IAAYgB,EAGd,iBAAVA,GACNA,EAAMC,SAAS,QACfD,EAAME,WAAW,KAKC,iBAAVF,GAAsBA,EAAME,WAAW,KACzC,IAAIlB,IAAIgB,EAAOG,OAAOC,SAASC,QAGjC,IAAIrB,IAAIgB,GAPN,IAAIhB,eAAegB,GART,eCNLM,EAAQC,UAEfA,EAAIjE,QAAQkE,MAAK,SAACC,EAAGC,UAAMD,EAAIC,KAqBxC,SAAgBC,EAAMC,EAAaC,EAAa/K,UAC1CA,EAAQ8K,EACHA,EAEL9K,EAAQ+K,EACHA,EAEF/K,EAWT,SAASgL,EAAmBC,EAAkBC,OACtCC,EAAUN,EAAM,EAAG,EAAGK,GACtBE,GAAOH,EAAOjG,OAAS,GAAKmG,EAC5BE,EAAOC,KAAKC,MAAMH,eAMC/J,IAArB4J,EAAOI,EAAO,GAETJ,EAAOI,IAPHD,EAAMC,IAOaJ,EAAOI,EAAO,GAAKJ,EAAOI,IAInDJ,EAAOI,IJchB,SAAYxM,GACVA,2BACAA,2BACAA,2BACAA,2BAJF,CAAYA,IAAAA,OILZ,IC9DM2M,EAA0B,aAInBC,EAAiB,SAACjG,UACxBA,EAEEA,EACJkD,MAAM,KACNgD,KAAI,SAAAC,UACEA,EAIEA,EAAKpF,OAAO,GAAGqF,cAAgBD,EAAKnF,MAAM,GAJ/B,MAMnBqF,KAAK,KAXU,IAcPC,EAAe,gBAAGC,IAAAA,KACvBC,QAAYD,GAAAA,EAAMvG,KAAOuG,EAAKvG,KAAKkD,MAAM,KAAKuD,QAAU,UACvDR,EAAeO,IAGXE,EAAc,gBAAGH,IAAAA,QACxBA,GAAQA,EAAKvG,KAAM,KACf2G,EACJJ,EAAKvG,KAAKkD,MAAM,KAAK1D,OAAS,EAAI+G,EAAKvG,KAAKkD,MAAM,KAAKtC,MAAQ,UAC1DqF,EAAeU,SAEjB,IC7BHC,EAAqB,SACzBC,EACAC,UAGkB,OAAdA,EAA2B,KAExBD,EAAaC,EADAhB,KAAKC,MAAMxC,KAAKC,MAAQ,MAsBxCuD,EAAgB,SAACC,OACfC,EAAmC,CACvCC,EAAG,QACHC,GAAI,OACJC,EAAG,MACHC,EAAG,KACHC,EAAG,OAGDN,EAAU,SACL,SAIHO,EAAkB,UAExB9N,OAAOgH,KAAKwG,GAAO1J,SAAQ,SAAAiK,OACnBhN,EAAQsL,KAAKC,MAAMiB,EAAUC,EAAMO,IACrCR,EAAU,IAAMO,EAAM/H,OAAS,GACd,IAAjB+H,EAAM/H,QACNhF,EAAQ,IACV+M,EAAMtI,KAAKzE,EAAQgN,GACnBR,GAAWxM,EAAQyM,EAAMO,OAGtBD,EAAMlB,KAAK,qCFuDkB,SACpCoB,EACAC,EACAC,OAEMC,EAAWD,EAAQ7B,KAAK+B,GAAM,IAC9BC,EAAMhC,KAAKgC,KAAKF,GAChBG,EAAMjC,KAAKiC,KAAKH,SAEf,CACLI,SAAUP,EAAYK,EAAMJ,EAAYK,EACxCE,SAAUR,EAAYM,EAAML,EAAYI,kCAvCP,SACnCI,EACAC,EACAC,SAKO,CACLX,UAAWS,GAJGE,EAAKC,KAAOD,EAAKE,MAAQ,GAKvCZ,UAAWS,GAJGC,EAAKG,IAAMH,EAAKI,OAAS,0CGvEf,SAC1BC,MAEiB,MAAbA,EAAmB,OAAO,SAE1BC,EAAiC,iBAAdD,EAAyBA,EAAY,QAI5C,OAAdC,GACqB,iBAAdD,GACP,QAAQE,KAAKF,KAEbC,EAAYE,OAAOH,IAIH,OAAdC,EAAoB,KAEhBG,EAC4B,KAAhCH,EAAUlI,WAAWhB,OAA4B,IAAZkJ,EAAmBA,SACnD,IAAInF,KAAKsF,MAIO,iBAAdJ,EAAwB,KAC3BK,EAAU,IAAIvF,KAAKkF,UAGrBG,OAAOrJ,MAAMuJ,EAAQC,WAAmB,MAK1C,wBAAwBJ,KAAKF,IAAc,cAAcE,KAAKF,IAE9DK,EAAQE,SAAS,EAAG,EAAG,EAAG,GAGrBF,UAGF,uCL4C+B,SACtC9B,EACAiC,UAEgB,OAAZjC,GAAgC,IAAZA,EAAsB,CAAEkC,KAAM,GAAI1B,KAAM,IAC5DR,EAAU,KACL,CAAEkC,KAAMN,QAAQ5B,EAAU,IAAImC,QAAQ,IAAK3B,KAAMyB,EAAUG,QAChEpC,EAAU,MACL,CAAEkC,KAAMN,QAAQ5B,EAAU,MAAMmC,QAAQ,IAAK3B,KAAMyB,EAAUI,MAC/D,CAAEH,KAAMN,QAAQ5B,EAAU,OAAOmC,QAAQ,IAAK3B,KAAMyB,EAAUK,oCM5GlC,SACnCC,EACAC,EACAC,OAEIC,EAAwB,KAStBzI,EAAO,WACPyI,IACFC,aAAaD,GACbA,EAAQ,KACRF,YAaG,CAAEI,MAxBK,WACPF,GACHH,IAcEG,GACFC,aAAaD,GAEfA,EAAQG,YAAW,WACjB5I,MACCwI,IAGWxI,KAAAA,qBCbM,SACtB6I,EACAC,EACAC,EACAC,OAEIC,EAAgD,KAChDC,EAAiB,SAEd,6CAAuBC,2BAAAA,sBACtBlB,EAAO3F,KAAKC,MACZ6G,EAAiC,IAAnBF,KAGhBE,GAAeL,SACjBG,EAAiBjB,OACjBY,EAAKQ,MAAMtN,KAAMoN,GAKH,OAAZF,IACFP,aAAaO,GACbA,EAAU,UAINK,EAAsBrB,EAAOiB,EAC7BK,OACQ3O,IAAZoO,GAAyBM,GAAuBN,KAE9CO,SACFL,EAAiBjB,OACjBY,EAAKQ,MAAMtN,KAAMoN,GAKnBF,EAAUL,YAAW,WACnBM,EAAiB5G,KAAKC,MACtB0G,EAAU,KACVJ,EAAKQ,MAAMG,EAAML,KAChBL,sDHoF0B,gBAC/BW,IAAAA,WACAC,IAAAA,SAKKD,IAAeC,EAClB,MAAO,CAAElO,KAAM,GAAIqK,UAAW,GAAI8D,KAAM,GAAIC,aAAa,OA3I5BC,EAiJzBC,IAjJyBD,EAmGH,SAC5BJ,EACAC,SAQiB,CAAC,MAAO,MAAO,MAChBzE,KAAI,SAAAzJ,UA9DE,SACtBA,EACAiO,EACAC,OAGqCK,EAIjCN,EAJFO,kCACkCC,EAGhCR,EAHFS,iCAC+BC,EAE7BV,EAFFW,8BACYC,EACVZ,EADFa,WAIwBC,EAGtBb,EAHFc,uBACeC,EAEbf,EAFFgB,cACAhJ,EACEgI,EADFhI,OA0BIiJ,EArBF,CACFC,IAAK,CACH/E,UAAWF,EAAmB0E,EAAWN,GAEzCc,UACmB,OAAjBd,KACEQ,GAA+C,IAAxBA,IAE7BO,IAAK,CACHjF,UAAWF,EAAmB8E,EAAcR,GAE5CY,UACmB,OAAjBZ,KAA2BM,KAAyBE,GAExDM,GAAI,CACFlF,UAAWF,EAAmB0E,EAAWF,GAEzCU,UAAsB,SAAXnJ,GAAqC,OAAhByI,IAIT3O,UACpBmP,OAAiBA,GAAWnP,KAAAA,IAAS,KAoBhBwP,CAAgBxP,EAAMiO,EAAYC,MAC3DuB,QACC,SACEN,WAEEA,GAAaA,EAAUE,aAE5B5F,KAAI,SAAA0F,eACAA,GACHhB,KAAMgB,EAAU9E,WAAa,EAAI,QAAU,QAC3C+D,YAAae,EAAU9E,WAAa,OAsBpBqF,CAAsBzB,EAAYC,IA5I1CzF,MACV,SAACkH,EAAMC,UAASvG,KAAKwG,IAAIF,EAAKtF,WAAahB,KAAKwG,IAAID,EAAKvF,cAEpDgE,EAAY,WA6IZC,EACH,CACEtO,WAAMsO,SAAAA,EAAYtO,KAClBqK,UAAWC,EACTgE,EAAWjE,WAAa,GACnBiE,EAAWjE,UACZiE,EAAWjE,WAEjB8D,KAAMG,EAAWH,KACjBC,YAAaE,EAAWF,aAE1B,CAAEpO,KAAM,GAAIqK,UAAW,GAAI8D,KAAM,GAAIC,aAAa,iCJhDpB,SAClC0B,EACAC,EACAC,oBADAD,IAAAA,EAAoB,aACpBC,IAAAA,EAAmB,SAEbC,0BAAWH,SAAAA,EAAMjJ,kBAAYiJ,SAAAA,EAAMvM,QAAQ,WAE3C2M,EAAWD,EAASE,YAAY,SACpB,IAAdD,EAAiB,OAAOD,QAEF,CACxBA,EAAS1L,MAAM,EAAG2L,GAClBD,EAAS1L,MAAM2L,IAFV3M,OAAMqC,cAKTrC,EAAKR,QAAUgN,EAAkBE,KAE3B1M,EAAKgB,MAAM,EAAGwL,GAAaC,EAAWpK,sBA7HxB,gBACxBwK,IAAAA,KACAC,IAAAA,UACAC,IAAAA,cAMMC,EAAY,IAAIzJ,KAAKsJ,UACvBI,EAAQD,GAAmBF,EAC3BI,EAAYF,GAAmBD,EAC5BF,wBA0SmB,SAC1BM,OAEMC,EAAIxE,OAAOuE,IAAQ,SAClB,IAAIE,KAAKC,aAAa,KAAM,CACjCC,SAAU,UACVC,sBAAuB,IACMC,OAAOL,uBAvSd,SAACM,OACrBC,EAAgB,MAChBD,GAAiB,IAAMA,EAAgB,KAAM,KACzCE,EAAU9H,KAAKC,MAAM2H,EAAgB,IAC3CC,EAAmBC,aACb5G,EAAsB,KAAZ4G,EAAiB,EAAI9H,KAAKC,MAAM2H,EAAgB,WACzDC,GAAmB3G,EAAU,EAAI,IAAMA,EAAU,OAAS,OAE/D0G,GAAiB,MAAQA,EAAgB,MAAO,KAC5CG,EAAQ/H,KAAKC,MAAM2H,EAAgB,MACzCC,EAAmBE,YACbD,EACJF,EAAgB,KAAO,IAAgB,KAAVG,EACzB,EACA/H,KAAKC,MAAO2H,EAAgB,KAAQ,WACnCC,GAAmBC,EAAU,EAAI,IAAMA,EAAU,OAAS,OAE/DF,GAAiB,MAAO,KACpBI,EAAOhI,KAAKC,MAAM2H,EAAgB,OACxCC,EAAmBG,aACbD,EACJH,EAAgB,MAAQ,MAAQI,GAAQ,IACpC,EACAhI,KAAKC,MAAO2H,EAAgB,MAAS,aACpCC,GAAmBE,EAAQ,EAAI,IAAMA,EAAQ,MAAQ,WAEpD/H,KAAKC,MAAM2H,2CAjEgB,SAACK,OAChCC,EAAQD,EAAQE,QAAQ,IAAK,UAKxB,KAJDC,SAASF,EAAMhN,MAAM,EAAG,GAAI,IAIf,KAHbkN,SAASF,EAAMhN,MAAM,EAAG,GAAI,IAGH,KAFzBkN,SAASF,EAAMhN,MAAM,EAAG,GAAI,IAEK,IAAM,UAAY,+BAyQpC,SAACsB,OACpB6L,EAAwB,CAC5BnO,KAAM,eACNvD,KAAM,GACNoJ,KAAM,oBAGHvD,GAAsB,iBAARA,SACV6L,UASDC,EAJW9L,EACdY,MAAM,QAAQ,GACd+K,QAAQ,MAAO,KAEe/K,MAAM,KAAKtC,UACvCwN,SACID,MAGHE,EAAWC,mBAAmBF,MAGhCC,EAASzJ,WAAW,OAASyJ,EAAS1J,SAAS,IAAK,SAC/C,CAAE3E,KAAMqO,EAAU5R,KAAM,GAAIoJ,KAAMwI,OAKrCE,EAAeF,EAASzB,YAAY,SACpB,IAAlB2B,GAAwC,IAAjBA,QAClB,CAAEvO,KAAMqO,EAAU5R,KAAM,GAAIoJ,KAAMwI,OAGrCxI,EAAOwI,EAASrN,MAAM,EAAGuN,GACzB9R,EAAO4R,EAASrN,MAAMuN,EAAe,GAAGC,oBAEvC,CAAExO,KAAMqO,EAAU5R,KAAAA,EAAMoJ,KAAAA,GAC/B,MAAO1H,UACPsQ,QAAQtQ,MAAM,8BAA+BA,GACtCgQ,gCGxRwB,kBACjCO,IAAAA,aAEAC,IAAAA,QAUID,EAHFE,KAAQC,IAAAA,SAAUC,IAAAA,OAClBC,EAEEL,EAFFK,KAEEL,EADFM,kBAAmBC,aAA+B,KAEzBC,KAZ3BC,SAYkE,IAA1DH,8BAEkB,gBACR/I,SAAe6I,SAAAA,EAAQ9O,OAAQ,yBACzBsG,EAAa,CAAEC,KAAMuI,wBACtBpI,EAAY,CAAEH,KAAMuI,0BACxBA,SAAAA,EAAQM,4BACRN,SAAAA,EAAQO,gCACXP,SAAAA,EAAQC,qBACHA,mBACPJ,SAAAA,EAAOI,sBACLJ,SAAAA,EAAO3O,kBACPiG,SAAe4I,SAAAA,EAAU7O,OAAQ,uBAC3BsG,EAAa,CAAEC,KAAMsI,sBACtBnI,EAAY,CAAEH,KAAMsI,iCACxBA,SAAAA,EAAUO,SAAS,IAES3V,OAAO6V,cAClDL,EAAAA,EAAgC,IAChCM,QAAO,SAACC,YACRA,8CACOA,IACN,IAEqC/V,OAAO6V,cAC7CJ,EAAAA,EAA2B,IAC3BK,QAAO,SAACC,YACRA,yCACOA,IACN,mCDd+B,SAACC,EAAgBC,OAI7CjK,EAAST,EAAQyK,UAEhBC,EAAUxJ,KAAI,SAAAyJ,UACZnK,EAAmBC,EAAQkK,sCMrEpCC,EACAC,EACAC,EACAC,SAEIC,EAAK,GACLC,EAAK,GACLC,EAAM,OAGLN,QACI,CAAEI,GAAAA,EAAIC,GAAAA,EAAIC,IAAAA,OA6CfC,YAvCEC,EAFgCR,EAA9BS,eAE2BhX,EAAYiX,SAE3CC,EAAkB,MAOlBH,EAAY,KAIRhB,EADFQ,EADFY,mBAE8BpB,MAChCmB,EAAkB,CAChBN,UAAIb,SAAAA,EAAOa,KAAM,GACjBC,WAAKd,SAAAA,EAAOc,MAAO,GACnBO,YAAMrB,SAAAA,EAAOqB,OAAQ,GACrBT,GAAI,QAED,KAEiBU,EAClBd,EADFY,2BAOEE,EAAAA,EAAqB,OAHvBC,cACAC,eACAC,UAGFN,EAAkB,CAChBN,cANsB,KAOtBC,eANwB,KAOxBF,cANsB,KAOtBS,KAAM,IAMVN,EACEC,aAAeG,EAAgBE,QAAQ,IAAI9L,SAASkL,GAElDO,KAEFJ,GAAG/Q,sBAASsR,EAAgBE,QAAQ,OAIpCT,GAAG/Q,sBAASsR,EAAgBP,MAAM,CAACH,IAKrCI,EAAKM,EAAgBN,aAASM,EAAgBN,IAAM,GAEhDa,MAAMC,QAAQR,EAAgBP,KAAOI,MACvCH,GAAGhR,aAAQsR,EAAgBP,KAKxBG,GAA0BC,GAC7BH,EAAGhR,KAAK4Q,GAIVK,GAAOK,EAAgBL,KAAO,IAAIhE,QAChC,SAAA8E,UAAgBA,IAAiBnB,SAO7BoB,EAAmB,+GACzBhB,EAAKA,EAAG/D,QAAO,SAAAkD,WACTA,IAAUS,GAAuBM,GAGjCf,IAAUU,GAAcV,IAAUW,GAGlCkB,EAAiBtI,KAAKyG,OAM5Bc,EAAMA,EAAIhE,QAAO,SAAAkD,UAEbA,IAAUU,GACVV,IAAUW,IACVkB,EAAiBtI,KAAKyG,MAanB,CACLY,GALFA,EAAKc,MAAML,KAAK,IAAIS,IAAIlB,IAMtBC,GALFA,EAAKa,MAAML,KAAK,IAAIS,IAAIjB,IAMtBC,IALFA,EAAMY,MAAML,KAAK,IAAIS,IAAIhB,6CLpBmB,gBAE5CiB,IAAAA,UAMMC,IAPNC,QArG0CpD,QAAQ,oBAAqB,IA4G9B5K,MAAM2C,UAC1CoL,EAEEA,EACJlL,KAAI,SAAA7C,UACIA,EACJ4K,QAAQ,KAAM,IACdA,QAAQ,KAAM,IACdqD,UAEJpF,QAAO,SAAAqF,eACyB1V,IAAxBsV,EAAUI,MAVA,uBFlFG,SACxBC,EACAC,WAGQC,EAAUjN,EAAM+M,GAChBG,EAAUlN,EAAMgN,YAEjBC,IAAYC,IAEVD,EAAQE,WAAaD,EAAQC,SACpC,MAAOzT,UACA,0BAWkB,SAAC0T,SACb,KAAXA,GAEgB,o4sBAEDlJ,KAAKkJ,IAAWA,EAAOrS,QAAU,mCC6EjB,SACnCwI,EACAC,EACAK,EACAE,SAMO,CACLsJ,EAAGhM,KAAKP,IAAI,EAAGO,KAAKR,IAAI,IAAK,GAAM0C,GAAYM,EAAQ,GAAM,KAC7DpB,EAAGpB,KAAKP,IAAI,EAAGO,KAAKR,IAAI,IAAK,GAAM2C,GAAYO,EAAS,GAAM,qCOpJrCuJ,WAGnBC,EAAkBC,OAAOF,GAAWvD,qBAInC0D,QAAQC,KAAKC,MAAMJ,IAC1B,MAAO7T,UACA,8BPCc8G,EAAeS,UAE/BF,EADQR,EAAQC,GACWS,sCC6EK,gBACvC2L,IAAAA,QACAF,IAAAA,uBAMOE,SAAAA,EAASpD,QAAQjI,GAAyB,SAACqM,EAAGpE,UAC5CkD,EAAUlD,EAAQqD,QACrBH,EAAUlD,EAAQqD,OAAO9C,eACzB,2CH0DiB,SACvB9B,OAEM4F,EAAc5F,EAAS4E,WACxBgB,QACI,CACL9L,UAAW,GACXG,SAAU,QAKR4L,EAAYD,EAAYpP,MAAM,UAGX,IAArBqP,EAAU/S,aACL,CACLgH,UAAW+L,EAAU,GACrB5L,SAAU,QAKRA,EAAW4L,EAAU3R,OAAS,SAG7B,CAAE4F,UAFS+L,EAAUlM,KAAK,KAEbM,SAAAA,wCA1GK,SACzB6L,EACAhG,EACAC,YAFA+F,IAAAA,EAAkB,aAClBhG,IAAAA,EAAoB,eACpBC,IAAAA,GAAoB,OAEhBgG,EAAiBD,SACjBA,EAAQhT,OAASgN,IACnBiG,EAAiBD,EAAQE,UAAU,EAAGlG,IAEpCC,IACFgG,GAAkC,OAE7BA"}