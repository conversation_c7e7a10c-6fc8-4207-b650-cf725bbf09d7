{"version": 3, "file": "utils.cjs.development.js", "sources": ["../src/debounce.ts", "../node_modules/regenerator-runtime/runtime.js", "../src/helpers.ts", "../src/url.ts", "../src/types/message.ts", "../src/email.ts", "../src/string.ts", "../src/math.ts", "../src/canned.ts", "../src/typingStatus.ts", "../src/sla.ts", "../src/date.ts"], "sourcesContent": ["/**\n * Creates a debounced version of a function that delays invoking the provided function\n * until after a specified wait time has elapsed since the last time it was invoked.\n *\n * @param {Function} func - The function to debounce. Will receive any arguments passed to the debounced function.\n * @param {number} wait - The number of milliseconds to delay execution after the last call.\n * @param {boolean} [immediate] - If true, the function will execute immediately on the first call,\n *                               then start the debounce behavior for subsequent calls.\n * @param {number} [maxWait] - The maximum time the function can be delayed before it's forcibly executed.\n *                            If specified, the function will be called after this many milliseconds\n *                            have passed since its last execution, regardless of the debounce wait time.\n *\n * @returns {Function} A debounced version of the original function that has the following behavior:\n *   - Delays execution until `wait` milliseconds have passed since the last call\n *   - If `immediate` is true, executes on the leading edge of the first call\n *   - If `maxWait` is provided, ensures the function is called at least once every `maxWait` milliseconds\n *   - Preserves the `this` context and arguments of the most recent call\n *   - Cancels pending executions when called again within the wait period\n *\n * @example\n * // Basic debounce\n * const debouncedSearch = debounce(searchAPI, 300);\n *\n * // With immediate execution\n * const debouncedSave = debounce(saveData, 1000, true);\n *\n * // With maximum wait time\n * const debouncedUpdate = debounce(updateUI, 200, false, 1000);\n */\nexport const debounce = (\n  func: (...args: any[]) => void,\n  wait: number,\n  immediate?: boolean,\n  maxWait?: number\n) => {\n  let timeout: ReturnType<typeof setTimeout> | null = null;\n  let lastInvokeTime = 0;\n\n  return function(this: any, ...args: any[]) {\n    const time = Date.now();\n    const isFirstCall = lastInvokeTime === 0;\n\n    // Check if this is the first call and immediate execution is requested\n    if (isFirstCall && immediate) {\n      lastInvokeTime = time;\n      func.apply(this, args);\n      return;\n    }\n\n    // Clear any existing timeout\n    if (timeout !== null) {\n      clearTimeout(timeout);\n      timeout = null;\n    }\n\n    // Calculate if maxWait threshold has been reached\n    const timeSinceLastInvoke = time - lastInvokeTime;\n    const shouldInvokeNow =\n      maxWait !== undefined && timeSinceLastInvoke >= maxWait;\n\n    if (shouldInvokeNow) {\n      lastInvokeTime = time;\n      func.apply(this, args);\n      return;\n    }\n\n    // Set a new timeout\n    timeout = setTimeout(() => {\n      lastInvokeTime = Date.now();\n      timeout = null;\n      func.apply(this, args);\n    }, wait);\n  };\n};\n", "/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar runtime = (function (exports) {\n  \"use strict\";\n\n  var Op = Object.prototype;\n  var hasOwn = Op.hasOwnProperty;\n  var undefined; // More compressible than void 0.\n  var $Symbol = typeof Symbol === \"function\" ? Symbol : {};\n  var iteratorSymbol = $Symbol.iterator || \"@@iterator\";\n  var asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\";\n  var toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n\n  function define(obj, key, value) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n    return obj[key];\n  }\n  try {\n    // IE 8 has a broken Object.defineProperty that only works on DOM objects.\n    define({}, \"\");\n  } catch (err) {\n    define = function(obj, key, value) {\n      return obj[key] = value;\n    };\n  }\n\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;\n    var generator = Object.create(protoGenerator.prototype);\n    var context = new Context(tryLocsList || []);\n\n    // The ._invoke method unifies the implementations of the .next,\n    // .throw, and .return methods.\n    generator._invoke = makeInvokeMethod(innerFn, self, context);\n\n    return generator;\n  }\n  exports.wrap = wrap;\n\n  // Try/catch helper to minimize deoptimizations. Returns a completion\n  // record like context.tryEntries[i].completion. This interface could\n  // have been (and was previously) designed to take a closure to be\n  // invoked without arguments, but in all the cases we care about we\n  // already have an existing method we want to call, so there's no need\n  // to create a new function object. We can even get away with assuming\n  // the method takes exactly one argument, since that happens to be true\n  // in every case, so we don't have to touch the arguments object. The\n  // only additional allocation required is the completion record, which\n  // has a stable shape and so hopefully should be cheap to allocate.\n  function tryCatch(fn, obj, arg) {\n    try {\n      return { type: \"normal\", arg: fn.call(obj, arg) };\n    } catch (err) {\n      return { type: \"throw\", arg: err };\n    }\n  }\n\n  var GenStateSuspendedStart = \"suspendedStart\";\n  var GenStateSuspendedYield = \"suspendedYield\";\n  var GenStateExecuting = \"executing\";\n  var GenStateCompleted = \"completed\";\n\n  // Returning this object from the innerFn has the same effect as\n  // breaking out of the dispatch switch statement.\n  var ContinueSentinel = {};\n\n  // Dummy constructor functions that we use as the .constructor and\n  // .constructor.prototype properties for functions that return Generator\n  // objects. For full spec compliance, you may wish to configure your\n  // minifier not to mangle the names of these two functions.\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n\n  // This is a polyfill for %IteratorPrototype% for environments that\n  // don't natively support it.\n  var IteratorPrototype = {};\n  IteratorPrototype[iteratorSymbol] = function () {\n    return this;\n  };\n\n  var getProto = Object.getPrototypeOf;\n  var NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  if (NativeIteratorPrototype &&\n      NativeIteratorPrototype !== Op &&\n      hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {\n    // This environment has a native %IteratorPrototype%; use it instead\n    // of the polyfill.\n    IteratorPrototype = NativeIteratorPrototype;\n  }\n\n  var Gp = GeneratorFunctionPrototype.prototype =\n    Generator.prototype = Object.create(IteratorPrototype);\n  GeneratorFunction.prototype = Gp.constructor = GeneratorFunctionPrototype;\n  GeneratorFunctionPrototype.constructor = GeneratorFunction;\n  GeneratorFunction.displayName = define(\n    GeneratorFunctionPrototype,\n    toStringTagSymbol,\n    \"GeneratorFunction\"\n  );\n\n  // Helper for defining the .next, .throw, and .return methods of the\n  // Iterator interface in terms of a single ._invoke method.\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      define(prototype, method, function(arg) {\n        return this._invoke(method, arg);\n      });\n    });\n  }\n\n  exports.isGeneratorFunction = function(genFun) {\n    var ctor = typeof genFun === \"function\" && genFun.constructor;\n    return ctor\n      ? ctor === GeneratorFunction ||\n        // For the native GeneratorFunction constructor, the best we can\n        // do is to check its .name property.\n        (ctor.displayName || ctor.name) === \"GeneratorFunction\"\n      : false;\n  };\n\n  exports.mark = function(genFun) {\n    if (Object.setPrototypeOf) {\n      Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);\n    } else {\n      genFun.__proto__ = GeneratorFunctionPrototype;\n      define(genFun, toStringTagSymbol, \"GeneratorFunction\");\n    }\n    genFun.prototype = Object.create(Gp);\n    return genFun;\n  };\n\n  // Within the body of any async function, `await x` is transformed to\n  // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test\n  // `hasOwn.call(value, \"__await\")` to determine if the yielded value is\n  // meant to be awaited.\n  exports.awrap = function(arg) {\n    return { __await: arg };\n  };\n\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (record.type === \"throw\") {\n        reject(record.arg);\n      } else {\n        var result = record.arg;\n        var value = result.value;\n        if (value &&\n            typeof value === \"object\" &&\n            hasOwn.call(value, \"__await\")) {\n          return PromiseImpl.resolve(value.__await).then(function(value) {\n            invoke(\"next\", value, resolve, reject);\n          }, function(err) {\n            invoke(\"throw\", err, resolve, reject);\n          });\n        }\n\n        return PromiseImpl.resolve(value).then(function(unwrapped) {\n          // When a yielded Promise is resolved, its final value becomes\n          // the .value of the Promise<{value,done}> result for the\n          // current iteration.\n          result.value = unwrapped;\n          resolve(result);\n        }, function(error) {\n          // If a rejected Promise was yielded, throw the rejection back\n          // into the async generator function so it can be handled there.\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n    }\n\n    var previousPromise;\n\n    function enqueue(method, arg) {\n      function callInvokeWithMethodAndArg() {\n        return new PromiseImpl(function(resolve, reject) {\n          invoke(method, arg, resolve, reject);\n        });\n      }\n\n      return previousPromise =\n        // If enqueue has been called before, then we want to wait until\n        // all previous Promises have been resolved before calling invoke,\n        // so that results are always delivered in the correct order. If\n        // enqueue has not been called before, then it is important to\n        // call invoke immediately, without waiting on a callback to fire,\n        // so that the async generator function has the opportunity to do\n        // any necessary setup in a predictable way. This predictability\n        // is why the Promise constructor synchronously invokes its\n        // executor callback, and why async functions synchronously\n        // execute code before the first await. Since we implement simple\n        // async functions in terms of async generators, it is especially\n        // important to get this right, even though it requires care.\n        previousPromise ? previousPromise.then(\n          callInvokeWithMethodAndArg,\n          // Avoid propagating failures to Promises returned by later\n          // invocations of the iterator.\n          callInvokeWithMethodAndArg\n        ) : callInvokeWithMethodAndArg();\n    }\n\n    // Define the unified helper method that is used to implement .next,\n    // .throw, and .return (see defineIteratorMethods).\n    this._invoke = enqueue;\n  }\n\n  defineIteratorMethods(AsyncIterator.prototype);\n  AsyncIterator.prototype[asyncIteratorSymbol] = function () {\n    return this;\n  };\n  exports.AsyncIterator = AsyncIterator;\n\n  // Note that simple async functions are implemented on top of\n  // AsyncIterator objects; they just return a Promise for the value of\n  // the final result produced by the iterator.\n  exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    if (PromiseImpl === void 0) PromiseImpl = Promise;\n\n    var iter = new AsyncIterator(\n      wrap(innerFn, outerFn, self, tryLocsList),\n      PromiseImpl\n    );\n\n    return exports.isGeneratorFunction(outerFn)\n      ? iter // If outerFn is a generator, return the full iterator.\n      : iter.next().then(function(result) {\n          return result.done ? result.value : iter.next();\n        });\n  };\n\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = GenStateSuspendedStart;\n\n    return function invoke(method, arg) {\n      if (state === GenStateExecuting) {\n        throw new Error(\"Generator is already running\");\n      }\n\n      if (state === GenStateCompleted) {\n        if (method === \"throw\") {\n          throw arg;\n        }\n\n        // Be forgiving, per 25.3.3.3.3 of the spec:\n        // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume\n        return doneResult();\n      }\n\n      context.method = method;\n      context.arg = arg;\n\n      while (true) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n\n        if (context.method === \"next\") {\n          // Setting context._sent for legacy support of Babel's\n          // function.sent implementation.\n          context.sent = context._sent = context.arg;\n\n        } else if (context.method === \"throw\") {\n          if (state === GenStateSuspendedStart) {\n            state = GenStateCompleted;\n            throw context.arg;\n          }\n\n          context.dispatchException(context.arg);\n\n        } else if (context.method === \"return\") {\n          context.abrupt(\"return\", context.arg);\n        }\n\n        state = GenStateExecuting;\n\n        var record = tryCatch(innerFn, self, context);\n        if (record.type === \"normal\") {\n          // If an exception is thrown from innerFn, we leave state ===\n          // GenStateExecuting and loop back for another invocation.\n          state = context.done\n            ? GenStateCompleted\n            : GenStateSuspendedYield;\n\n          if (record.arg === ContinueSentinel) {\n            continue;\n          }\n\n          return {\n            value: record.arg,\n            done: context.done\n          };\n\n        } else if (record.type === \"throw\") {\n          state = GenStateCompleted;\n          // Dispatch the exception by looping back around to the\n          // context.dispatchException(context.arg) call above.\n          context.method = \"throw\";\n          context.arg = record.arg;\n        }\n      }\n    };\n  }\n\n  // Call delegate.iterator[context.method](context.arg) and handle the\n  // result, either by returning a { value, done } result from the\n  // delegate iterator, or by modifying context.method and context.arg,\n  // setting context.delegate to null, and returning the ContinueSentinel.\n  function maybeInvokeDelegate(delegate, context) {\n    var method = delegate.iterator[context.method];\n    if (method === undefined) {\n      // A .throw or .return when the delegate iterator has no .throw\n      // method always terminates the yield* loop.\n      context.delegate = null;\n\n      if (context.method === \"throw\") {\n        // Note: [\"return\"] must be used for ES3 parsing compatibility.\n        if (delegate.iterator[\"return\"]) {\n          // If the delegate iterator has a return method, give it a\n          // chance to clean up.\n          context.method = \"return\";\n          context.arg = undefined;\n          maybeInvokeDelegate(delegate, context);\n\n          if (context.method === \"throw\") {\n            // If maybeInvokeDelegate(context) changed context.method from\n            // \"return\" to \"throw\", let that override the TypeError below.\n            return ContinueSentinel;\n          }\n        }\n\n        context.method = \"throw\";\n        context.arg = new TypeError(\n          \"The iterator does not provide a 'throw' method\");\n      }\n\n      return ContinueSentinel;\n    }\n\n    var record = tryCatch(method, delegate.iterator, context.arg);\n\n    if (record.type === \"throw\") {\n      context.method = \"throw\";\n      context.arg = record.arg;\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    var info = record.arg;\n\n    if (! info) {\n      context.method = \"throw\";\n      context.arg = new TypeError(\"iterator result is not an object\");\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    if (info.done) {\n      // Assign the result of the finished delegate to the temporary\n      // variable specified by delegate.resultName (see delegateYield).\n      context[delegate.resultName] = info.value;\n\n      // Resume execution at the desired location (see delegateYield).\n      context.next = delegate.nextLoc;\n\n      // If context.method was \"throw\" but the delegate handled the\n      // exception, let the outer generator proceed normally. If\n      // context.method was \"next\", forget context.arg since it has been\n      // \"consumed\" by the delegate iterator. If context.method was\n      // \"return\", allow the original .return call to continue in the\n      // outer generator.\n      if (context.method !== \"return\") {\n        context.method = \"next\";\n        context.arg = undefined;\n      }\n\n    } else {\n      // Re-yield the result returned by the delegate method.\n      return info;\n    }\n\n    // The delegate iterator is finished, so forget it and continue with\n    // the outer generator.\n    context.delegate = null;\n    return ContinueSentinel;\n  }\n\n  // Define Generator.prototype.{next,throw,return} in terms of the\n  // unified ._invoke helper method.\n  defineIteratorMethods(Gp);\n\n  define(Gp, toStringTagSymbol, \"Generator\");\n\n  // A Generator should always return itself as the iterator object when the\n  // @@iterator function is called on it. Some browsers' implementations of the\n  // iterator prototype chain incorrectly implement this, causing the Generator\n  // object to not be returned from this call. This ensures that doesn't happen.\n  // See https://github.com/facebook/regenerator/issues/274 for more details.\n  Gp[iteratorSymbol] = function() {\n    return this;\n  };\n\n  Gp.toString = function() {\n    return \"[object Generator]\";\n  };\n\n  function pushTryEntry(locs) {\n    var entry = { tryLoc: locs[0] };\n\n    if (1 in locs) {\n      entry.catchLoc = locs[1];\n    }\n\n    if (2 in locs) {\n      entry.finallyLoc = locs[2];\n      entry.afterLoc = locs[3];\n    }\n\n    this.tryEntries.push(entry);\n  }\n\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\";\n    delete record.arg;\n    entry.completion = record;\n  }\n\n  function Context(tryLocsList) {\n    // The root entry object (effectively a try statement without a catch\n    // or a finally block) gives us a place to store values thrown from\n    // locations where there is no enclosing try statement.\n    this.tryEntries = [{ tryLoc: \"root\" }];\n    tryLocsList.forEach(pushTryEntry, this);\n    this.reset(true);\n  }\n\n  exports.keys = function(object) {\n    var keys = [];\n    for (var key in object) {\n      keys.push(key);\n    }\n    keys.reverse();\n\n    // Rather than returning an object with a next method, we keep\n    // things simple and return the next function itself.\n    return function next() {\n      while (keys.length) {\n        var key = keys.pop();\n        if (key in object) {\n          next.value = key;\n          next.done = false;\n          return next;\n        }\n      }\n\n      // To avoid creating an additional object, we just hang the .value\n      // and .done properties off the next function object itself. This\n      // also ensures that the minifier will not anonymize the function.\n      next.done = true;\n      return next;\n    };\n  };\n\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) {\n        return iteratorMethod.call(iterable);\n      }\n\n      if (typeof iterable.next === \"function\") {\n        return iterable;\n      }\n\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next() {\n          while (++i < iterable.length) {\n            if (hasOwn.call(iterable, i)) {\n              next.value = iterable[i];\n              next.done = false;\n              return next;\n            }\n          }\n\n          next.value = undefined;\n          next.done = true;\n\n          return next;\n        };\n\n        return next.next = next;\n      }\n    }\n\n    // Return an iterator with no values.\n    return { next: doneResult };\n  }\n  exports.values = values;\n\n  function doneResult() {\n    return { value: undefined, done: true };\n  }\n\n  Context.prototype = {\n    constructor: Context,\n\n    reset: function(skipTempReset) {\n      this.prev = 0;\n      this.next = 0;\n      // Resetting context._sent for legacy support of Babel's\n      // function.sent implementation.\n      this.sent = this._sent = undefined;\n      this.done = false;\n      this.delegate = null;\n\n      this.method = \"next\";\n      this.arg = undefined;\n\n      this.tryEntries.forEach(resetTryEntry);\n\n      if (!skipTempReset) {\n        for (var name in this) {\n          // Not sure about the optimal order of these conditions:\n          if (name.charAt(0) === \"t\" &&\n              hasOwn.call(this, name) &&\n              !isNaN(+name.slice(1))) {\n            this[name] = undefined;\n          }\n        }\n      }\n    },\n\n    stop: function() {\n      this.done = true;\n\n      var rootEntry = this.tryEntries[0];\n      var rootRecord = rootEntry.completion;\n      if (rootRecord.type === \"throw\") {\n        throw rootRecord.arg;\n      }\n\n      return this.rval;\n    },\n\n    dispatchException: function(exception) {\n      if (this.done) {\n        throw exception;\n      }\n\n      var context = this;\n      function handle(loc, caught) {\n        record.type = \"throw\";\n        record.arg = exception;\n        context.next = loc;\n\n        if (caught) {\n          // If the dispatched exception was caught by a catch block,\n          // then let that catch block handle the exception normally.\n          context.method = \"next\";\n          context.arg = undefined;\n        }\n\n        return !! caught;\n      }\n\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        var record = entry.completion;\n\n        if (entry.tryLoc === \"root\") {\n          // Exception thrown outside of any try block that could handle\n          // it, so set the completion value of the entire function to\n          // throw the exception.\n          return handle(\"end\");\n        }\n\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\");\n          var hasFinally = hasOwn.call(entry, \"finallyLoc\");\n\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            } else if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            }\n\n          } else if (hasFinally) {\n            if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else {\n            throw new Error(\"try statement without catch or finally\");\n          }\n        }\n      }\n    },\n\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev &&\n            hasOwn.call(entry, \"finallyLoc\") &&\n            this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n\n      if (finallyEntry &&\n          (type === \"break\" ||\n           type === \"continue\") &&\n          finallyEntry.tryLoc <= arg &&\n          arg <= finallyEntry.finallyLoc) {\n        // Ignore the finally entry if control is not jumping to a\n        // location outside the try/catch block.\n        finallyEntry = null;\n      }\n\n      var record = finallyEntry ? finallyEntry.completion : {};\n      record.type = type;\n      record.arg = arg;\n\n      if (finallyEntry) {\n        this.method = \"next\";\n        this.next = finallyEntry.finallyLoc;\n        return ContinueSentinel;\n      }\n\n      return this.complete(record);\n    },\n\n    complete: function(record, afterLoc) {\n      if (record.type === \"throw\") {\n        throw record.arg;\n      }\n\n      if (record.type === \"break\" ||\n          record.type === \"continue\") {\n        this.next = record.arg;\n      } else if (record.type === \"return\") {\n        this.rval = this.arg = record.arg;\n        this.method = \"return\";\n        this.next = \"end\";\n      } else if (record.type === \"normal\" && afterLoc) {\n        this.next = afterLoc;\n      }\n\n      return ContinueSentinel;\n    },\n\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) {\n          this.complete(entry.completion, entry.afterLoc);\n          resetTryEntry(entry);\n          return ContinueSentinel;\n        }\n      }\n    },\n\n    \"catch\": function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (record.type === \"throw\") {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n\n      // The context.catch method must only be called with a location\n      // argument that corresponds to a known catch block.\n      throw new Error(\"illegal catch attempt\");\n    },\n\n    delegateYield: function(iterable, resultName, nextLoc) {\n      this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      };\n\n      if (this.method === \"next\") {\n        // Deliberately forget the last sent value so that we don't\n        // accidentally pass it on to the delegate.\n        this.arg = undefined;\n      }\n\n      return ContinueSentinel;\n    }\n  };\n\n  // Regardless of whether this script is executing as a CommonJS module\n  // or not, return the runtime object so that we can declare the variable\n  // regeneratorRuntime in the outer scope, which allows this module to be\n  // injected easily by `bin/regenerator --include-runtime script.js`.\n  return exports;\n\n}(\n  // If this script is executing as a CommonJS module, use module.exports\n  // as the regeneratorRuntime namespace. Otherwise create a new empty\n  // object. Either way, the resulting object will be used to initialize\n  // the regeneratorRuntime variable at the top of this file.\n  typeof module === \"object\" ? module.exports : {}\n));\n\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  // This module should not be running in strict mode, so the above\n  // assignment should always work unless something is misconfigured. Just\n  // in case runtime.js accidentally runs in strict mode, we can escape\n  // strict mode using a global Function call. This could conceivably fail\n  // if a Content Security Policy forbids using Function, but in that case\n  // the proper solution is to fix the accidental strict mode problem. If\n  // you've misconfigured your bundler to force strict mode and applied a\n  // CSP to forbid Function, and you're not willing to fix either of those\n  // problems, please detail your unique predicament in a GitHub issue.\n  Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n}\n", "import isToday from 'date-fns/isToday';\nimport isYesterday from 'date-fns/isYesterday';\n/**\n * @name Get contrasting text color\n * @description Get contrasting text color from a text color\n * @param bgColor  Background color of text.\n * @returns contrasting text color\n */\nexport const getContrastingTextColor = (bgColor: string): string => {\n  const color = bgColor.replace('#', '');\n  const r = parseInt(color.slice(0, 2), 16);\n  const g = parseInt(color.slice(2, 4), 16);\n  const b = parseInt(color.slice(4, 6), 16);\n  // http://stackoverflow.com/a/3943023/112731\n  return r * 0.299 + g * 0.587 + b * 0.114 > 186 ? '#000000' : '#FFFFFF';\n};\n\n/**\n * @name Get formatted date\n * @description Get date in today, yesterday or any other date format\n * @param date  date\n * @param todayText  Today text\n * @param yesterdayText  Yesterday text\n * @returns formatted date\n */\nexport const formatDate = ({\n  date,\n  todayText,\n  yesterdayText,\n}: {\n  date: string;\n  todayText: string;\n  yesterdayText: string;\n}) => {\n  const dateValue = new Date(date);\n  if (isToday(dateValue)) return todayText;\n  if (isYesterday(dateValue)) return yesterdayText;\n  return date;\n};\n\n/**\n * @name formatTime\n * @description Format time to Hour, Minute and Second\n * @param timeInSeconds  number\n * @returns formatted time\n */\n\nexport const formatTime = (timeInSeconds: number) => {\n  let formattedTime = '';\n  if (timeInSeconds >= 60 && timeInSeconds < 3600) {\n    const minutes = Math.floor(timeInSeconds / 60);\n    formattedTime = `${minutes} Min`;\n    const seconds = minutes === 60 ? 0 : Math.floor(timeInSeconds % 60);\n    return formattedTime + `${seconds > 0 ? ' ' + seconds + ' Sec' : ''}`;\n  }\n  if (timeInSeconds >= 3600 && timeInSeconds < 86400) {\n    const hours = Math.floor(timeInSeconds / 3600);\n    formattedTime = `${hours} Hr`;\n    const minutes =\n      timeInSeconds % 3600 < 60 || hours === 24\n        ? 0\n        : Math.floor((timeInSeconds % 3600) / 60);\n    return formattedTime + `${minutes > 0 ? ' ' + minutes + ' Min' : ''}`;\n  }\n  if (timeInSeconds >= 86400) {\n    const days = Math.floor(timeInSeconds / 86400);\n    formattedTime = `${days} Day`;\n    const hours =\n      timeInSeconds % 86400 < 3600 || days >= 364\n        ? 0\n        : Math.floor((timeInSeconds % 86400) / 3600);\n    return formattedTime + `${hours > 0 ? ' ' + hours + ' Hr' : ''}`;\n  }\n  return `${Math.floor(timeInSeconds)} Sec`;\n};\n\n/**\n * @name trimContent\n * @description Trim a string to max length\n * @param content String to trim\n * @param maxLength Length of the string to trim, default 1024\n * @param ellipsis Boolean to add dots at the end of the string, default false\n * @returns trimmed string\n */\nexport const trimContent = (\n  content: string = '',\n  maxLength: number = 1024,\n  ellipsis: boolean = false\n) => {\n  let trimmedContent = content;\n  if (content.length > maxLength) {\n    trimmedContent = content.substring(0, maxLength);\n  }\n  if (ellipsis) {\n    trimmedContent = trimmedContent + '...';\n  }\n  return trimmedContent;\n};\n\n/**\n * @name convertSecondsToTimeUnit\n * @description Convert seconds to time unit\n * @param seconds  number\n * @param unitNames  object\n * @returns time and unit\n * @example\n * convertToUnit(60, { minute: 'm', hour: 'h', day: 'd' }); // { time: 1, unit: 'm' }\n * convertToUnit(60, { minute: 'Minutes', hour: 'Hours', day: 'Days' }); // { time: 1, unit: 'Minutes' }\n */\n\nexport const convertSecondsToTimeUnit = (\n  seconds: number,\n  unitNames: { minute: string; hour: string; day: string }\n) => {\n  if (seconds === null || seconds === 0) return { time: '', unit: '' };\n  if (seconds < 3600)\n    return { time: Number((seconds / 60).toFixed(1)), unit: unitNames.minute };\n  if (seconds < 86400)\n    return { time: Number((seconds / 3600).toFixed(1)), unit: unitNames.hour };\n  return { time: Number((seconds / 86400).toFixed(1)), unit: unitNames.day };\n};\n\n/**\n * @name fileNameWithEllipsis\n * @description Truncates a filename while preserving the extension\n * @param {Object} file - File object containing filename or name property\n * @param {number} [maxLength=26] - Maximum length of the filename (excluding extension)\n * @param {string} [ellipsis='…'] - Character to use for truncation\n * @returns {string} Truncated filename with extension\n * @example\n * fileNameWithEllipsis({ filename: 'very-long-filename.pdf' }, 10) // 'very-long-f….pdf'\n * fileNameWithEllipsis({ name: 'short.txt' }, 10) // 'short.txt'\n */\nexport const fileNameWithEllipsis = (\n  file: { filename?: string; name?: string },\n  maxLength: number = 26,\n  ellipsis: string = '…'\n): string => {\n  const fullName = file?.filename ?? file?.name ?? 'Untitled';\n\n  const dotIndex = fullName.lastIndexOf('.');\n  if (dotIndex === -1) return fullName;\n\n  const [name, extension] = [\n    fullName.slice(0, dotIndex),\n    fullName.slice(dotIndex),\n  ];\n\n  if (name.length <= maxLength) return fullName;\n\n  return `${name.slice(0, maxLength)}${ellipsis}${extension}`;\n};\n\n/**\n * @name splitName\n * @description Splits a full name into firstName and lastName\n * @param {string} name - Full name of the user\n * @returns {Object} Object with firstName and lastName\n * @example\n * splitName('Mary Jane Smith') // { firstName: 'Mary Jane', lastName: 'Smith' }\n * splitName('Alice') // { firstName: 'Alice', lastName: '' }\n * splitName('John Doe') // { firstName: 'John', lastName: 'Doe' }\n * splitName('') // { firstName: '', lastName: '' }\n */\nexport const splitName = (\n  fullName: string\n): { firstName: string; lastName: string } => {\n  const trimmedName = fullName.trim();\n  if (!trimmedName) {\n    return {\n      firstName: '',\n      lastName: '',\n    };\n  }\n\n  // Split the name by spaces\n  const nameParts = trimmedName.split(/\\s+/);\n\n  // If only one word, treat it as firstName\n  if (nameParts.length === 1) {\n    return {\n      firstName: nameParts[0],\n      lastName: '',\n    };\n  }\n\n  // Last element is lastName, everything else is firstName\n  const lastName = nameParts.pop() || '';\n  const firstName = nameParts.join(' ');\n\n  return { firstName, lastName };\n};\n\ninterface DownloadFileOptions {\n  url: string;\n  type: string;\n  extension?: string | null;\n}\n/**\n * Downloads a file from a URL with proper file type handling\n * @name downloadFile\n * @description Downloads file from URL with proper type handling and cleanup\n * @param {Object} options Download configuration options\n * @param {string} options.url File URL to download\n * @param {string} options.type File type identifier\n * @param {string} [options.extension] Optional file extension\n * @returns {Promise<boolean>} Returns true if download successful, false otherwise\n */\nexport const downloadFile = async ({\n  url,\n  type,\n  extension = null,\n}: DownloadFileOptions): Promise<void> => {\n  if (!url || !type) {\n    throw new Error('Invalid download parameters');\n  }\n\n  try {\n    const response = await fetch(url, { cache: 'no-store' });\n\n    if (!response.ok) {\n      throw new Error(`Download failed: ${response.status}`);\n    }\n\n    const blobData = await response.blob();\n\n    const contentType = response.headers.get('content-type');\n\n    const fileExtension =\n      extension || (contentType ? contentType.split('/')[1] : type);\n\n    const dispositionHeader = response.headers.get('content-disposition');\n    const filenameMatch = dispositionHeader?.match(/filename=\"(.*?)\"/);\n\n    const filename =\n      filenameMatch?.[1] ?? `attachment_${Date.now()}.${fileExtension}`;\n\n    const blobUrl = URL.createObjectURL(blobData);\n    const link = Object.assign(document.createElement('a'), {\n      href: blobUrl,\n      download: filename,\n      style: 'display: none',\n    });\n\n    document.body.append(link);\n    link.click();\n    link.remove();\n    URL.revokeObjectURL(blobUrl);\n  } catch (error) {\n    throw error instanceof Error ? error : new Error('Download failed');\n  }\n};\n\ninterface FileInfo {\n  name: string; // Full filename with extension\n  type: string; // File extension only\n  base: string; // Filename without extension\n}\n/**\n * Extracts file information from a URL or file path.\n *\n * @param {string} url - The URL or file path to process\n * @returns {FileInfo} Object containing file information\n *\n * @example\n * getFileInfo('https://example.com/path/Document%20Name.PDF')\n * returns {\n *   name: 'Document Name.PDF',\n *   type: 'pdf',\n *   base: 'Document Name'\n * }\n *\n * getFileInfo('invalid/url')\n * returns {\n *   name: 'Unknown File',\n *   type: '',\n *   base: 'Unknown File'\n * }\n */\nexport const getFileInfo = (url: string): FileInfo => {\n  const defaultInfo: FileInfo = {\n    name: 'Unknown File',\n    type: '',\n    base: 'Unknown File',\n  };\n\n  if (!url || typeof url !== 'string') {\n    return defaultInfo;\n  }\n\n  try {\n    // Handle both URL and file path cases\n    const cleanUrl = url\n      .split(/[?#]/)[0] // Remove query params and hash\n      .replace(/\\\\/g, '/'); // Normalize path separators\n\n    const encodedFilename = cleanUrl.split('/').pop();\n    if (!encodedFilename) {\n      return defaultInfo;\n    }\n\n    const fileName = decodeURIComponent(encodedFilename);\n\n    // Handle hidden files (starting with dot)\n    if (fileName.startsWith('.') && !fileName.includes('.', 1)) {\n      return { name: fileName, type: '', base: fileName };\n    }\n\n    // last index is where the file extension starts\n    // This will handle cases where the file name has multiple dots\n    const lastDotIndex = fileName.lastIndexOf('.');\n    if (lastDotIndex === -1 || lastDotIndex === 0) {\n      return { name: fileName, type: '', base: fileName };\n    }\n\n    const base = fileName.slice(0, lastDotIndex);\n    const type = fileName.slice(lastDotIndex + 1).toLowerCase();\n\n    return { name: fileName, type, base };\n  } catch (error) {\n    console.error('Error processing file info:', error);\n    return defaultInfo;\n  }\n};\n\n/**\n * Formats a number with K/M/B/T suffixes using Intl.NumberFormat\n * @param {number | string | null | undefined} num - The number to format\n * @returns {string} Formatted string (e.g., \"1.2K\", \"2.3M\", \"999\")\n * @example\n * formatNumber(1234)     // \"1.2K\"\n * formatNumber(1000000)  // \"1M\"\n * formatNumber(999)      // \"999\"\n * formatNumber(12344)    // \"12.3K\"\n */\nexport const formatNumber = (\n  num: number | string | null | undefined\n): string => {\n  const n = Number(num) || 0;\n  return new Intl.NumberFormat('en', {\n    notation: 'compact',\n    maximumFractionDigits: 1,\n  } as Intl.NumberFormatOptions).format(n);\n};\n", "/**\n * URL related helper functions\n */\n\n/**\n * Converts various input formats to URL objects.\n * Handles URL objects, domain strings, relative paths, and full URLs.\n * @param {string|URL} input - Input to convert to URL object\n * @returns {URL|null} URL object or null if input is invalid\n */\nexport const toURL = (input: string | URL | null | undefined): URL | null => {\n  if (!input) return null;\n  if (input instanceof URL) return input;\n\n  if (\n    typeof input === 'string' &&\n    !input.includes('://') &&\n    !input.startsWith('/')\n  ) {\n    return new URL(`https://${input}`);\n  }\n\n  if (typeof input === 'string' && input.startsWith('/')) {\n    return new URL(input, window.location.origin);\n  }\n\n  return new URL(input as string);\n};\n\n/**\n * Determines if two URLs belong to the same host by comparing their normalized URL objects.\n * Handles various input formats including URL objects, domain strings, relative paths, and full URLs.\n * Returns false if either URL cannot be parsed or normalized.\n * @param {string|URL} url1 - First URL to compare\n * @param {string|URL} url2 - Second URL to compare\n * @returns {boolean} True if both URLs have the same host, false otherwise\n */\nexport const isSameHost = (\n  url1: string | URL | null | undefined,\n  url2: string | URL | null | undefined\n): boolean => {\n  try {\n    const urlObj1 = toURL(url1);\n    const urlObj2 = toURL(url2);\n\n    if (!urlObj1 || !urlObj2) return false;\n\n    return urlObj1.hostname === urlObj2.hostname;\n  } catch (error) {\n    return false;\n  }\n};\n\n/**\n * Check if a string is a valid domain name.\n * An empty string is allowed and considered valid.\n *\n * @param domain Domain to validate.\n * @returns Whether the domain matches the rules.\n */\nexport const isValidDomain = (domain: string): boolean => {\n  if (domain === '') return true;\n\n  const domainRegex = /^(?!-)(?!.*--)[\\p{L}0-9-]{1,63}(?<!-)(?:\\.(?!-)(?!.*--)[\\p{L}0-9-]{1,63}(?<!-))*\\.[\\p{L}]{2,63}$/u;\n\n  return domainRegex.test(domain) && domain.length <= 253;\n};\n", "export type EmailAttributes = {\n  bcc: string[] | null;\n  cc: string[] | null;\n  content_type: string;\n  date: string;\n  from: string[] | null;\n  html_content: {\n    full: string;\n    reply: string;\n    quoted: string;\n  };\n  in_reply_to: null;\n  message_id: string;\n  multipart: boolean;\n  number_of_attachments: number;\n  subject: string;\n  text_content: {\n    full: string;\n    reply: string;\n    quoted: string;\n  };\n  to: string[] | null;\n};\n\nexport type IncomingContentAttribute = {\n  email: EmailAttributes | null;\n};\n\nexport type OutgoingContentAttribute = {\n  cc_emails: string[] | null;\n  bcc_emails: string[] | null;\n  to_emails: string[] | null;\n  external_error: string;\n};\n\nexport type MessageContentAttributes =\n  | IncomingContentAttribute\n  | OutgoingContentAttribute;\n\nexport type MessageConversation = {\n  id: number;\n  assignee_id: number;\n  custom_attributes: Record<string, any>;\n  first_reply_created_at: number;\n  waiting_since: number;\n  status: string;\n  unread_count: number;\n  last_activity_at: number;\n  contact_inbox: { source_id: string };\n};\n\nexport type MessageAttachment = {\n  id: number;\n  message_id: number;\n  file_type: string;\n  account_id: number;\n  extension: null;\n  data_url: string;\n  thumb_url: string;\n  file_size: number;\n  width: null;\n  height: null;\n};\n\nexport type MessageSender = {\n  custom_attributes: {};\n  email: null;\n  id: number;\n  identifier: null;\n  name: string;\n  phone_number: null;\n  thumbnail: string;\n  type: string;\n};\n\nexport enum MessageType {\n  INCOMING = 0,\n  OUTGOING = 1,\n  ACTIVITY = 2,\n  TEMPLATE = 3,\n}\n\nexport type BaseEmailMessage = {\n  id: number;\n  content: null;\n  account_id: number;\n  inbox_id: number;\n  conversation_id: number;\n  message_type: MessageType;\n  created_at: number;\n  updated_at: string;\n  private: boolean;\n  status: string;\n  source_id: null;\n  content_type: string;\n  content_attributes: MessageContentAttributes;\n  sender_type: string;\n  sender_id: number;\n  external_source_ids: {};\n  additional_attributes: {};\n  processed_message_content: null;\n  sentiment: {};\n  conversation: MessageConversation;\n  attachments: MessageAttachment[];\n  sender: MessageSender;\n};\n\nexport type IncomingEmailMessage = BaseEmailMessage & {\n  message_type: MessageType.INCOMING;\n  content_attributes: IncomingContentAttribute;\n};\n\nexport type OutgoingEmailMessage = BaseEmailMessage & {\n  message_type: MessageType.OUTGOING;\n  content_attributes: OutgoingContentAttribute;\n};\n\nexport type EmailMessage = IncomingEmailMessage | OutgoingEmailMessage;\n", "import {\n  EmailMessage,\n  MessageType,\n  IncomingEmailMessage,\n  OutgoingEmailMessage,\n} from './types/message';\n\nexport function getRecipients(\n  lastEmail: EmailMessage,\n  conversationContact: string,\n  inboxEmail: string,\n  forwardToEmail: string\n) {\n  let to = [] as string[];\n  let cc = [] as string[];\n  let bcc = [] as string[];\n\n  // Reset emails if there's no lastEmail\n  if (!lastEmail) {\n    return { to, cc, bcc };\n  }\n\n  // Extract values from lastEmail and current conversation context\n  const { message_type: messageType } = lastEmail;\n\n  const isIncoming = messageType === MessageType.INCOMING;\n\n  let emailAttributes = {} as {\n    cc: string[] | null;\n    bcc: string[] | null;\n    from: string[] | null;\n    to: string[] | null;\n  };\n\n  if (isIncoming) {\n    const {\n      content_attributes: contentAttributes,\n    } = lastEmail as IncomingEmailMessage;\n    const email = contentAttributes.email;\n    emailAttributes = {\n      cc: email?.cc || [],\n      bcc: email?.bcc || [],\n      from: email?.from || [],\n      to: [],\n    };\n  } else {\n    const {\n      content_attributes: contentAttributes,\n    } = lastEmail as OutgoingEmailMessage;\n\n    const {\n      cc_emails: ccEmails = [],\n      bcc_emails: bccEmails = [],\n      to_emails: toEmails = [],\n    } = contentAttributes ?? {};\n\n    emailAttributes = {\n      cc: ccEmails,\n      bcc: bccEmails,\n      to: toEmails,\n      from: [],\n    };\n  }\n\n  let isLastEmailFromContact = false;\n  // this will be false anyway if the last email was outgoing\n  isLastEmailFromContact =\n    isIncoming && (emailAttributes.from ?? []).includes(conversationContact);\n\n  if (isIncoming) {\n    // Reply to sender if incoming\n    to.push(...(emailAttributes.from ?? []));\n  } else {\n    // Otherwise, reply to the last recipient (for outgoing message)\n    // If there is no to_emails, reply to the conversation contact\n    to.push(...(emailAttributes.to ?? [conversationContact]));\n  }\n\n  // Start building the cc list, including additional recipients\n  // If the email had multiple recipients, include them in the cc list\n  cc = emailAttributes.cc ? [...emailAttributes.cc] : [];\n  // Only include 'to' recipients in cc for incoming emails, not for outgoing\n  if (Array.isArray(emailAttributes.to) && isIncoming) {\n    cc.push(...emailAttributes.to);\n  }\n\n  // Add the conversation contact to cc if the last email wasn't sent by them\n  // Ensure the message is an incoming one\n  if (!isLastEmailFromContact && isIncoming) {\n    cc.push(conversationContact);\n  }\n\n  // Process BCC: Remove conversation contact from bcc as it is already in cc\n  bcc = (emailAttributes.bcc || []).filter(\n    emailAddress => emailAddress !== conversationContact\n  );\n\n  // Filter out undesired emails from cc:\n  // - Remove conversation contact from cc if they sent the last email\n  // - Remove inbox and forward-to email to prevent loops\n  // - Remove emails matching the reply UUID pattern\n  const replyUUIDPattern = /^reply\\+([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/i;\n  cc = cc.filter(email => {\n    if (email === conversationContact && isLastEmailFromContact) {\n      return false;\n    }\n    if (email === inboxEmail || email === forwardToEmail) {\n      return false;\n    }\n    if (replyUUIDPattern.test(email)) {\n      return false;\n    }\n    return true;\n  });\n\n  bcc = bcc.filter(email => {\n    if (\n      email === inboxEmail ||\n      email === forwardToEmail ||\n      replyUUIDPattern.test(email)\n    ) {\n      return false;\n    }\n\n    return true;\n  });\n\n  // Deduplicate each recipient list by converting to a Set then back to an array\n  to = Array.from(new Set(to));\n  cc = Array.from(new Set(cc));\n  bcc = Array.from(new Set(bcc));\n\n  return {\n    to,\n    cc,\n    bcc,\n  };\n}\n", "/**\n * Function that parses a string boolean value and returns the corresponding boolean value\n * @param {string | number} candidate - The string boolean value to be parsed\n * @return {boolean} - The parsed boolean value\n */\n\nexport function parseBoolean(candidate: string | number) {\n  try {\n    // lowercase the string, so TRUE becomes true\n    const candidateString = String(candidate).toLowerCase();\n\n    // wrap in boolean to ensure that the return value\n    // is a boolean even if values like 0 or 1 are passed\n    return Boolean(JSON.parse(candidateString));\n  } catch (error) {\n    return false;\n  }\n}\n", "/**\n * Sorts an array of numbers in ascending order.\n * @param {number[]} arr - The array of numbers to be sorted.\n * @returns {number[]} - The sorted array.\n */\nexport function sortAsc(arr: number[]) {\n  // .slice() is used to create a copy of the array so that the original array is not mutated\n  return arr.slice().sort((a, b) => a - b);\n}\n\n/**\n * Calculates the quantile value of an array at a specified percentile.\n * @param {number[]} arr - The array of numbers to calculate the quantile value from.\n * @param {number} q - The percentile to calculate the quantile value for.\n * @returns {number} - The quantile value.\n */\nexport function quantile(arr: number[], q: number) {\n  const sorted = sortAsc(arr); // Sort the array in ascending order\n  return _quantileForSorted(sorted, q); // Calculate the quantile value\n}\n\n/**\n * Clamps a value between a minimum and maximum range.\n * @param {number} min - The minimum range.\n * @param {number} max - The maximum range.\n * @param {number} value - The value to be clamped.\n * @returns {number} - The clamped value.\n */\nexport function clamp(min: number, max: number, value: number) {\n  if (value < min) {\n    return min;\n  }\n  if (value > max) {\n    return max;\n  }\n  return value;\n}\n\n/**\n * This method assumes the the array provided is already sorted in ascending order.\n * It's a helper method for the quantile method and should not be exported as is.\n *\n * @param {number[]} arr - The array of numbers to calculate the quantile value from.\n * @param {number} q - The percentile to calculate the quantile value for.\n * @returns {number} - The quantile value.\n */\nfunction _quantileForSorted(sorted: number[], q: number) {\n  const clamped = clamp(0, 1, q); // Clamp the percentile between 0 and 1\n  const pos = (sorted.length - 1) * clamped; // Calculate the index of the element at the specified percentile\n  const base = Math.floor(pos); // Find the index of the closest element to the specified percentile\n  const rest = pos - base; // Calculate the decimal value between the closest elements\n\n  // Interpolate the quantile value between the closest elements\n  // Most libraries don't to the interpolation, but I'm just having fun here\n  // also see https://en.wikipedia.org/wiki/Quantile#Estimating_quantiles_from_a_sample\n  if (sorted[base + 1] !== undefined) {\n    // in case the position was a integer, the rest will be 0 and the interpolation will be skipped\n    return sorted[base] + rest * (sorted[base + 1] - sorted[base]);\n  }\n\n  // Return the closest element if there is no interpolation possible\n  return sorted[base];\n}\n\n/**\n * Calculates the quantile values for an array of intervals.\n * @param {number[]} data - The array of numbers to calculate the quantile values from.\n * @param {number[]} intervals - The array of intervals to calculate the quantile values for.\n * @returns {number[]} - The array of quantile values for the intervals.\n */\nexport const getQuantileIntervals = (data: number[], intervals: number[]) => {\n  // Sort the array in ascending order before looping through the intervals.\n  // depending on the size of the array and the number of intervals, this can speed up the process by at least twice\n  // for a random array of 100 numbers and 5 intervals, the speedup is 3x\n  const sorted = sortAsc(data);\n\n  return intervals.map(interval => {\n    return _quantileForSorted(sorted, interval);\n  });\n};\n\n/**\n * Calculates the relative position of a point from the center of an element\n *\n * @param {number} mouseX - The x-coordinate of the mouse pointer\n * @param {number} mouseY - The y-coordinate of the mouse pointer\n * @param {DOMRect} rect - The bounding client rectangle of the target element\n * @returns {{relativeX: number, relativeY: number}} Object containing x and y distances from center\n */\nexport const calculateCenterOffset = (\n  mouseX: number,\n  mouseY: number,\n  rect: DOMRect\n) => {\n  const centerX = rect.left + rect.width / 2;\n  const centerY = rect.top + rect.height / 2;\n\n  return {\n    relativeX: mouseX - centerX,\n    relativeY: mouseY - centerY,\n  };\n};\n\n/**\n * Applies a rotation matrix to coordinates\n * Used to adjust mouse coordinates based on the current rotation of the image\n * This function implements a standard 2D rotation matrix transformation:\n * [x']   [cos(θ) -sin(θ)] [x]\n * [y'] = [sin(θ)  cos(θ)] [y]\n *\n * @see {@link https://mathworld.wolfram.com/RotationMatrix.html} for mathematical derivation\n *\n * @param {number} relativeX - X-coordinate relative to center before rotation\n * @param {number} relativeY - Y-coordinate relative to center before rotation\n * @param {number} angle - Rotation angle in degrees\n * @returns {{rotatedX: number, rotatedY: number}} Coordinates after applying rotation matrix\n */\nexport const applyRotationTransform = (\n  relativeX: number,\n  relativeY: number,\n  angle: number\n) => {\n  const radians = (angle * Math.PI) / 180;\n  const cos = Math.cos(-radians);\n  const sin = Math.sin(-radians);\n\n  return {\n    rotatedX: relativeX * cos - relativeY * sin,\n    rotatedY: relativeX * sin + relativeY * cos,\n  };\n};\n\n/**\n * Converts absolute rotated coordinates to percentage values relative to image dimensions\n * Ensures values are clamped between 0-100% for valid CSS transform-origin properties\n *\n * @param {number} rotatedX - X-coordinate after rotation transformation\n * @param {number} rotatedY - Y-coordinate after rotation transformation\n * @param {number} width - Width of the target element\n * @param {number} height - Height of the target element\n * @returns {{x: number, y: number}} Normalized coordinates as percentages (0-100%)\n */\nexport const normalizeToPercentage = (\n  rotatedX: number,\n  rotatedY: number,\n  width: number,\n  height: number\n) => {\n  // Convert to percentages (0-100%) relative to image dimensions\n  // 50% represents the center point\n  // The division by (width/2) maps the range [-width/2, width/2] to [-50%, 50%]\n  // Adding 50% shifts this to [0%, 100%]\n  return {\n    x: Math.max(0, Math.min(100, 50 + (rotatedX / (width / 2)) * 50)),\n    y: Math.max(0, Math.min(100, 50 + (rotatedY / (height / 2)) * 50)),\n  };\n};\n", "import {\n  Conversation,\n  Sender,\n  Variables,\n  CustomAttributes,\n  Contact,\n  Inbox,\n} from './types/conversation';\nconst MESSAGE_VARIABLES_REGEX = /{{(.*?)}}/g;\n\nconst skipCodeBlocks = (str: string) => str.replace(/```(?:.|\\n)+?```/g, '');\n\nexport const capitalizeName = (name: string | null): string => {\n  if (!name) return ''; // Return empty string for null or undefined input\n\n  return name\n    .split(' ') // Split the name into words based on spaces\n    .map(word => {\n      if (!word) return ''; // Handle empty strings that might result from multiple spaces\n\n      // Capitalize only the first character, leaving the rest unchanged\n      // This correctly handles accented characters like 'í' in 'Aríel'\n      return word.charAt(0).toUpperCase() + word.slice(1);\n    })\n    .join(' '); // Rejoin the words with spaces\n};\n\nexport const getFirstName = ({ user }: { user: Sender }) => {\n  const firstName = user?.name ? user.name.split(' ').shift() : '';\n  return capitalizeName(firstName as string);\n};\n\nexport const getLastName = ({ user }: { user: Sender }) => {\n  if (user && user.name) {\n    const lastName =\n      user.name.split(' ').length > 1 ? user.name.split(' ').pop() : '';\n    return capitalizeName(lastName as string);\n  }\n  return '';\n};\n\nexport const getMessageVariables = ({\n  conversation,\n  contact,\n  inbox,\n}: {\n  conversation: Conversation;\n  contact?: Contact;\n  inbox?: Inbox;\n}) => {\n  const {\n    meta: { assignee, sender },\n    id,\n    custom_attributes: conversationCustomAttributes = {},\n  } = conversation;\n  const { custom_attributes: contactCustomAttributes } = contact || {};\n\n  const standardVariables = {\n    'contact.name': capitalizeName(sender?.name || ''),\n    'contact.first_name': getFirstName({ user: sender }),\n    'contact.last_name': getLastName({ user: sender }),\n    'contact.email': sender?.email,\n    'contact.phone': sender?.phone_number,\n    'contact.id': sender?.id,\n    'conversation.id': id,\n    'inbox.id': inbox?.id,\n    'inbox.name': inbox?.name,\n    'agent.name': capitalizeName(assignee?.name || ''),\n    'agent.first_name': getFirstName({ user: assignee }),\n    'agent.last_name': getLastName({ user: assignee }),\n    'agent.email': assignee?.email ?? '',\n  };\n  const conversationCustomAttributeVariables = Object.entries(\n    conversationCustomAttributes ?? {}\n  ).reduce((acc: CustomAttributes, [key, value]) => {\n    acc[`conversation.custom_attribute.${key}`] = value;\n    return acc;\n  }, {});\n\n  const contactCustomAttributeVariables = Object.entries(\n    contactCustomAttributes ?? {}\n  ).reduce((acc: CustomAttributes, [key, value]) => {\n    acc[`contact.custom_attribute.${key}`] = value;\n    return acc;\n  }, {});\n\n  const variables = {\n    ...standardVariables,\n    ...conversationCustomAttributeVariables,\n    ...contactCustomAttributeVariables,\n  };\n\n  return variables;\n};\n\nexport const replaceVariablesInMessage = ({\n  message,\n  variables,\n}: {\n  message: string;\n  variables: Variables;\n}) => {\n  // @ts-ignore\n  return message?.replace(MESSAGE_VARIABLES_REGEX, (_, replace) => {\n    return variables[replace.trim()]\n      ? variables[replace.trim().toLowerCase()]\n      : '';\n  });\n};\n\nexport const getUndefinedVariablesInMessage = ({\n  message,\n  variables,\n}: {\n  message: string;\n  variables: Variables;\n}) => {\n  const messageWithOutCodeBlocks = skipCodeBlocks(message);\n  const matches = messageWithOutCodeBlocks.match(MESSAGE_VARIABLES_REGEX);\n  if (!matches) return [];\n\n  return matches\n    .map(match => {\n      return match\n        .replace('{{', '')\n        .replace('}}', '')\n        .trim();\n    })\n    .filter(variable => {\n      return variables[variable] === undefined;\n    });\n};\n", "/**\n * Creates a typing indicator utility.\n * @param onStartTyping Callback function to be called when typing starts\n * @param onStopTyping Callback function to be called when typing stops after delay\n * @param idleTime Delay for idle time in ms before considering typing stopped\n * @returns An object with start and stop methods for typing indicator\n */\n\ntype CallbackFunction = () => void;\ntype Timeout = ReturnType<typeof setTimeout>;\n\nexport const createTypingIndicator = (\n  onStartTyping: CallbackFunction,\n  onStopTyping: CallbackFunction,\n  idleTime: number\n) => {\n  let timer: Timeout | null = null;\n\n  const start = (): void => {\n    if (!timer) {\n      onStartTyping();\n    }\n    reset();\n  };\n\n  const stop = (): void => {\n    if (timer) {\n      clearTimeout(timer as Timeout);\n      timer = null;\n      onStopTyping();\n    }\n  };\n\n  const reset = (): void => {\n    if (timer) {\n      clearTimeout(timer as Timeout);\n    }\n    timer = setTimeout(() => {\n      stop();\n    }, idleTime) as Timeout;\n  };\n\n  return { start, stop };\n};\n", "import { Conversation } from './types/conversation';\nimport { AppliedSla, SLAStatus } from './types/sla';\n\n/**\n * Calculates the threshold for an SLA based on the current time and the provided threshold.\n * @param timeOffset - The time offset in seconds.\n * @param threshold - The threshold in seconds or null if not applicable.\n * @returns The calculated threshold in seconds or null if the threshold is null.\n */\nconst calculateThreshold = (\n  timeOffset: number,\n  threshold: number | null\n): number | null => {\n  // Calculate the time left for the SLA to breach or the time since the SLA has missed\n  if (threshold === null) return null;\n  const currentTime = Math.floor(Date.now() / 1000);\n  return timeOffset + threshold - currentTime;\n};\n\n/**\n * Finds the most urgent SLA status based on the threshold.\n * @param SLAStatuses - An array of SLAStatus objects.\n * @returns The most urgent SLAStatus object.\n */\nconst findMostUrgentSLAStatus = (SLAStatuses: SLAStatus[]): SLAStatus => {\n  // Sort the SLAs based on the threshold and return the most urgent SLA\n  SLAStatuses.sort(\n    (sla1, sla2) => Math.abs(sla1.threshold) - Math.abs(sla2.threshold)\n  );\n  return SLAStatuses[0];\n};\n\n/**\n * Formats the SLA time in a human-readable format.\n * @param seconds - The time in seconds.\n * @returns A formatted string representing the time.\n */\nconst formatSLATime = (seconds: number): string => {\n  const units: { [key: string]: number } = {\n    y: 31536000, // 60 * 60 * 24 * 365\n    mo: 2592000, // 60 * 60 * 24 * 30\n    d: 86400, // 60 * 60 * 24\n    h: 3600, // 60 * 60\n    m: 60,\n  };\n\n  if (seconds < 60) {\n    return '1m';\n  }\n\n  // we will only show two parts, two max granularity's, h-m, y-d, d-h, m, but no seconds\n  const parts: string[] = [];\n\n  Object.keys(units).forEach(unit => {\n    const value = Math.floor(seconds / units[unit]);\n    if (seconds < 60 && parts.length > 0) return;\n    if (parts.length === 2) return;\n    if (value > 0) {\n      parts.push(value + unit);\n      seconds -= value * units[unit];\n    }\n  });\n  return parts.join(' ');\n};\n\n/**\n * Creates an SLA object based on the type, applied SLA, and chat details.\n * @param type - The type of SLA (FRT, NRT, RT).\n * @param appliedSla - The applied SLA details.\n * @param chat - The chat details.\n * @returns An object containing the SLA status or null if conditions are not met.\n */\nconst createSLAObject = (\n  type: string,\n  appliedSla: AppliedSla,\n  chat: Conversation\n): { threshold: number | null; type: string; condition: boolean } | null => {\n  const {\n    sla_first_response_time_threshold: frtThreshold,\n    sla_next_response_time_threshold: nrtThreshold,\n    sla_resolution_time_threshold: rtThreshold,\n    created_at: createdAt,\n  } = appliedSla;\n\n  const {\n    first_reply_created_at: firstReplyCreatedAt,\n    waiting_since: waitingSince,\n    status,\n  } = chat;\n\n  const SLATypes: {\n    [key: string]: { threshold: number | null; condition: boolean };\n  } = {\n    FRT: {\n      threshold: calculateThreshold(createdAt, frtThreshold),\n      //   Check FRT only if threshold is not null and first reply hasn't been made\n      condition:\n        frtThreshold !== null &&\n        (!firstReplyCreatedAt || firstReplyCreatedAt === 0),\n    },\n    NRT: {\n      threshold: calculateThreshold(waitingSince, nrtThreshold),\n      // Check NRT only if threshold is not null, first reply has been made and we are waiting since\n      condition:\n        nrtThreshold !== null && !!firstReplyCreatedAt && !!waitingSince,\n    },\n    RT: {\n      threshold: calculateThreshold(createdAt, rtThreshold),\n      // Check RT only if the conversation is open and threshold is not null\n      condition: status === 'open' && rtThreshold !== null,\n    },\n  };\n\n  const SLAStatus = SLATypes[type];\n  return SLAStatus ? { ...SLAStatus, type } : null;\n};\n\n/**\n * Evaluates SLA conditions and returns an array of SLAStatus objects.\n * @param appliedSla - The applied SLA details.\n * @param chat - The chat details.\n * @returns An array of SLAStatus objects.\n */\nconst evaluateSLAConditions = (\n  appliedSla: AppliedSla,\n  chat: Conversation\n): {\n  threshold: number;\n  type: string;\n  icon: string;\n  isSlaMissed: boolean;\n}[] => {\n  // Filter out the SLA based on conditions and update the object with the breach status(icon, isSlaMissed)\n  const SLATypes = ['FRT', 'NRT', 'RT'];\n  return SLATypes.map(type => createSLAObject(type, appliedSla, chat))\n    .filter(\n      (\n        SLAStatus\n      ): SLAStatus is { threshold: number; type: string; condition: boolean } =>\n        !!SLAStatus && SLAStatus.condition\n    )\n    .map(SLAStatus => ({\n      ...SLAStatus,\n      icon: SLAStatus.threshold <= 0 ? 'flame' : 'alarm',\n      isSlaMissed: SLAStatus.threshold <= 0,\n    }));\n};\n\n/**\n * Evaluates the SLA status for a given chat and applied SLA.\n * @param {Object} params - The parameters object.\n * @param params.appliedSla - The applied SLA details.\n * @param params.chat - The chat details.\n * @returns An object containing the most urgent SLA status.\n */\nexport const evaluateSLAStatus = ({\n  appliedSla,\n  chat,\n}: {\n  appliedSla: AppliedSla;\n  chat: Conversation;\n}): { type: string; threshold: string; icon: string; isSlaMissed: boolean } => {\n  if (!appliedSla || !chat)\n    return { type: '', threshold: '', icon: '', isSlaMissed: false };\n\n  // Filter out the SLA and create the object for each breach\n  const SLAStatuses = evaluateSLAConditions(appliedSla, chat) as SLAStatus[];\n\n  // Return the most urgent SLA which is latest to breach or has missed\n  const mostUrgent = findMostUrgentSLAStatus(SLAStatuses);\n  return mostUrgent\n    ? {\n        type: mostUrgent?.type,\n        threshold: formatSLATime(\n          mostUrgent.threshold <= 0\n            ? -mostUrgent.threshold\n            : mostUrgent.threshold\n        ),\n        icon: mostUrgent.icon,\n        isSlaMissed: mostUrgent.isSlaMissed,\n      }\n    : { type: '', threshold: '', icon: '', isSlaMissed: false };\n};\n", "/**\n * Parses various date formats into a JavaScript Date object\n *\n * This function handles different date input formats commonly found in conversation data:\n * - 10-digit timestamps (Unix seconds) - automatically converted to milliseconds\n * - 13-digit timestamps (Unix milliseconds) - used directly\n * - String representations of timestamps\n * - ISO date strings (e.g., \"2025-06-01T12:30:00Z\")\n * - Simple date strings (e.g., \"2025-06-01\") - time defaults to 00:00:00\n * - Date strings with space-separated time (e.g., \"2025-06-01 12:30:00\")\n *\n * Note: This function follows JavaScript Date constructor behavior for date parsing.\n * Some invalid dates like \"2025-02-30\" auto-correct to valid dates (becomes \"2025-03-02\"),\n * while malformed strings like \"2025-13-01\" or \"2025-06-01T25:00:00\" return null.\n *\n * @example\n * coerceToDate('2025-06-01') // Returns Date object set to 2025-06-01 00:00:00\n * coerceToDate('2025-06-01T12:30:00Z') // Returns Date object with specified time\n * coerceToDate(1748834578) // Returns Date object (10-digit timestamp in seconds)\n * coerceToDate(1748834578000) // Returns Date object (13-digit timestamp in milliseconds)\n * coerceToDate('1748834578') // Returns Date object (string timestamp converted)\n * coerceToDate(null) // Returns null\n * coerceToDate('invalid-date') // Returns null\n */\nexport const coerceToDate = (\n  dateInput: string | number | null | undefined\n): Date | null => {\n  if (dateInput == null) return null;\n\n  let timestamp = typeof dateInput === 'number' ? dateInput : null;\n\n  // Handle string inputs that represent numeric timestamps\n  if (\n    timestamp === null &&\n    typeof dateInput === 'string' &&\n    /^\\d+$/.test(dateInput)\n  ) {\n    timestamp = Number(dateInput);\n  }\n\n  // Process numeric timestamps\n  if (timestamp !== null) {\n    // Convert 10-digit timestamps (seconds) to milliseconds\n    const timestampMs =\n      timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;\n    return new Date(timestampMs);\n  }\n\n  // Process string date inputs\n  if (typeof dateInput === 'string') {\n    const dateObj = new Date(dateInput);\n\n    // Return null for invalid dates\n    if (Number.isNaN(dateObj.getTime())) return null;\n\n    // If no time component is specified, set time to 00:00:00\n    // this is because by default JS will set the time to midnight UTC for that date\n    const hasTimeComponent =\n      /T\\d{2}:\\d{2}(:\\d{2})?/.test(dateInput) || /\\d{2}:\\d{2}/.test(dateInput);\n    if (!hasTimeComponent) {\n      dateObj.setHours(0, 0, 0, 0);\n    }\n\n    return dateObj;\n  }\n\n  return null;\n};\n"], "names": ["debounce", "func", "wait", "immediate", "max<PERSON><PERSON>", "timeout", "lastInvokeTime", "args", "time", "Date", "now", "isFirstCall", "apply", "clearTimeout", "timeSinceLastInvoke", "shouldInvokeNow", "undefined", "setTimeout", "getContrastingTextColor", "bgColor", "color", "replace", "r", "parseInt", "slice", "g", "b", "formatDate", "date", "todayText", "yesterdayText", "dateValue", "isToday", "isYesterday", "formatTime", "timeInSeconds", "formattedTime", "minutes", "Math", "floor", "seconds", "hours", "days", "trimContent", "content", "max<PERSON><PERSON><PERSON>", "ellipsis", "<PERSON><PERSON><PERSON>nt", "length", "substring", "convertSecondsToTimeUnit", "unitNames", "unit", "Number", "toFixed", "minute", "hour", "day", "fileNameWithEllipsis", "file", "fullName", "filename", "name", "dotIndex", "lastIndexOf", "extension", "splitName", "trimmedName", "trim", "firstName", "lastName", "nameParts", "split", "pop", "join", "downloadFile", "url", "type", "Error", "fetch", "cache", "response", "ok", "status", "blob", "blobData", "contentType", "headers", "get", "fileExtension", "<PERSON><PERSON><PERSON><PERSON>", "filenameMatch", "match", "blobUrl", "URL", "createObjectURL", "link", "Object", "assign", "document", "createElement", "href", "download", "style", "body", "append", "click", "remove", "revokeObjectURL", "getFileInfo", "defaultInfo", "base", "cleanUrl", "encodedFilename", "fileName", "decodeURIComponent", "startsWith", "includes", "lastDotIndex", "toLowerCase", "error", "console", "formatNumber", "num", "n", "Intl", "NumberFormat", "notation", "maximumFractionDigits", "format", "toURL", "input", "window", "location", "origin", "isSameHost", "url1", "url2", "urlObj1", "urlObj2", "hostname", "isValidDomain", "domain", "domainRegex", "test", "MessageType", "getRecipients", "lastEmail", "conversationContact", "inboxEmail", "forwardToEmail", "to", "cc", "bcc", "messageType", "message_type", "isIncoming", "INCOMING", "emailAttributes", "contentAttributes", "content_attributes", "email", "from", "cc_emails", "ccEmails", "bcc_emails", "bccEmails", "to_emails", "toEmails", "isLastEmailFromContact", "push", "Array", "isArray", "filter", "emailAddress", "replyUUIDPattern", "Set", "parseBoolean", "candidate", "candidate<PERSON><PERSON>", "String", "Boolean", "JSON", "parse", "sortAsc", "arr", "sort", "a", "quantile", "q", "sorted", "_quantileForSorted", "clamp", "min", "max", "value", "clamped", "pos", "rest", "getQuantileIntervals", "data", "intervals", "map", "interval", "calculateCenterOffset", "mouseX", "mouseY", "rect", "centerX", "left", "width", "centerY", "top", "height", "relativeX", "relativeY", "applyRotationTransform", "angle", "radians", "PI", "cos", "sin", "rotatedX", "rotatedY", "normalizeToPercentage", "x", "y", "MESSAGE_VARIABLES_REGEX", "skipCode<PERSON>locks", "str", "capitalizeName", "word", "char<PERSON>t", "toUpperCase", "getFirstName", "user", "shift", "getLastName", "getMessageVariables", "conversation", "contact", "inbox", "meta", "assignee", "sender", "id", "custom_attributes", "conversationCustomAttributes", "contactCustomAttributes", "standardVariables", "phone_number", "conversationCustomAttributeVariables", "entries", "reduce", "acc", "key", "contactCustomAttributeVariables", "variables", "replaceVariablesInMessage", "message", "_", "getUndefinedVariablesInMessage", "messageWithOutCodeBlocks", "matches", "variable", "createTypingIndicator", "onStartTyping", "onStopTyping", "idleTime", "timer", "start", "reset", "stop", "calculateThreshold", "timeOffset", "threshold", "currentTime", "findMostUrgentSLAStatus", "SLAStatuses", "sla1", "sla2", "abs", "formatSLATime", "units", "mo", "d", "h", "m", "parts", "keys", "for<PERSON>ach", "createSLAObject", "appliedSla", "chat", "frtThreshold", "sla_first_response_time_threshold", "nrtThreshold", "sla_next_response_time_threshold", "rtThreshold", "sla_resolution_time_threshold", "createdAt", "created_at", "firstReplyCreatedAt", "first_reply_created_at", "waitingSince", "waiting_since", "SLATypes", "FRT", "condition", "NRT", "RT", "SLAStatus", "evaluateSLAConditions", "icon", "isSlaMissed", "evaluateSLAStatus", "mostUrgent", "coerceToDate", "dateInput", "timestamp", "timestampMs", "toString", "date<PERSON><PERSON>j", "isNaN", "getTime", "hasTimeComponent", "setHours"], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6BaA,QAAQ,GAAG,SAAXA,QAAW,CACtBC,IADsB,EAEtBC,IAFsB,EAGtBC,SAHsB,EAItBC,OAJsB;AAMtB,MAAIC,OAAO,GAAyC,IAApD;AACA,MAAIC,cAAc,GAAG,CAArB;AAEA,SAAO;;;sCAAuBC;AAAAA,MAAAA;;;AAC5B,QAAMC,IAAI,GAAGC,IAAI,CAACC,GAAL,EAAb;AACA,QAAMC,WAAW,GAAGL,cAAc,KAAK,CAAvC;;AAGA,QAAIK,WAAW,IAAIR,SAAnB,EAA8B;AAC5BG,MAAAA,cAAc,GAAGE,IAAjB;AACAP,MAAAA,IAAI,CAACW,KAAL,CAAW,IAAX,EAAiBL,IAAjB;AACA;AACD;;;AAGD,QAAIF,OAAO,KAAK,IAAhB,EAAsB;AACpBQ,MAAAA,YAAY,CAACR,OAAD,CAAZ;AACAA,MAAAA,OAAO,GAAG,IAAV;AACD;;;AAGD,QAAMS,mBAAmB,GAAGN,IAAI,GAAGF,cAAnC;AACA,QAAMS,eAAe,GACnBX,OAAO,KAAKY,SAAZ,IAAyBF,mBAAmB,IAAIV,OADlD;;AAGA,QAAIW,eAAJ,EAAqB;AACnBT,MAAAA,cAAc,GAAGE,IAAjB;AACAP,MAAAA,IAAI,CAACW,KAAL,CAAW,IAAX,EAAiBL,IAAjB;AACA;AACD;;;AAGDF,IAAAA,OAAO,GAAGY,UAAU,CAAC;AACnBX,MAAAA,cAAc,GAAGG,IAAI,CAACC,GAAL,EAAjB;AACAL,MAAAA,OAAO,GAAG,IAAV;AACAJ,MAAAA,IAAI,CAACW,KAAL,CAAW,KAAX,EAAiBL,IAAjB;AACD,KAJmB,EAIjBL,IAJiB,CAApB;AAKD,GAlCD;AAmCD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,IAAI,UAAU,OAAO,EAAE;AAClC,AACA;AACA,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC;AAC5B,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC,cAAc,CAAC;AACjC,EAAE,IAAIc,WAAS,CAAC;AAChB,EAAE,IAAI,OAAO,GAAG,OAAO,MAAM,KAAK,UAAU,GAAG,MAAM,GAAG,EAAE,CAAC;AAC3D,EAAE,IAAI,cAAc,GAAG,OAAO,CAAC,QAAQ,IAAI,YAAY,CAAC;AACxD,EAAE,IAAI,mBAAmB,GAAG,OAAO,CAAC,aAAa,IAAI,iBAAiB,CAAC;AACvE,EAAE,IAAI,iBAAiB,GAAG,OAAO,CAAC,WAAW,IAAI,eAAe,CAAC;AACjE;AACA,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;AACnC,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE;AACpC,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,UAAU,EAAE,IAAI;AACtB,MAAM,YAAY,EAAE,IAAI;AACxB,MAAM,QAAQ,EAAE,IAAI;AACpB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;AACpB,GAAG;AACH,EAAE,IAAI;AACN;AACA,IAAI,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACnB,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,MAAM,GAAG,SAAS,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;AACvC,MAAM,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC9B,KAAK,CAAC;AACN,GAAG;AACH;AACA,EAAE,SAAS,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE;AACrD;AACA,IAAI,IAAI,cAAc,GAAG,OAAO,IAAI,OAAO,CAAC,SAAS,YAAY,SAAS,GAAG,OAAO,GAAG,SAAS,CAAC;AACjG,IAAI,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;AAC5D,IAAI,IAAI,OAAO,GAAG,IAAI,OAAO,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;AACjD;AACA;AACA;AACA,IAAI,SAAS,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AACjE;AACA,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG;AACH,EAAE,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AAClC,IAAI,IAAI;AACR,MAAM,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;AACxD,KAAK,CAAC,OAAO,GAAG,EAAE;AAClB,MAAM,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACzC,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,sBAAsB,GAAG,gBAAgB,CAAC;AAChD,EAAE,IAAI,sBAAsB,GAAG,gBAAgB,CAAC;AAChD,EAAE,IAAI,iBAAiB,GAAG,WAAW,CAAC;AACtC,EAAE,IAAI,iBAAiB,GAAG,WAAW,CAAC;AACtC;AACA;AACA;AACA,EAAE,IAAI,gBAAgB,GAAG,EAAE,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,SAAS,GAAG,EAAE;AACzB,EAAE,SAAS,iBAAiB,GAAG,EAAE;AACjC,EAAE,SAAS,0BAA0B,GAAG,EAAE;AAC1C;AACA;AACA;AACA,EAAE,IAAI,iBAAiB,GAAG,EAAE,CAAC;AAC7B,EAAE,iBAAiB,CAAC,cAAc,CAAC,GAAG,YAAY;AAClD,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,CAAC;AACJ;AACA,EAAE,IAAI,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAC;AACvC,EAAE,IAAI,uBAAuB,GAAG,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3E,EAAE,IAAI,uBAAuB;AAC7B,MAAM,uBAAuB,KAAK,EAAE;AACpC,MAAM,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,cAAc,CAAC,EAAE;AAC5D;AACA;AACA,IAAI,iBAAiB,GAAG,uBAAuB,CAAC;AAChD,GAAG;AACH;AACA,EAAE,IAAI,EAAE,GAAG,0BAA0B,CAAC,SAAS;AAC/C,IAAI,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAC3D,EAAE,iBAAiB,CAAC,SAAS,GAAG,EAAE,CAAC,WAAW,GAAG,0BAA0B,CAAC;AAC5E,EAAE,0BAA0B,CAAC,WAAW,GAAG,iBAAiB,CAAC;AAC7D,EAAE,iBAAiB,CAAC,WAAW,GAAG,MAAM;AACxC,IAAI,0BAA0B;AAC9B,IAAI,iBAAiB;AACrB,IAAI,mBAAmB;AACvB,GAAG,CAAC;AACJ;AACA;AACA;AACA,EAAE,SAAS,qBAAqB,CAAC,SAAS,EAAE;AAC5C,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,SAAS,MAAM,EAAE;AACzD,MAAM,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,GAAG,EAAE;AAC9C,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AACzC,OAAO,CAAC,CAAC;AACT,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA,EAAE,OAAO,CAAC,mBAAmB,GAAG,SAAS,MAAM,EAAE;AACjD,IAAI,IAAI,IAAI,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,MAAM,CAAC,WAAW,CAAC;AAClE,IAAI,OAAO,IAAI;AACf,QAAQ,IAAI,KAAK,iBAAiB;AAClC;AACA;AACA,QAAQ,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,MAAM,mBAAmB;AAC/D,QAAQ,KAAK,CAAC;AACd,GAAG,CAAC;AACJ;AACA,EAAE,OAAO,CAAC,IAAI,GAAG,SAAS,MAAM,EAAE;AAClC,IAAI,IAAI,MAAM,CAAC,cAAc,EAAE;AAC/B,MAAM,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,0BAA0B,CAAC,CAAC;AAChE,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,SAAS,GAAG,0BAA0B,CAAC;AACpD,MAAM,MAAM,CAAC,MAAM,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;AAC7D,KAAK;AACL,IAAI,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACzC,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,KAAK,GAAG,SAAS,GAAG,EAAE;AAChC,IAAI,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;AAC5B,GAAG,CAAC;AACJ;AACA,EAAE,SAAS,aAAa,CAAC,SAAS,EAAE,WAAW,EAAE;AACjD,IAAI,SAAS,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE;AAClD,MAAM,IAAI,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;AAC/D,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE;AACnC,QAAQ,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC3B,OAAO,MAAM;AACb,QAAQ,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC;AAChC,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AACjC,QAAQ,IAAI,KAAK;AACjB,YAAY,OAAO,KAAK,KAAK,QAAQ;AACrC,YAAY,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE;AAC3C,UAAU,OAAO,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,KAAK,EAAE;AACzE,YAAY,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AACnD,WAAW,EAAE,SAAS,GAAG,EAAE;AAC3B,YAAY,MAAM,CAAC,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AAClD,WAAW,CAAC,CAAC;AACb,SAAS;AACT;AACA,QAAQ,OAAO,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,SAAS,EAAE;AACnE;AACA;AACA;AACA,UAAU,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC;AACnC,UAAU,OAAO,CAAC,MAAM,CAAC,CAAC;AAC1B,SAAS,EAAE,SAAS,KAAK,EAAE;AAC3B;AACA;AACA,UAAU,OAAO,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AACzD,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK;AACL;AACA,IAAI,IAAI,eAAe,CAAC;AACxB;AACA,IAAI,SAAS,OAAO,CAAC,MAAM,EAAE,GAAG,EAAE;AAClC,MAAM,SAAS,0BAA0B,GAAG;AAC5C,QAAQ,OAAO,IAAI,WAAW,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE;AACzD,UAAU,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AAC/C,SAAS,CAAC,CAAC;AACX,OAAO;AACP;AACA,MAAM,OAAO,eAAe;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,eAAe,GAAG,eAAe,CAAC,IAAI;AAC9C,UAAU,0BAA0B;AACpC;AACA;AACA,UAAU,0BAA0B;AACpC,SAAS,GAAG,0BAA0B,EAAE,CAAC;AACzC,KAAK;AACL;AACA;AACA;AACA,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,GAAG;AACH;AACA,EAAE,qBAAqB,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AACjD,EAAE,aAAa,CAAC,SAAS,CAAC,mBAAmB,CAAC,GAAG,YAAY;AAC7D,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;AACxC;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,KAAK,GAAG,SAAS,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE;AAC7E,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC;AACtD;AACA,IAAI,IAAI,IAAI,GAAG,IAAI,aAAa;AAChC,MAAM,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,CAAC;AAC/C,MAAM,WAAW;AACjB,KAAK,CAAC;AACN;AACA,IAAI,OAAO,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC;AAC/C,QAAQ,IAAI;AACZ,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,SAAS,MAAM,EAAE;AAC1C,UAAU,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AAC1D,SAAS,CAAC,CAAC;AACX,GAAG,CAAC;AACJ;AACA,EAAE,SAAS,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;AACpD,IAAI,IAAI,KAAK,GAAG,sBAAsB,CAAC;AACvC;AACA,IAAI,OAAO,SAAS,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE;AACxC,MAAM,IAAI,KAAK,KAAK,iBAAiB,EAAE;AACvC,QAAQ,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;AACxD,OAAO;AACP;AACA,MAAM,IAAI,KAAK,KAAK,iBAAiB,EAAE;AACvC,QAAQ,IAAI,MAAM,KAAK,OAAO,EAAE;AAChC,UAAU,MAAM,GAAG,CAAC;AACpB,SAAS;AACT;AACA;AACA;AACA,QAAQ,OAAO,UAAU,EAAE,CAAC;AAC5B,OAAO;AACP;AACA,MAAM,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;AAC9B,MAAM,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AACxB;AACA,MAAM,OAAO,IAAI,EAAE;AACnB,QAAQ,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AACxC,QAAQ,IAAI,QAAQ,EAAE;AACtB,UAAU,IAAI,cAAc,GAAG,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACtE,UAAU,IAAI,cAAc,EAAE;AAC9B,YAAY,IAAI,cAAc,KAAK,gBAAgB,EAAE,SAAS;AAC9D,YAAY,OAAO,cAAc,CAAC;AAClC,WAAW;AACX,SAAS;AACT;AACA,QAAQ,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE;AACvC;AACA;AACA,UAAU,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC;AACrD;AACA,SAAS,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,EAAE;AAC/C,UAAU,IAAI,KAAK,KAAK,sBAAsB,EAAE;AAChD,YAAY,KAAK,GAAG,iBAAiB,CAAC;AACtC,YAAY,MAAM,OAAO,CAAC,GAAG,CAAC;AAC9B,WAAW;AACX;AACA,UAAU,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACjD;AACA,SAAS,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE;AAChD,UAAU,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AAChD,SAAS;AACT;AACA,QAAQ,KAAK,GAAG,iBAAiB,CAAC;AAClC;AACA,QAAQ,IAAI,MAAM,GAAG,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AACtD,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE;AACtC;AACA;AACA,UAAU,KAAK,GAAG,OAAO,CAAC,IAAI;AAC9B,cAAc,iBAAiB;AAC/B,cAAc,sBAAsB,CAAC;AACrC;AACA,UAAU,IAAI,MAAM,CAAC,GAAG,KAAK,gBAAgB,EAAE;AAC/C,YAAY,SAAS;AACrB,WAAW;AACX;AACA,UAAU,OAAO;AACjB,YAAY,KAAK,EAAE,MAAM,CAAC,GAAG;AAC7B,YAAY,IAAI,EAAE,OAAO,CAAC,IAAI;AAC9B,WAAW,CAAC;AACZ;AACA,SAAS,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE;AAC5C,UAAU,KAAK,GAAG,iBAAiB,CAAC;AACpC;AACA;AACA,UAAU,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC;AACnC,UAAU,OAAO,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;AACnC,SAAS;AACT,OAAO;AACP,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,mBAAmB,CAAC,QAAQ,EAAE,OAAO,EAAE;AAClD,IAAI,IAAI,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnD,IAAI,IAAI,MAAM,KAAKA,WAAS,EAAE;AAC9B;AACA;AACA,MAAM,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC9B;AACA,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,EAAE;AACtC;AACA,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACzC;AACA;AACA,UAAU,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC;AACpC,UAAU,OAAO,CAAC,GAAG,GAAGA,WAAS,CAAC;AAClC,UAAU,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACjD;AACA,UAAU,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,EAAE;AAC1C;AACA;AACA,YAAY,OAAO,gBAAgB,CAAC;AACpC,WAAW;AACX,SAAS;AACT;AACA,QAAQ,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC;AACjC,QAAQ,OAAO,CAAC,GAAG,GAAG,IAAI,SAAS;AACnC,UAAU,gDAAgD,CAAC,CAAC;AAC5D,OAAO;AACP;AACA,MAAM,OAAO,gBAAgB,CAAC;AAC9B,KAAK;AACL;AACA,IAAI,IAAI,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AAClE;AACA,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE;AACjC,MAAM,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC;AAC/B,MAAM,OAAO,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;AAC/B,MAAM,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC9B,MAAM,OAAO,gBAAgB,CAAC;AAC9B,KAAK;AACL;AACA,IAAI,IAAI,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC;AAC1B;AACA,IAAI,IAAI,EAAE,IAAI,EAAE;AAChB,MAAM,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC;AAC/B,MAAM,OAAO,CAAC,GAAG,GAAG,IAAI,SAAS,CAAC,kCAAkC,CAAC,CAAC;AACtE,MAAM,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC9B,MAAM,OAAO,gBAAgB,CAAC;AAC9B,KAAK;AACL;AACA,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;AACnB;AACA;AACA,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;AAChD;AACA;AACA,MAAM,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE;AACvC,QAAQ,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;AAChC,QAAQ,OAAO,CAAC,GAAG,GAAGA,WAAS,CAAC;AAChC,OAAO;AACP;AACA,KAAK,MAAM;AACX;AACA,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL;AACA;AACA;AACA,IAAI,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC5B,IAAI,OAAO,gBAAgB,CAAC;AAC5B,GAAG;AACH;AACA;AACA;AACA,EAAE,qBAAqB,CAAC,EAAE,CAAC,CAAC;AAC5B;AACA,EAAE,MAAM,CAAC,EAAE,EAAE,iBAAiB,EAAE,WAAW,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,EAAE,CAAC,cAAc,CAAC,GAAG,WAAW;AAClC,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,CAAC;AACJ;AACA,EAAE,EAAE,CAAC,QAAQ,GAAG,WAAW;AAC3B,IAAI,OAAO,oBAAoB,CAAC;AAChC,GAAG,CAAC;AACJ;AACA,EAAE,SAAS,YAAY,CAAC,IAAI,EAAE;AAC9B,IAAI,IAAI,KAAK,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;AACpC;AACA,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;AACnB,MAAM,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/B,KAAK;AACL;AACA,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;AACnB,MAAM,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACjC,MAAM,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/B,KAAK;AACL;AACA,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,GAAG;AACH;AACA,EAAE,SAAS,aAAa,CAAC,KAAK,EAAE;AAChC,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC;AACxC,IAAI,MAAM,CAAC,IAAI,GAAG,QAAQ,CAAC;AAC3B,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC;AACtB,IAAI,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC;AAC9B,GAAG;AACH;AACA,EAAE,SAAS,OAAO,CAAC,WAAW,EAAE;AAChC;AACA;AACA;AACA,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;AAC3C,IAAI,WAAW,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACrB,GAAG;AACH;AACA,EAAE,OAAO,CAAC,IAAI,GAAG,SAAS,MAAM,EAAE;AAClC,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AAClB,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;AAC5B,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrB,KAAK;AACL,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;AACnB;AACA;AACA;AACA,IAAI,OAAO,SAAS,IAAI,GAAG;AAC3B,MAAM,OAAO,IAAI,CAAC,MAAM,EAAE;AAC1B,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AAC7B,QAAQ,IAAI,GAAG,IAAI,MAAM,EAAE;AAC3B,UAAU,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3B,UAAU,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;AAC5B,UAAU,OAAO,IAAI,CAAC;AACtB,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACvB,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,CAAC;AACN,GAAG,CAAC;AACJ;AACA,EAAE,SAAS,MAAM,CAAC,QAAQ,EAAE;AAC5B,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,IAAI,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC;AACpD,MAAM,IAAI,cAAc,EAAE;AAC1B,QAAQ,OAAO,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC7C,OAAO;AACP;AACA,MAAM,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE;AAC/C,QAAQ,OAAO,QAAQ,CAAC;AACxB,OAAO;AACP;AACA,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACnC,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,SAAS,IAAI,GAAG;AAC3C,UAAU,OAAO,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE;AACxC,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE;AAC1C,cAAc,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACvC,cAAc,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;AAChC,cAAc,OAAO,IAAI,CAAC;AAC1B,aAAa;AACb,WAAW;AACX;AACA,UAAU,IAAI,CAAC,KAAK,GAAGA,WAAS,CAAC;AACjC,UAAU,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AAC3B;AACA,UAAU,OAAO,IAAI,CAAC;AACtB,SAAS,CAAC;AACV;AACA,QAAQ,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AAChC,OAAO;AACP,KAAK;AACL;AACA;AACA,IAAI,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;AAChC,GAAG;AACH,EAAE,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;AAC1B;AACA,EAAE,SAAS,UAAU,GAAG;AACxB,IAAI,OAAO,EAAE,KAAK,EAAEA,WAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AAC5C,GAAG;AACH;AACA,EAAE,OAAO,CAAC,SAAS,GAAG;AACtB,IAAI,WAAW,EAAE,OAAO;AACxB;AACA,IAAI,KAAK,EAAE,SAAS,aAAa,EAAE;AACnC,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AACpB,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AACpB;AACA;AACA,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAGA,WAAS,CAAC;AACzC,MAAM,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;AACxB,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC3B;AACA,MAAM,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AAC3B,MAAM,IAAI,CAAC,GAAG,GAAGA,WAAS,CAAC;AAC3B;AACA,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAC7C;AACA,MAAM,IAAI,CAAC,aAAa,EAAE;AAC1B,QAAQ,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;AAC/B;AACA,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;AACpC,cAAc,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;AACrC,cAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;AACtC,YAAY,IAAI,CAAC,IAAI,CAAC,GAAGA,WAAS,CAAC;AACnC,WAAW;AACX,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA,IAAI,IAAI,EAAE,WAAW;AACrB,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACvB;AACA,MAAM,IAAI,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACzC,MAAM,IAAI,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;AAC5C,MAAM,IAAI,UAAU,CAAC,IAAI,KAAK,OAAO,EAAE;AACvC,QAAQ,MAAM,UAAU,CAAC,GAAG,CAAC;AAC7B,OAAO;AACP;AACA,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC;AACvB,KAAK;AACL;AACA,IAAI,iBAAiB,EAAE,SAAS,SAAS,EAAE;AAC3C,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE;AACrB,QAAQ,MAAM,SAAS,CAAC;AACxB,OAAO;AACP;AACA,MAAM,IAAI,OAAO,GAAG,IAAI,CAAC;AACzB,MAAM,SAAS,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE;AACnC,QAAQ,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC;AAC9B,QAAQ,MAAM,CAAC,GAAG,GAAG,SAAS,CAAC;AAC/B,QAAQ,OAAO,CAAC,IAAI,GAAG,GAAG,CAAC;AAC3B;AACA,QAAQ,IAAI,MAAM,EAAE;AACpB;AACA;AACA,UAAU,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;AAClC,UAAU,OAAO,CAAC,GAAG,GAAGA,WAAS,CAAC;AAClC,SAAS;AACT;AACA,QAAQ,OAAO,CAAC,EAAE,MAAM,CAAC;AACzB,OAAO;AACP;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AAC5D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACvC,QAAQ,IAAI,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC;AACtC;AACA,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,EAAE;AACrC;AACA;AACA;AACA,UAAU,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;AAC/B,SAAS;AACT;AACA,QAAQ,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE;AACvC,UAAU,IAAI,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;AACxD,UAAU,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AAC5D;AACA,UAAU,IAAI,QAAQ,IAAI,UAAU,EAAE;AACtC,YAAY,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE;AAC5C,cAAc,OAAO,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAClD,aAAa,MAAM,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,UAAU,EAAE;AACrD,cAAc,OAAO,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AAC9C,aAAa;AACb;AACA,WAAW,MAAM,IAAI,QAAQ,EAAE;AAC/B,YAAY,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE;AAC5C,cAAc,OAAO,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAClD,aAAa;AACb;AACA,WAAW,MAAM,IAAI,UAAU,EAAE;AACjC,YAAY,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,UAAU,EAAE;AAC9C,cAAc,OAAO,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AAC9C,aAAa;AACb;AACA,WAAW,MAAM;AACjB,YAAY,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AACtE,WAAW;AACX,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA,IAAI,MAAM,EAAE,SAAS,IAAI,EAAE,GAAG,EAAE;AAChC,MAAM,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AAC5D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACvC,QAAQ,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI;AACrC,YAAY,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;AAC5C,YAAY,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,UAAU,EAAE;AAC1C,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC;AACnC,UAAU,MAAM;AAChB,SAAS;AACT,OAAO;AACP;AACA,MAAM,IAAI,YAAY;AACtB,WAAW,IAAI,KAAK,OAAO;AAC3B,WAAW,IAAI,KAAK,UAAU,CAAC;AAC/B,UAAU,YAAY,CAAC,MAAM,IAAI,GAAG;AACpC,UAAU,GAAG,IAAI,YAAY,CAAC,UAAU,EAAE;AAC1C;AACA;AACA,QAAQ,YAAY,GAAG,IAAI,CAAC;AAC5B,OAAO;AACP;AACA,MAAM,IAAI,MAAM,GAAG,YAAY,GAAG,YAAY,CAAC,UAAU,GAAG,EAAE,CAAC;AAC/D,MAAM,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB,MAAM,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;AACvB;AACA,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AAC7B,QAAQ,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,UAAU,CAAC;AAC5C,QAAQ,OAAO,gBAAgB,CAAC;AAChC,OAAO;AACP;AACA,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACnC,KAAK;AACL;AACA,IAAI,QAAQ,EAAE,SAAS,MAAM,EAAE,QAAQ,EAAE;AACzC,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE;AACnC,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC;AACzB,OAAO;AACP;AACA,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO;AACjC,UAAU,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE;AACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC;AAC/B,OAAO,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC3C,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;AAC1C,QAAQ,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;AAC/B,QAAQ,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;AAC1B,OAAO,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,QAAQ,EAAE;AACvD,QAAQ,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;AAC7B,OAAO;AACP;AACA,MAAM,OAAO,gBAAgB,CAAC;AAC9B,KAAK;AACL;AACA,IAAI,MAAM,EAAE,SAAS,UAAU,EAAE;AACjC,MAAM,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AAC5D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACvC,QAAQ,IAAI,KAAK,CAAC,UAAU,KAAK,UAAU,EAAE;AAC7C,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC1D,UAAU,aAAa,CAAC,KAAK,CAAC,CAAC;AAC/B,UAAU,OAAO,gBAAgB,CAAC;AAClC,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA,IAAI,OAAO,EAAE,SAAS,MAAM,EAAE;AAC9B,MAAM,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AAC5D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACvC,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,EAAE;AACrC,UAAU,IAAI,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC;AACxC,UAAU,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE;AACvC,YAAY,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC;AACpC,YAAY,aAAa,CAAC,KAAK,CAAC,CAAC;AACjC,WAAW;AACX,UAAU,OAAO,MAAM,CAAC;AACxB,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA,MAAM,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;AAC/C,KAAK;AACL;AACA,IAAI,aAAa,EAAE,SAAS,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE;AAC3D,MAAM,IAAI,CAAC,QAAQ,GAAG;AACtB,QAAQ,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC;AAClC,QAAQ,UAAU,EAAE,UAAU;AAC9B,QAAQ,OAAO,EAAE,OAAO;AACxB,OAAO,CAAC;AACR;AACA,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE;AAClC;AACA;AACA,QAAQ,IAAI,CAAC,GAAG,GAAGA,WAAS,CAAC;AAC7B,OAAO;AACP;AACA,MAAM,OAAO,gBAAgB,CAAC;AAC9B,KAAK;AACL,GAAG,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,OAAO,CAAC;AACjB;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,EAAE,CAA6B,MAAM,CAAC,OAAO,CAAK;AAClD,CAAC,CAAC,CAAC;AACH;AACA,IAAI;AACJ,EAAE,kBAAkB,GAAG,OAAO,CAAC;AAC/B,CAAC,CAAC,OAAO,oBAAoB,EAAE;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC,OAAO,CAAC,CAAC;AACnD;;;ACzuBA;;;;;;;AAMA,IAAaE,uBAAuB,GAAG,SAA1BA,uBAA0B,CAACC,OAAD;AACrC,MAAMC,KAAK,GAAGD,OAAO,CAACE,OAAR,CAAgB,GAAhB,EAAqB,EAArB,CAAd;AACA,MAAMC,CAAC,GAAGC,QAAQ,CAACH,KAAK,CAACI,KAAN,CAAY,CAAZ,EAAe,CAAf,CAAD,EAAoB,EAApB,CAAlB;AACA,MAAMC,CAAC,GAAGF,QAAQ,CAACH,KAAK,CAACI,KAAN,CAAY,CAAZ,EAAe,CAAf,CAAD,EAAoB,EAApB,CAAlB;AACA,MAAME,CAAC,GAAGH,QAAQ,CAACH,KAAK,CAACI,KAAN,CAAY,CAAZ,EAAe,CAAf,CAAD,EAAoB,EAApB,CAAlB;;AAEA,SAAOF,CAAC,GAAG,KAAJ,GAAYG,CAAC,GAAG,KAAhB,GAAwBC,CAAC,GAAG,KAA5B,GAAoC,GAApC,GAA0C,SAA1C,GAAsD,SAA7D;AACD,CAPM;AASP;;;;;;;;;AAQA,IAAaC,UAAU,GAAG,SAAbA,UAAa;MACxBC,YAAAA;MACAC,iBAAAA;MACAC,qBAAAA;AAMA,MAAMC,SAAS,GAAG,IAAItB,IAAJ,CAASmB,IAAT,CAAlB;AACA,MAAII,OAAO,CAACD,SAAD,CAAX,EAAwB,OAAOF,SAAP;AACxB,MAAII,WAAW,CAACF,SAAD,CAAf,EAA4B,OAAOD,aAAP;AAC5B,SAAOF,IAAP;AACD,CAbM;AAeP;;;;;;;AAOA,IAAaM,UAAU,GAAG,SAAbA,UAAa,CAACC,aAAD;AACxB,MAAIC,aAAa,GAAG,EAApB;;AACA,MAAID,aAAa,IAAI,EAAjB,IAAuBA,aAAa,GAAG,IAA3C,EAAiD;AAC/C,QAAME,OAAO,GAAGC,IAAI,CAACC,KAAL,CAAWJ,aAAa,GAAG,EAA3B,CAAhB;AACAC,IAAAA,aAAa,GAAMC,OAAN,SAAb;AACA,QAAMG,OAAO,GAAGH,OAAO,KAAK,EAAZ,GAAiB,CAAjB,GAAqBC,IAAI,CAACC,KAAL,CAAWJ,aAAa,GAAG,EAA3B,CAArC;AACA,WAAOC,aAAa,UAAMI,OAAO,GAAG,CAAV,GAAc,MAAMA,OAAN,GAAgB,MAA9B,GAAuC,EAA7C,EAApB;AACD;;AACD,MAAIL,aAAa,IAAI,IAAjB,IAAyBA,aAAa,GAAG,KAA7C,EAAoD;AAClD,QAAMM,KAAK,GAAGH,IAAI,CAACC,KAAL,CAAWJ,aAAa,GAAG,IAA3B,CAAd;AACAC,IAAAA,aAAa,GAAMK,KAAN,QAAb;;AACA,QAAMJ,QAAO,GACXF,aAAa,GAAG,IAAhB,GAAuB,EAAvB,IAA6BM,KAAK,KAAK,EAAvC,GACI,CADJ,GAEIH,IAAI,CAACC,KAAL,CAAYJ,aAAa,GAAG,IAAjB,GAAyB,EAApC,CAHN;;AAIA,WAAOC,aAAa,UAAMC,QAAO,GAAG,CAAV,GAAc,MAAMA,QAAN,GAAgB,MAA9B,GAAuC,EAA7C,EAApB;AACD;;AACD,MAAIF,aAAa,IAAI,KAArB,EAA4B;AAC1B,QAAMO,IAAI,GAAGJ,IAAI,CAACC,KAAL,CAAWJ,aAAa,GAAG,KAA3B,CAAb;AACAC,IAAAA,aAAa,GAAMM,IAAN,SAAb;;AACA,QAAMD,MAAK,GACTN,aAAa,GAAG,KAAhB,GAAwB,IAAxB,IAAgCO,IAAI,IAAI,GAAxC,GACI,CADJ,GAEIJ,IAAI,CAACC,KAAL,CAAYJ,aAAa,GAAG,KAAjB,GAA0B,IAArC,CAHN;;AAIA,WAAOC,aAAa,UAAMK,MAAK,GAAG,CAAR,GAAY,MAAMA,MAAN,GAAc,KAA1B,GAAkC,EAAxC,EAApB;AACD;;AACD,SAAUH,IAAI,CAACC,KAAL,CAAWJ,aAAX,CAAV;AACD,CA3BM;AA6BP;;;;;;;;;AAQA,IAAaQ,WAAW,GAAG,SAAdA,WAAc,CACzBC,OADyB,EAEzBC,SAFyB,EAGzBC,QAHyB;MACzBF;AAAAA,IAAAA,UAAkB;;;MAClBC;AAAAA,IAAAA,YAAoB;;;MACpBC;AAAAA,IAAAA,WAAoB;;;AAEpB,MAAIC,cAAc,GAAGH,OAArB;;AACA,MAAIA,OAAO,CAACI,MAAR,GAAiBH,SAArB,EAAgC;AAC9BE,IAAAA,cAAc,GAAGH,OAAO,CAACK,SAAR,CAAkB,CAAlB,EAAqBJ,SAArB,CAAjB;AACD;;AACD,MAAIC,QAAJ,EAAc;AACZC,IAAAA,cAAc,GAAGA,cAAc,GAAG,KAAlC;AACD;;AACD,SAAOA,cAAP;AACD,CAbM;AAeP;;;;;;;;;;;AAWA,IAAaG,wBAAwB,GAAG,SAA3BA,wBAA2B,CACtCV,OADsC,EAEtCW,SAFsC;AAItC,MAAIX,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,CAApC,EAAuC,OAAO;AAAEhC,IAAAA,IAAI,EAAE,EAAR;AAAY4C,IAAAA,IAAI,EAAE;AAAlB,GAAP;AACvC,MAAIZ,OAAO,GAAG,IAAd,EACE,OAAO;AAAEhC,IAAAA,IAAI,EAAE6C,MAAM,CAAC,CAACb,OAAO,GAAG,EAAX,EAAec,OAAf,CAAuB,CAAvB,CAAD,CAAd;AAA2CF,IAAAA,IAAI,EAAED,SAAS,CAACI;AAA3D,GAAP;AACF,MAAIf,OAAO,GAAG,KAAd,EACE,OAAO;AAAEhC,IAAAA,IAAI,EAAE6C,MAAM,CAAC,CAACb,OAAO,GAAG,IAAX,EAAiBc,OAAjB,CAAyB,CAAzB,CAAD,CAAd;AAA6CF,IAAAA,IAAI,EAAED,SAAS,CAACK;AAA7D,GAAP;AACF,SAAO;AAAEhD,IAAAA,IAAI,EAAE6C,MAAM,CAAC,CAACb,OAAO,GAAG,KAAX,EAAkBc,OAAlB,CAA0B,CAA1B,CAAD,CAAd;AAA8CF,IAAAA,IAAI,EAAED,SAAS,CAACM;AAA9D,GAAP;AACD,CAVM;AAYP;;;;;;;;;;;;AAWA,IAAaC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAClCC,IADkC,EAElCd,SAFkC,EAGlCC,QAHkC;;;MAElCD;AAAAA,IAAAA,YAAoB;;;MACpBC;AAAAA,IAAAA,WAAmB;;;AAEnB,MAAMc,QAAQ,8BAAGD,IAAH,oBAAGA,IAAI,CAAEE,QAAT,6BAAqBF,IAArB,oBAAqBA,IAAI,CAAEG,IAA3B,oBAAmC,UAAjD;AAEA,MAAMC,QAAQ,GAAGH,QAAQ,CAACI,WAAT,CAAqB,GAArB,CAAjB;AACA,MAAID,QAAQ,KAAK,CAAC,CAAlB,EAAqB,OAAOH,QAAP;AAErB,cAA0B,CACxBA,QAAQ,CAACpC,KAAT,CAAe,CAAf,EAAkBuC,QAAlB,CADwB,EAExBH,QAAQ,CAACpC,KAAT,CAAeuC,QAAf,CAFwB,CAA1B;AAAA,MAAOD,IAAP;AAAA,MAAaG,SAAb;AAKA,MAAIH,IAAI,CAACd,MAAL,IAAeH,SAAnB,EAA8B,OAAOe,QAAP;AAE9B,cAAUE,IAAI,CAACtC,KAAL,CAAW,CAAX,EAAcqB,SAAd,CAAV,GAAqCC,QAArC,GAAgDmB,SAAhD;AACD,CAlBM;AAoBP;;;;;;;;;;;;AAWA,IAAaC,SAAS,GAAG,SAAZA,SAAY,CACvBN,QADuB;AAGvB,MAAMO,WAAW,GAAGP,QAAQ,CAACQ,IAAT,EAApB;;AACA,MAAI,CAACD,WAAL,EAAkB;AAChB,WAAO;AACLE,MAAAA,SAAS,EAAE,EADN;AAELC,MAAAA,QAAQ,EAAE;AAFL,KAAP;AAID;;;AAGD,MAAMC,SAAS,GAAGJ,WAAW,CAACK,KAAZ,CAAkB,KAAlB,CAAlB;;AAGA,MAAID,SAAS,CAACvB,MAAV,KAAqB,CAAzB,EAA4B;AAC1B,WAAO;AACLqB,MAAAA,SAAS,EAAEE,SAAS,CAAC,CAAD,CADf;AAELD,MAAAA,QAAQ,EAAE;AAFL,KAAP;AAID;;;AAGD,MAAMA,QAAQ,GAAGC,SAAS,CAACE,GAAV,MAAmB,EAApC;AACA,MAAMJ,SAAS,GAAGE,SAAS,CAACG,IAAV,CAAe,GAAf,CAAlB;AAEA,SAAO;AAAEL,IAAAA,SAAS,EAATA,SAAF;AAAaC,IAAAA,QAAQ,EAARA;AAAb,GAAP;AACD,CA3BM;AAkCP;;;;;;;;;;;AAUA,IAAaK,YAAY;AAAA,0EAAG;AAAA;;AAAA;AAAA;AAAA;AAAA;AAC1BC,YAAAA,GAD0B,SAC1BA,GAD0B,EAE1BC,IAF0B,SAE1BA,IAF0B,0BAG1BZ,SAH0B,EAG1BA,SAH0B,gCAGd,IAHc;;AAAA,kBAKtB,CAACW,GAAD,IAAQ,CAACC,IALa;AAAA;AAAA;AAAA;;AAAA,kBAMlB,IAAIC,KAAJ,CAAU,6BAAV,CANkB;;AAAA;AAAA;AAAA;AAAA,mBAUDC,KAAK,CAACH,GAAD,EAAM;AAAEI,cAAAA,KAAK,EAAE;AAAT,aAAN,CAVJ;;AAAA;AAUlBC,YAAAA,QAVkB;;AAAA,gBAYnBA,QAAQ,CAACC,EAZU;AAAA;AAAA;AAAA;;AAAA,kBAahB,IAAIJ,KAAJ,uBAA8BG,QAAQ,CAACE,MAAvC,CAbgB;;AAAA;AAAA;AAAA,mBAgBDF,QAAQ,CAACG,IAAT,EAhBC;;AAAA;AAgBlBC,YAAAA,QAhBkB;AAkBlBC,YAAAA,WAlBkB,GAkBJL,QAAQ,CAACM,OAAT,CAAiBC,GAAjB,CAAqB,cAArB,CAlBI;AAoBlBC,YAAAA,aApBkB,GAqBtBxB,SAAS,KAAKqB,WAAW,GAAGA,WAAW,CAACd,KAAZ,CAAkB,GAAlB,EAAuB,CAAvB,CAAH,GAA+BK,IAA/C,CArBa;AAuBlBa,YAAAA,iBAvBkB,GAuBET,QAAQ,CAACM,OAAT,CAAiBC,GAAjB,CAAqB,qBAArB,CAvBF;AAwBlBG,YAAAA,aAxBkB,GAwBFD,iBAxBE,oBAwBFA,iBAAiB,CAAEE,KAAnB,CAAyB,kBAAzB,CAxBE;AA0BlB/B,YAAAA,QA1BkB,sBA2BtB8B,aA3BsB,oBA2BtBA,aAAa,CAAG,CAAH,CA3BS,8CA2BclF,IAAI,CAACC,GAAL,EA3Bd,SA2B4B+E,aA3B5B;AA6BlBI,YAAAA,OA7BkB,GA6BRC,GAAG,CAACC,eAAJ,CAAoBV,QAApB,CA7BQ;AA8BlBW,YAAAA,IA9BkB,GA8BXC,MAAM,CAACC,MAAP,CAAcC,QAAQ,CAACC,aAAT,CAAuB,GAAvB,CAAd,EAA2C;AACtDC,cAAAA,IAAI,EAAER,OADgD;AAEtDS,cAAAA,QAAQ,EAAEzC,QAF4C;AAGtD0C,cAAAA,KAAK,EAAE;AAH+C,aAA3C,CA9BW;AAoCxBJ,YAAAA,QAAQ,CAACK,IAAT,CAAcC,MAAd,CAAqBT,IAArB;AACAA,YAAAA,IAAI,CAACU,KAAL;AACAV,YAAAA,IAAI,CAACW,MAAL;AACAb,YAAAA,GAAG,CAACc,eAAJ,CAAoBf,OAApB;AAvCwB;AAAA;;AAAA;AAAA;AAAA;AAAA,kBAyClB,uBAAiBf,KAAjB,iBAAiC,IAAIA,KAAJ,CAAU,iBAAV,CAzCf;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAAH;;AAAA,kBAAZH,YAAY;AAAA;AAAA;AAAA,GAAlB;AAkDP;;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAakC,WAAW,GAAG,SAAdA,WAAc,CAACjC,GAAD;AACzB,MAAMkC,WAAW,GAAa;AAC5BhD,IAAAA,IAAI,EAAE,cADsB;AAE5Be,IAAAA,IAAI,EAAE,EAFsB;AAG5BkC,IAAAA,IAAI,EAAE;AAHsB,GAA9B;;AAMA,MAAI,CAACnC,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;AACnC,WAAOkC,WAAP;AACD;;AAED,MAAI;AACF;AACA,QAAME,QAAQ,GAAGpC,GAAG,CACjBJ,KADc,CACR,MADQ,EACA,CADA;AAAA,KAEdnD,OAFc,CAEN,KAFM,EAEC,GAFD,CAAjB,CAFE;;AAMF,QAAM4F,eAAe,GAAGD,QAAQ,CAACxC,KAAT,CAAe,GAAf,EAAoBC,GAApB,EAAxB;;AACA,QAAI,CAACwC,eAAL,EAAsB;AACpB,aAAOH,WAAP;AACD;;AAED,QAAMI,QAAQ,GAAGC,kBAAkB,CAACF,eAAD,CAAnC,CAXE;;AAcF,QAAIC,QAAQ,CAACE,UAAT,CAAoB,GAApB,KAA4B,CAACF,QAAQ,CAACG,QAAT,CAAkB,GAAlB,EAAuB,CAAvB,CAAjC,EAA4D;AAC1D,aAAO;AAAEvD,QAAAA,IAAI,EAAEoD,QAAR;AAAkBrC,QAAAA,IAAI,EAAE,EAAxB;AAA4BkC,QAAAA,IAAI,EAAEG;AAAlC,OAAP;AACD,KAhBC;AAmBF;;;AACA,QAAMI,YAAY,GAAGJ,QAAQ,CAAClD,WAAT,CAAqB,GAArB,CAArB;;AACA,QAAIsD,YAAY,KAAK,CAAC,CAAlB,IAAuBA,YAAY,KAAK,CAA5C,EAA+C;AAC7C,aAAO;AAAExD,QAAAA,IAAI,EAAEoD,QAAR;AAAkBrC,QAAAA,IAAI,EAAE,EAAxB;AAA4BkC,QAAAA,IAAI,EAAEG;AAAlC,OAAP;AACD;;AAED,QAAMH,IAAI,GAAGG,QAAQ,CAAC1F,KAAT,CAAe,CAAf,EAAkB8F,YAAlB,CAAb;AACA,QAAMzC,IAAI,GAAGqC,QAAQ,CAAC1F,KAAT,CAAe8F,YAAY,GAAG,CAA9B,EAAiCC,WAAjC,EAAb;AAEA,WAAO;AAAEzD,MAAAA,IAAI,EAAEoD,QAAR;AAAkBrC,MAAAA,IAAI,EAAJA,IAAlB;AAAwBkC,MAAAA,IAAI,EAAJA;AAAxB,KAAP;AACD,GA7BD,CA6BE,OAAOS,KAAP,EAAc;AACdC,IAAAA,OAAO,CAACD,KAAR,CAAc,6BAAd,EAA6CA,KAA7C;AACA,WAAOV,WAAP;AACD;AACF,CA5CM;AA8CP;;;;;;;;;;;AAUA,IAAaY,YAAY,GAAG,SAAfA,YAAe,CAC1BC,GAD0B;AAG1B,MAAMC,CAAC,GAAGvE,MAAM,CAACsE,GAAD,CAAN,IAAe,CAAzB;AACA,SAAO,IAAIE,IAAI,CAACC,YAAT,CAAsB,IAAtB,EAA4B;AACjCC,IAAAA,QAAQ,EAAE,SADuB;AAEjCC,IAAAA,qBAAqB,EAAE;AAFU,GAA5B,EAGwBC,MAHxB,CAG+BL,CAH/B,CAAP;AAID,CARM;;AC/UP;;;;AAIA;;;;;;AAMA,IAAaM,KAAK,GAAG,SAARA,KAAQ,CAACC,KAAD;AACnB,MAAI,CAACA,KAAL,EAAY,OAAO,IAAP;AACZ,MAAIA,KAAK,YAAYrC,GAArB,EAA0B,OAAOqC,KAAP;;AAE1B,MACE,OAAOA,KAAP,KAAiB,QAAjB,IACA,CAACA,KAAK,CAACd,QAAN,CAAe,KAAf,CADD,IAEA,CAACc,KAAK,CAACf,UAAN,CAAiB,GAAjB,CAHH,EAIE;AACA,WAAO,IAAItB,GAAJ,cAAmBqC,KAAnB,CAAP;AACD;;AAED,MAAI,OAAOA,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,CAACf,UAAN,CAAiB,GAAjB,CAAjC,EAAwD;AACtD,WAAO,IAAItB,GAAJ,CAAQqC,KAAR,EAAeC,MAAM,CAACC,QAAP,CAAgBC,MAA/B,CAAP;AACD;;AAED,SAAO,IAAIxC,GAAJ,CAAQqC,KAAR,CAAP;AACD,CAjBM;AAmBP;;;;;;;;;AAQA,IAAaI,UAAU,GAAG,SAAbA,UAAa,CACxBC,IADwB,EAExBC,IAFwB;AAIxB,MAAI;AACF,QAAMC,OAAO,GAAGR,KAAK,CAACM,IAAD,CAArB;AACA,QAAMG,OAAO,GAAGT,KAAK,CAACO,IAAD,CAArB;AAEA,QAAI,CAACC,OAAD,IAAY,CAACC,OAAjB,EAA0B,OAAO,KAAP;AAE1B,WAAOD,OAAO,CAACE,QAAR,KAAqBD,OAAO,CAACC,QAApC;AACD,GAPD,CAOE,OAAOpB,KAAP,EAAc;AACd,WAAO,KAAP;AACD;AACF,CAdM;AAgBP;;;;;;;;AAOA,IAAaqB,aAAa,GAAG,SAAhBA,aAAgB,CAACC,MAAD;AAC3B,MAAIA,MAAM,KAAK,EAAf,EAAmB,OAAO,IAAP;AAEnB,MAAMC,WAAW,GAAG,m4sBAApB;AAEA,SAAOA,WAAW,CAACC,IAAZ,CAAiBF,MAAjB,KAA4BA,MAAM,CAAC9F,MAAP,IAAiB,GAApD;AACD,CANM;;ACeP,IAAYiG,WAAZ;;AAAA,WAAYA;AACVA,EAAAA,wCAAA,aAAA;AACAA,EAAAA,wCAAA,aAAA;AACAA,EAAAA,wCAAA,aAAA;AACAA,EAAAA,wCAAA,aAAA;AACD,CALD,EAAYA,WAAW,KAAXA,WAAW,KAAA,CAAvB;;SCpEgBC,cACdC,WACAC,qBACAC,YACAC;;;AAEA,MAAIC,EAAE,GAAG,EAAT;AACA,MAAIC,EAAE,GAAG,EAAT;AACA,MAAIC,GAAG,GAAG,EAAV;;AAGA,MAAI,CAACN,SAAL,EAAgB;AACd,WAAO;AAAEI,MAAAA,EAAE,EAAFA,EAAF;AAAMC,MAAAA,EAAE,EAAFA,EAAN;AAAUC,MAAAA,GAAG,EAAHA;AAAV,KAAP;AACD;;;AAGD,MAAsBC,WAAtB,GAAsCP,SAAtC,CAAQQ,YAAR;AAEA,MAAMC,UAAU,GAAGF,WAAW,KAAKT,WAAW,CAACY,QAA/C;AAEA,MAAIC,eAAe,GAAG,EAAtB;;AAOA,MAAIF,UAAJ,EAAgB;AACd,QACsBG,iBADtB,GAEIZ,SAFJ,CACEa,kBADF;AAGA,QAAMC,KAAK,GAAGF,iBAAiB,CAACE,KAAhC;AACAH,IAAAA,eAAe,GAAG;AAChBN,MAAAA,EAAE,EAAE,CAAAS,KAAK,QAAL,YAAAA,KAAK,CAAET,EAAP,KAAa,EADD;AAEhBC,MAAAA,GAAG,EAAE,CAAAQ,KAAK,QAAL,YAAAA,KAAK,CAAER,GAAP,KAAc,EAFH;AAGhBS,MAAAA,IAAI,EAAE,CAAAD,KAAK,QAAL,YAAAA,KAAK,CAAEC,IAAP,KAAe,EAHL;AAIhBX,MAAAA,EAAE,EAAE;AAJY,KAAlB;AAMD,GAXD,MAWO;AACL,QACsBQ,kBADtB,GAEIZ,SAFJ,CACEa,kBADF;;AAIA,eAIID,kBAJJ,WAIIA,kBAJJ,GAIyB,EAJzB;AAAA,8BACEI,SADF;AAAA,QACaC,QADb,+BACwB,EADxB;AAAA,+BAEEC,UAFF;AAAA,QAEcC,SAFd,gCAE0B,EAF1B;AAAA,8BAGEC,SAHF;AAAA,QAGaC,QAHb,+BAGwB,EAHxB;;AAMAV,IAAAA,eAAe,GAAG;AAChBN,MAAAA,EAAE,EAAEY,QADY;AAEhBX,MAAAA,GAAG,EAAEa,SAFW;AAGhBf,MAAAA,EAAE,EAAEiB,QAHY;AAIhBN,MAAAA,IAAI,EAAE;AAJU,KAAlB;AAMD;;AAED,MAAIO,sBAAsB,GAAG,KAA7B;;AAEAA,EAAAA,sBAAsB,GACpBb,UAAU,IAAI,0BAACE,eAAe,CAACI,IAAjB,oCAAyB,EAAzB,EAA6B7C,QAA7B,CAAsC+B,mBAAtC,CADhB;;AAGA,MAAIQ,UAAJ,EAAgB;AAAA;;AACd;AACA,WAAAL,EAAE,EAACmB,IAAH,sCAAYZ,eAAe,CAACI,IAA5B,qCAAoC,EAApC;AACD,GAHD,MAGO;AAAA;;AACL;AACA;AACA,YAAAX,EAAE,EAACmB,IAAH,oCAAYZ,eAAe,CAACP,EAA5B,kCAAkC,CAACH,mBAAD,CAAlC;AACD;AAGD;;;AACAI,EAAAA,EAAE,GAAGM,eAAe,CAACN,EAAhB,aAAyBM,eAAe,CAACN,EAAzC,IAA+C,EAApD;;AAEA,MAAImB,KAAK,CAACC,OAAN,CAAcd,eAAe,CAACP,EAA9B,KAAqCK,UAAzC,EAAqD;AAAA;;AACnD,WAAAJ,EAAE,EAACkB,IAAH,YAAWZ,eAAe,CAACP,EAA3B;AACD;AAGD;;;AACA,MAAI,CAACkB,sBAAD,IAA2Bb,UAA/B,EAA2C;AACzCJ,IAAAA,EAAE,CAACkB,IAAH,CAAQtB,mBAAR;AACD;;;AAGDK,EAAAA,GAAG,GAAG,CAACK,eAAe,CAACL,GAAhB,IAAuB,EAAxB,EAA4BoB,MAA5B,CACJ,UAAAC,YAAY;AAAA,WAAIA,YAAY,KAAK1B,mBAArB;AAAA,GADR,CAAN;AAKA;AACA;AACA;;AACA,MAAM2B,gBAAgB,GAAG,uGAAzB;AACAvB,EAAAA,EAAE,GAAGA,EAAE,CAACqB,MAAH,CAAU,UAAAZ,KAAK;AAClB,QAAIA,KAAK,KAAKb,mBAAV,IAAiCqB,sBAArC,EAA6D;AAC3D,aAAO,KAAP;AACD;;AACD,QAAIR,KAAK,KAAKZ,UAAV,IAAwBY,KAAK,KAAKX,cAAtC,EAAsD;AACpD,aAAO,KAAP;AACD;;AACD,QAAIyB,gBAAgB,CAAC/B,IAAjB,CAAsBiB,KAAtB,CAAJ,EAAkC;AAChC,aAAO,KAAP;AACD;;AACD,WAAO,IAAP;AACD,GAXI,CAAL;AAaAR,EAAAA,GAAG,GAAGA,GAAG,CAACoB,MAAJ,CAAW,UAAAZ,KAAK;AACpB,QACEA,KAAK,KAAKZ,UAAV,IACAY,KAAK,KAAKX,cADV,IAEAyB,gBAAgB,CAAC/B,IAAjB,CAAsBiB,KAAtB,CAHF,EAIE;AACA,aAAO,KAAP;AACD;;AAED,WAAO,IAAP;AACD,GAVK,CAAN;;AAaAV,EAAAA,EAAE,GAAGoB,KAAK,CAACT,IAAN,CAAW,IAAIc,GAAJ,CAAQzB,EAAR,CAAX,CAAL;AACAC,EAAAA,EAAE,GAAGmB,KAAK,CAACT,IAAN,CAAW,IAAIc,GAAJ,CAAQxB,EAAR,CAAX,CAAL;AACAC,EAAAA,GAAG,GAAGkB,KAAK,CAACT,IAAN,CAAW,IAAIc,GAAJ,CAAQvB,GAAR,CAAX,CAAN;AAEA,SAAO;AACLF,IAAAA,EAAE,EAAFA,EADK;AAELC,IAAAA,EAAE,EAAFA,EAFK;AAGLC,IAAAA,GAAG,EAAHA;AAHK,GAAP;AAKD;;ACzID;;;;;AAMA,SAAgBwB,aAAaC;AAC3B,MAAI;AACF;AACA,QAAMC,eAAe,GAAGC,MAAM,CAACF,SAAD,CAAN,CAAkB3D,WAAlB,EAAxB,CAFE;AAKF;;AACA,WAAO8D,OAAO,CAACC,IAAI,CAACC,KAAL,CAAWJ,eAAX,CAAD,CAAd;AACD,GAPD,CAOE,OAAO3D,KAAP,EAAc;AACd,WAAO,KAAP;AACD;AACF;;ACjBD;;;;;AAKA,SAAgBgE,QAAQC;AACtB;AACA,SAAOA,GAAG,CAACjK,KAAJ,GAAYkK,IAAZ,CAAiB,UAACC,CAAD,EAAIjK,CAAJ;AAAA,WAAUiK,CAAC,GAAGjK,CAAd;AAAA,GAAjB,CAAP;AACD;AAED;;;;;;;AAMA,SAAgBkK,SAASH,KAAeI;AACtC,MAAMC,MAAM,GAAGN,OAAO,CAACC,GAAD,CAAtB;;AACA,SAAOM,kBAAkB,CAACD,MAAD,EAASD,CAAT,CAAzB;AACD;AAED;;;;;;;;AAOA,SAAgBG,MAAMC,KAAaC,KAAaC;AAC9C,MAAIA,KAAK,GAAGF,GAAZ,EAAiB;AACf,WAAOA,GAAP;AACD;;AACD,MAAIE,KAAK,GAAGD,GAAZ,EAAiB;AACf,WAAOA,GAAP;AACD;;AACD,SAAOC,KAAP;AACD;AAED;;;;;;;;;AAQA,SAASJ,kBAAT,CAA4BD,MAA5B,EAA8CD,CAA9C;AACE,MAAMO,OAAO,GAAGJ,KAAK,CAAC,CAAD,EAAI,CAAJ,EAAOH,CAAP,CAArB;;AACA,MAAMQ,GAAG,GAAG,CAACP,MAAM,CAAC9I,MAAP,GAAgB,CAAjB,IAAsBoJ,OAAlC;;AACA,MAAMrF,IAAI,GAAGzE,IAAI,CAACC,KAAL,CAAW8J,GAAX,CAAb;;AACA,MAAMC,IAAI,GAAGD,GAAG,GAAGtF,IAAnB;AAEA;AACA;AACA;;AACA,MAAI+E,MAAM,CAAC/E,IAAI,GAAG,CAAR,CAAN,KAAqB/F,SAAzB,EAAoC;AAClC;AACA,WAAO8K,MAAM,CAAC/E,IAAD,CAAN,GAAeuF,IAAI,IAAIR,MAAM,CAAC/E,IAAI,GAAG,CAAR,CAAN,GAAmB+E,MAAM,CAAC/E,IAAD,CAA7B,CAA1B;AACD;;;AAGD,SAAO+E,MAAM,CAAC/E,IAAD,CAAb;AACD;AAED;;;;;;;;AAMA,IAAawF,oBAAoB,GAAG,SAAvBA,oBAAuB,CAACC,IAAD,EAAiBC,SAAjB;AAClC;AACA;AACA;AACA,MAAMX,MAAM,GAAGN,OAAO,CAACgB,IAAD,CAAtB;AAEA,SAAOC,SAAS,CAACC,GAAV,CAAc,UAAAC,QAAQ;AAC3B,WAAOZ,kBAAkB,CAACD,MAAD,EAASa,QAAT,CAAzB;AACD,GAFM,CAAP;AAGD,CATM;AAWP;;;;;;;;;AAQA,IAAaC,qBAAqB,GAAG,SAAxBA,qBAAwB,CACnCC,MADmC,EAEnCC,MAFmC,EAGnCC,IAHmC;AAKnC,MAAMC,OAAO,GAAGD,IAAI,CAACE,IAAL,GAAYF,IAAI,CAACG,KAAL,GAAa,CAAzC;AACA,MAAMC,OAAO,GAAGJ,IAAI,CAACK,GAAL,GAAWL,IAAI,CAACM,MAAL,GAAc,CAAzC;AAEA,SAAO;AACLC,IAAAA,SAAS,EAAET,MAAM,GAAGG,OADf;AAELO,IAAAA,SAAS,EAAET,MAAM,GAAGK;AAFf,GAAP;AAID,CAZM;AAcP;;;;;;;;;;;;;;;AAcA,IAAaK,sBAAsB,GAAG,SAAzBA,sBAAyB,CACpCF,SADoC,EAEpCC,SAFoC,EAGpCE,KAHoC;AAKpC,MAAMC,OAAO,GAAID,KAAK,GAAGnL,IAAI,CAACqL,EAAd,GAAoB,GAApC;AACA,MAAMC,GAAG,GAAGtL,IAAI,CAACsL,GAAL,CAAS,CAACF,OAAV,CAAZ;AACA,MAAMG,GAAG,GAAGvL,IAAI,CAACuL,GAAL,CAAS,CAACH,OAAV,CAAZ;AAEA,SAAO;AACLI,IAAAA,QAAQ,EAAER,SAAS,GAAGM,GAAZ,GAAkBL,SAAS,GAAGM,GADnC;AAELE,IAAAA,QAAQ,EAAET,SAAS,GAAGO,GAAZ,GAAkBN,SAAS,GAAGK;AAFnC,GAAP;AAID,CAbM;AAeP;;;;;;;;;;;AAUA,IAAaI,qBAAqB,GAAG,SAAxBA,qBAAwB,CACnCF,QADmC,EAEnCC,QAFmC,EAGnCb,KAHmC,EAInCG,MAJmC;AAMnC;AACA;AACA;AACA;AACA,SAAO;AACLY,IAAAA,CAAC,EAAE3L,IAAI,CAAC4J,GAAL,CAAS,CAAT,EAAY5J,IAAI,CAAC2J,GAAL,CAAS,GAAT,EAAc,KAAM6B,QAAQ,IAAIZ,KAAK,GAAG,CAAZ,CAAT,GAA2B,EAA9C,CAAZ,CADE;AAELgB,IAAAA,CAAC,EAAE5L,IAAI,CAAC4J,GAAL,CAAS,CAAT,EAAY5J,IAAI,CAAC2J,GAAL,CAAS,GAAT,EAAc,KAAM8B,QAAQ,IAAIV,MAAM,GAAG,CAAb,CAAT,GAA4B,EAA/C,CAAZ;AAFE,GAAP;AAID,CAdM;;ACtIP,IAAMc,uBAAuB,GAAG,YAAhC;;AAEA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,GAAD;AAAA,SAAiBA,GAAG,CAAChN,OAAJ,CAAY,mBAAZ,EAAiC,EAAjC,CAAjB;AAAA,CAAvB;;AAEA,AAAO,IAAMiN,cAAc,GAAG,SAAjBA,cAAiB,CAACxK,IAAD;AAC5B,MAAI,CAACA,IAAL,EAAW,OAAO,EAAP;;AAEX,SAAOA,IAAI,CACRU,KADI,CACE,GADF;AAAA,GAEJkI,GAFI,CAEA,UAAA6B,IAAI;AACP,QAAI,CAACA,IAAL,EAAW,OAAO,EAAP;AAEX;AACA;;AACA,WAAOA,IAAI,CAACC,MAAL,CAAY,CAAZ,EAAeC,WAAf,KAA+BF,IAAI,CAAC/M,KAAL,CAAW,CAAX,CAAtC;AACD,GARI,EASJkD,IATI,CASC,GATD,CAAP;AAUD,CAbM;AAeP,AAAO,IAAMgK,YAAY,GAAG,SAAfA,YAAe;MAAGC,YAAAA;AAC7B,MAAMtK,SAAS,GAAGsK,IAAI,QAAJ,IAAAA,IAAI,CAAE7K,IAAN,GAAa6K,IAAI,CAAC7K,IAAL,CAAUU,KAAV,CAAgB,GAAhB,EAAqBoK,KAArB,EAAb,GAA4C,EAA9D;AACA,SAAON,cAAc,CAACjK,SAAD,CAArB;AACD,CAHM;AAKP,AAAO,IAAMwK,WAAW,GAAG,SAAdA,WAAc;MAAGF,aAAAA;;AAC5B,MAAIA,IAAI,IAAIA,IAAI,CAAC7K,IAAjB,EAAuB;AACrB,QAAMQ,QAAQ,GACZqK,IAAI,CAAC7K,IAAL,CAAUU,KAAV,CAAgB,GAAhB,EAAqBxB,MAArB,GAA8B,CAA9B,GAAkC2L,IAAI,CAAC7K,IAAL,CAAUU,KAAV,CAAgB,GAAhB,EAAqBC,GAArB,EAAlC,GAA+D,EADjE;AAEA,WAAO6J,cAAc,CAAChK,QAAD,CAArB;AACD;;AACD,SAAO,EAAP;AACD,CAPM;AASP,IAAawK,mBAAmB,GAAG,SAAtBA,mBAAsB;;;MACjCC,qBAAAA;MACAC,gBAAAA;MACAC,cAAAA;AAMA,2BAIIF,YAJJ,CACEG,IADF;AAAA,MACUC,QADV,sBACUA,QADV;AAAA,MACoBC,MADpB,sBACoBA,MADpB;AAAA,MAEEC,EAFF,GAIIN,YAJJ,CAEEM,EAFF;AAAA,8BAIIN,YAJJ,CAGEO,iBAHF;AAAA,MAGqBC,4BAHrB,sCAGoD,EAHpD;;AAKA,cAAuDP,OAAO,IAAI,EAAlE;AAAA,MAA2BQ,uBAA3B,SAAQF,iBAAR;;AAEA,MAAMG,iBAAiB,GAAG;AACxB,oBAAgBnB,cAAc,CAAC,CAAAc,MAAM,QAAN,YAAAA,MAAM,CAAEtL,IAAR,KAAgB,EAAjB,CADN;AAExB,0BAAsB4K,YAAY,CAAC;AAAEC,MAAAA,IAAI,EAAES;AAAR,KAAD,CAFV;AAGxB,yBAAqBP,WAAW,CAAC;AAAEF,MAAAA,IAAI,EAAES;AAAR,KAAD,CAHR;AAIxB,qBAAiBA,MAAjB,oBAAiBA,MAAM,CAAEnF,KAJD;AAKxB,qBAAiBmF,MAAjB,oBAAiBA,MAAM,CAAEM,YALD;AAMxB,kBAAcN,MAAd,oBAAcA,MAAM,CAAEC,EANE;AAOxB,uBAAmBA,EAPK;AAQxB,gBAAYJ,KAAZ,oBAAYA,KAAK,CAAEI,EARK;AASxB,kBAAcJ,KAAd,oBAAcA,KAAK,CAAEnL,IATG;AAUxB,kBAAcwK,cAAc,CAAC,CAAAa,QAAQ,QAAR,YAAAA,QAAQ,CAAErL,IAAV,KAAkB,EAAnB,CAVJ;AAWxB,wBAAoB4K,YAAY,CAAC;AAAEC,MAAAA,IAAI,EAAEQ;AAAR,KAAD,CAXR;AAYxB,uBAAmBN,WAAW,CAAC;AAAEF,MAAAA,IAAI,EAAEQ;AAAR,KAAD,CAZN;AAaxB,sCAAeA,QAAf,oBAAeA,QAAQ,CAAElF,KAAzB,8BAAkC;AAbV,GAA1B;AAeA,MAAM0F,oCAAoC,GAAG1J,MAAM,CAAC2J,OAAP,CAC3CL,4BAD2C,WAC3CA,4BAD2C,GACX,EADW,EAE3CM,MAF2C,CAEpC,UAACC,GAAD;QAAyBC;QAAK5D;AACrC2D,IAAAA,GAAG,oCAAkCC,GAAlC,CAAH,GAA8C5D,KAA9C;AACA,WAAO2D,GAAP;AACD,GAL4C,EAK1C,EAL0C,CAA7C;AAOA,MAAME,+BAA+B,GAAG/J,MAAM,CAAC2J,OAAP,CACtCJ,uBADsC,WACtCA,uBADsC,GACX,EADW,EAEtCK,MAFsC,CAE/B,UAACC,GAAD;QAAyBC;QAAK5D;AACrC2D,IAAAA,GAAG,+BAA6BC,GAA7B,CAAH,GAAyC5D,KAAzC;AACA,WAAO2D,GAAP;AACD,GALuC,EAKrC,EALqC,CAAxC;;AAOA,MAAMG,SAAS,gBACVR,iBADU,EAEVE,oCAFU,EAGVK,+BAHU,CAAf;;AAMA,SAAOC,SAAP;AACD,CApDM;AAsDP,IAAaC,yBAAyB,GAAG,SAA5BA,yBAA4B;MACvCC,gBAAAA;MACAF,kBAAAA;AAKA;AACA,SAAOE,OAAP,oBAAOA,OAAO,CAAE9O,OAAT,CAAiB8M,uBAAjB,EAA0C,UAACiC,CAAD,EAAI/O,OAAJ;AAC/C,WAAO4O,SAAS,CAAC5O,OAAO,CAAC+C,IAAR,EAAD,CAAT,GACH6L,SAAS,CAAC5O,OAAO,CAAC+C,IAAR,GAAemD,WAAf,EAAD,CADN,GAEH,EAFJ;AAGD,GAJM,CAAP;AAKD,CAbM;AAeP,IAAa8I,8BAA8B,GAAG,SAAjCA,8BAAiC;MAC5CF,gBAAAA;MACAF,kBAAAA;AAKA,MAAMK,wBAAwB,GAAGlC,cAAc,CAAC+B,OAAD,CAA/C;AACA,MAAMI,OAAO,GAAGD,wBAAwB,CAAC1K,KAAzB,CAA+BuI,uBAA/B,CAAhB;AACA,MAAI,CAACoC,OAAL,EAAc,OAAO,EAAP;AAEd,SAAOA,OAAO,CACX7D,GADI,CACA,UAAA9G,KAAK;AACR,WAAOA,KAAK,CACTvE,OADI,CACI,IADJ,EACU,EADV,EAEJA,OAFI,CAEI,IAFJ,EAEU,EAFV,EAGJ+C,IAHI,EAAP;AAID,GANI,EAOJyG,MAPI,CAOG,UAAA2F,QAAQ;AACd,WAAOP,SAAS,CAACO,QAAD,CAAT,KAAwBxP,SAA/B;AACD,GATI,CAAP;AAUD,CArBM;;AC9GP;;;;;;;AAWA,IAAayP,qBAAqB,GAAG,SAAxBA,qBAAwB,CACnCC,aADmC,EAEnCC,YAFmC,EAGnCC,QAHmC;AAKnC,MAAIC,KAAK,GAAmB,IAA5B;;AAEA,MAAMC,KAAK,GAAG,SAARA,KAAQ;AACZ,QAAI,CAACD,KAAL,EAAY;AACVH,MAAAA,aAAa;AACd;;AACDK,IAAAA,KAAK;AACN,GALD;;AAOA,MAAMC,IAAI,GAAG,SAAPA,IAAO;AACX,QAAIH,KAAJ,EAAW;AACThQ,MAAAA,YAAY,CAACgQ,KAAD,CAAZ;AACAA,MAAAA,KAAK,GAAG,IAAR;AACAF,MAAAA,YAAY;AACb;AACF,GAND;;AAQA,MAAMI,KAAK,GAAG,SAARA,KAAQ;AACZ,QAAIF,KAAJ,EAAW;AACThQ,MAAAA,YAAY,CAACgQ,KAAD,CAAZ;AACD;;AACDA,IAAAA,KAAK,GAAG5P,UAAU,CAAC;AACjB+P,MAAAA,IAAI;AACL,KAFiB,EAEfJ,QAFe,CAAlB;AAGD,GAPD;;AASA,SAAO;AAAEE,IAAAA,KAAK,EAALA,KAAF;AAASE,IAAAA,IAAI,EAAJA;AAAT,GAAP;AACD,CAhCM;;ACRP;;;;;;AAMA,IAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,CACzBC,UADyB,EAEzBC,SAFyB;AAIzB;AACA,MAAIA,SAAS,KAAK,IAAlB,EAAwB,OAAO,IAAP;AACxB,MAAMC,WAAW,GAAG9O,IAAI,CAACC,KAAL,CAAW9B,IAAI,CAACC,GAAL,KAAa,IAAxB,CAApB;AACA,SAAOwQ,UAAU,GAAGC,SAAb,GAAyBC,WAAhC;AACD,CARD;AAUA;;;;;;;AAKA,IAAMC,uBAAuB,GAAG,SAA1BA,uBAA0B,CAACC,WAAD;AAC9B;AACAA,EAAAA,WAAW,CAAC5F,IAAZ,CACE,UAAC6F,IAAD,EAAOC,IAAP;AAAA,WAAgBlP,IAAI,CAACmP,GAAL,CAASF,IAAI,CAACJ,SAAd,IAA2B7O,IAAI,CAACmP,GAAL,CAASD,IAAI,CAACL,SAAd,CAA3C;AAAA,GADF;AAGA,SAAOG,WAAW,CAAC,CAAD,CAAlB;AACD,CAND;AAQA;;;;;;;AAKA,IAAMI,aAAa,GAAG,SAAhBA,aAAgB,CAAClP,OAAD;AACpB,MAAMmP,KAAK,GAA8B;AACvCzD,IAAAA,CAAC,EAAE,QADoC;AAEvC0D,IAAAA,EAAE,EAAE,OAFmC;AAGvCC,IAAAA,CAAC,EAAE,KAHoC;AAIvCC,IAAAA,CAAC,EAAE,IAJoC;AAKvCC,IAAAA,CAAC,EAAE;AALoC,GAAzC;;AAQA,MAAIvP,OAAO,GAAG,EAAd,EAAkB;AAChB,WAAO,IAAP;AACD;;;AAGD,MAAMwP,KAAK,GAAa,EAAxB;AAEA/L,EAAAA,MAAM,CAACgM,IAAP,CAAYN,KAAZ,EAAmBO,OAAnB,CAA2B,UAAA9O,IAAI;AAC7B,QAAM+I,KAAK,GAAG7J,IAAI,CAACC,KAAL,CAAWC,OAAO,GAAGmP,KAAK,CAACvO,IAAD,CAA1B,CAAd;AACA,QAAIZ,OAAO,GAAG,EAAV,IAAgBwP,KAAK,CAAChP,MAAN,GAAe,CAAnC,EAAsC;AACtC,QAAIgP,KAAK,CAAChP,MAAN,KAAiB,CAArB,EAAwB;;AACxB,QAAImJ,KAAK,GAAG,CAAZ,EAAe;AACb6F,MAAAA,KAAK,CAACtH,IAAN,CAAWyB,KAAK,GAAG/I,IAAnB;AACAZ,MAAAA,OAAO,IAAI2J,KAAK,GAAGwF,KAAK,CAACvO,IAAD,CAAxB;AACD;AACF,GARD;AASA,SAAO4O,KAAK,CAACtN,IAAN,CAAW,GAAX,CAAP;AACD,CA1BD;AA4BA;;;;;;;;;AAOA,IAAMyN,eAAe,GAAG,SAAlBA,eAAkB,CACtBtN,IADsB,EAEtBuN,UAFsB,EAGtBC,IAHsB;AAKtB,MACqCC,YADrC,GAKIF,UALJ,CACEG,iCADF;AAAA,MAEoCC,YAFpC,GAKIJ,UALJ,CAEEK,gCAFF;AAAA,MAGiCC,WAHjC,GAKIN,UALJ,CAGEO,6BAHF;AAAA,MAIcC,SAJd,GAKIR,UALJ,CAIES,UAJF;AAOA,MAC0BC,mBAD1B,GAIIT,IAJJ,CACEU,sBADF;AAAA,MAEiBC,YAFjB,GAIIX,IAJJ,CAEEY,aAFF;AAAA,MAGE9N,MAHF,GAIIkN,IAJJ,CAGElN,MAHF;AAMA,MAAM+N,QAAQ,GAEV;AACFC,IAAAA,GAAG,EAAE;AACHhC,MAAAA,SAAS,EAAEF,kBAAkB,CAAC2B,SAAD,EAAYN,YAAZ,CAD1B;AAEH;AACAc,MAAAA,SAAS,EACPd,YAAY,KAAK,IAAjB,KACC,CAACQ,mBAAD,IAAwBA,mBAAmB,KAAK,CADjD;AAJC,KADH;AAQFO,IAAAA,GAAG,EAAE;AACHlC,MAAAA,SAAS,EAAEF,kBAAkB,CAAC+B,YAAD,EAAeR,YAAf,CAD1B;AAEH;AACAY,MAAAA,SAAS,EACPZ,YAAY,KAAK,IAAjB,IAAyB,CAAC,CAACM,mBAA3B,IAAkD,CAAC,CAACE;AAJnD,KARH;AAcFM,IAAAA,EAAE,EAAE;AACFnC,MAAAA,SAAS,EAAEF,kBAAkB,CAAC2B,SAAD,EAAYF,WAAZ,CAD3B;AAEF;AACAU,MAAAA,SAAS,EAAEjO,MAAM,KAAK,MAAX,IAAqBuN,WAAW,KAAK;AAH9C;AAdF,GAFJ;AAuBA,MAAMa,SAAS,GAAGL,QAAQ,CAACrO,IAAD,CAA1B;AACA,SAAO0O,SAAS,gBAAQA,SAAR;AAAmB1O,IAAAA,IAAI,EAAJA;AAAnB,OAA4B,IAA5C;AACD,CA3CD;AA6CA;;;;;;;;AAMA,IAAM2O,qBAAqB,GAAG,SAAxBA,qBAAwB,CAC5BpB,UAD4B,EAE5BC,IAF4B;AAS5B;AACA,MAAMa,QAAQ,GAAG,CAAC,KAAD,EAAQ,KAAR,EAAe,IAAf,CAAjB;AACA,SAAOA,QAAQ,CAACxG,GAAT,CAAa,UAAA7H,IAAI;AAAA,WAAIsN,eAAe,CAACtN,IAAD,EAAOuN,UAAP,EAAmBC,IAAnB,CAAnB;AAAA,GAAjB,EACJxH,MADI,CAEH,UACE0I,SADF;AAAA,WAGE,CAAC,CAACA,SAAF,IAAeA,SAAS,CAACH,SAH3B;AAAA,GAFG,EAOJ1G,GAPI,CAOA,UAAA6G,SAAS;AAAA,wBACTA,SADS;AAEZE,MAAAA,IAAI,EAAEF,SAAS,CAACpC,SAAV,IAAuB,CAAvB,GAA2B,OAA3B,GAAqC,OAF/B;AAGZuC,MAAAA,WAAW,EAAEH,SAAS,CAACpC,SAAV,IAAuB;AAHxB;AAAA,GAPT,CAAP;AAYD,CAvBD;AAyBA;;;;;;;;;AAOA,IAAawC,iBAAiB,GAAG,SAApBA,iBAAoB;MAC/BvB,kBAAAA;MACAC,YAAAA;AAKA,MAAI,CAACD,UAAD,IAAe,CAACC,IAApB,EACE,OAAO;AAAExN,IAAAA,IAAI,EAAE,EAAR;AAAYsM,IAAAA,SAAS,EAAE,EAAvB;AAA2BsC,IAAAA,IAAI,EAAE,EAAjC;AAAqCC,IAAAA,WAAW,EAAE;AAAlD,GAAP;;AAGF,MAAMpC,WAAW,GAAGkC,qBAAqB,CAACpB,UAAD,EAAaC,IAAb,CAAzC;;AAGA,MAAMuB,UAAU,GAAGvC,uBAAuB,CAACC,WAAD,CAA1C;AACA,SAAOsC,UAAU,GACb;AACE/O,IAAAA,IAAI,EAAE+O,UAAF,oBAAEA,UAAU,CAAE/O,IADpB;AAEEsM,IAAAA,SAAS,EAAEO,aAAa,CACtBkC,UAAU,CAACzC,SAAX,IAAwB,CAAxB,GACI,CAACyC,UAAU,CAACzC,SADhB,GAEIyC,UAAU,CAACzC,SAHO,CAF1B;AAOEsC,IAAAA,IAAI,EAAEG,UAAU,CAACH,IAPnB;AAQEC,IAAAA,WAAW,EAAEE,UAAU,CAACF;AAR1B,GADa,GAWb;AAAE7O,IAAAA,IAAI,EAAE,EAAR;AAAYsM,IAAAA,SAAS,EAAE,EAAvB;AAA2BsC,IAAAA,IAAI,EAAE,EAAjC;AAAqCC,IAAAA,WAAW,EAAE;AAAlD,GAXJ;AAYD,CA3BM;;AC3JP;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,IAAaG,YAAY,GAAG,SAAfA,YAAe,CAC1BC,SAD0B;AAG1B,MAAIA,SAAS,IAAI,IAAjB,EAAuB,OAAO,IAAP;AAEvB,MAAIC,SAAS,GAAG,OAAOD,SAAP,KAAqB,QAArB,GAAgCA,SAAhC,GAA4C,IAA5D;;AAGA,MACEC,SAAS,KAAK,IAAd,IACA,OAAOD,SAAP,KAAqB,QADrB,IAEA,QAAQ9K,IAAR,CAAa8K,SAAb,CAHF,EAIE;AACAC,IAAAA,SAAS,GAAG1Q,MAAM,CAACyQ,SAAD,CAAlB;AACD;;;AAGD,MAAIC,SAAS,KAAK,IAAlB,EAAwB;AACtB;AACA,QAAMC,WAAW,GACfD,SAAS,CAACE,QAAV,GAAqBjR,MAArB,KAAgC,EAAhC,GAAqC+Q,SAAS,GAAG,IAAjD,GAAwDA,SAD1D;AAEA,WAAO,IAAItT,IAAJ,CAASuT,WAAT,CAAP;AACD;;;AAGD,MAAI,OAAOF,SAAP,KAAqB,QAAzB,EAAmC;AACjC,QAAMI,OAAO,GAAG,IAAIzT,IAAJ,CAASqT,SAAT,CAAhB,CADiC;;AAIjC,QAAIzQ,MAAM,CAAC8Q,KAAP,CAAaD,OAAO,CAACE,OAAR,EAAb,CAAJ,EAAqC,OAAO,IAAP,CAJJ;AAOjC;;AACA,QAAMC,gBAAgB,GACpB,wBAAwBrL,IAAxB,CAA6B8K,SAA7B,KAA2C,cAAc9K,IAAd,CAAmB8K,SAAnB,CAD7C;;AAEA,QAAI,CAACO,gBAAL,EAAuB;AACrBH,MAAAA,OAAO,CAACI,QAAR,CAAiB,CAAjB,EAAoB,CAApB,EAAuB,CAAvB,EAA0B,CAA1B;AACD;;AAED,WAAOJ,OAAP;AACD;;AAED,SAAO,IAAP;AACD,CA3CM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}