{"name": "@chatwoot/utils", "version": "0.0.49", "description": "Chatwoot utils", "private": false, "license": "MIT", "main": "dist/index.js", "typings": "dist/index.d.ts", "files": ["dist", "src"], "engines": {"node": ">=10"}, "scripts": {"start": "tsdx watch", "build": "tsdx build", "test": "tsdx test", "lint": "tsdx lint", "prepare": "tsdx build", "link": "npm link", "size": "size-limit", "analyze": "size-limit --why", "docs": "typedoc --theme default --out docs ./src"}, "prettier": {"printWidth": 80, "semi": true, "singleQuote": true, "trailingComma": "es5"}, "module": "dist/utils.esm.js", "size-limit": [{"path": "dist/utils.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/utils.esm.js", "limit": "10 KB"}], "devDependencies": {"@size-limit/preset-small-lib": "^7.0.8", "@types/jest": "^29.5.14", "husky": "^8.0.1", "size-limit": "^8.0.0", "tsdx": "^0.14.1", "tslib": "^2.4.0", "typedoc": "^0.23.10", "typescript": "^4.7"}, "bugs": {"url": "https://github.com/chatwoot/utils/issues"}, "homepage": "https://github.com/chatwoot/utils#readme", "dependencies": {"date-fns": "^2.29.1"}}