/*!
  * vue-i18n v9.14.5
  * (c) 2025 ka<PERSON><PERSON> kawa<PERSON>
  * Released under the MIT License.
  */
var VueI18n=function(e,t){"use strict";function n(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const r="undefined"!=typeof window,a=(e,t=!1)=>t?Symbol.for(e):Symbol(e),o=(e,t,n)=>l({l:e,k:t,s:n}),l=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),s=e=>"number"==typeof e&&isFinite(e),c=e=>"[object Date]"===y(e),u=e=>"[object RegExp]"===y(e),i=e=>I(e)&&0===Object.keys(e).length,f=Object.assign,m=Object.create,_=(e=null)=>m(e);function p(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/\//g,"&#x2F;").replace(/=/g,"&#x3D;")}function d(e){return e.replace(/&(?![a-zA-Z0-9#]{2,6};)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}const g=Object.prototype.hasOwnProperty;function E(e,t){return g.call(e,t)}const b=Array.isArray,v=e=>"function"==typeof e,k=e=>"string"==typeof e,h=e=>"boolean"==typeof e,L=e=>null!==e&&"object"==typeof e,N=e=>L(e)&&v(e.then)&&v(e.catch),T=Object.prototype.toString,y=e=>T.call(e),I=e=>{if(!L(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t.constructor===Object};function O(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}function C(e){let t=e;return()=>++t}const P=e=>!L(e)||b(e);function A(e,t){if(P(e)||P(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:e,des:t}=n.pop();Object.keys(e).forEach((r=>{"__proto__"!==r&&(L(e[r])&&!L(t[r])&&(t[r]=Array.isArray(e[r])?[]:_()),P(t[r])||P(e[r])?t[r]=e[r]:n.push({src:e[r],des:t[r]}))}))}}function R(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const F={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2};const S={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17};function D(e,t,n={}){const{domain:r,messages:a,args:o}=n,l=new SyntaxError(String(e));return l.code=e,t&&(l.location=t),l.domain=r,l}function M(e){throw e}const w=" ",x="\r",W="\n",U=String.fromCharCode(8232),$=String.fromCharCode(8233);function H(e){const t=e;let n=0,r=1,a=1,o=0;const l=e=>t[e]===x&&t[e+1]===W,s=e=>t[e]===$,c=e=>t[e]===U,u=e=>l(e)||(e=>t[e]===W)(e)||s(e)||c(e),i=e=>l(e)||s(e)||c(e)?W:t[e];function f(){return o=0,u(n)&&(r++,a=0),l(n)&&n++,n++,a++,t[n]}return{index:()=>n,line:()=>r,column:()=>a,peekOffset:()=>o,charAt:i,currentChar:()=>i(n),currentPeek:()=>i(n+o),next:f,peek:function(){return l(n+o)&&o++,o++,t[n+o]},reset:function(){n=0,r=1,a=1,o=0},resetPeek:function(e=0){o=e},skipToPeek:function(){const e=n+o;for(;e!==n;)f();o=0}}}const j=void 0,V="'";function X(e,t={}){const n=!1!==t.location,r=H(e),a=()=>r.index(),o=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},l=o(),s=a(),c={currentType:14,offset:s,startLoc:l,endLoc:l,lastType:14,lastOffset:s,lastStartLoc:l,lastEndLoc:l,braceNest:0,inLinked:!1,text:""},u=()=>c,{onError:i}=t;function f(e,t,r){e.endLoc=o(),e.currentType=t;const a={type:t};return n&&(a.loc=R(e.startLoc,e.endLoc)),null!=r&&(a.value=r),a}const m=e=>f(e,14);function _(e,t){return e.currentChar()===t?(e.next(),t):(S.EXPECTED_TOKEN,o(),"")}function p(e){let t="";for(;e.currentPeek()===w||e.currentPeek()===W;)t+=e.currentPeek(),e.peek();return t}function d(e){const t=p(e);return e.skipToPeek(),t}function g(e){if(e===j)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function E(e,t){const{currentType:n}=t;if(2!==n)return!1;p(e);const r=function(e){if(e===j)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function b(e){p(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function v(e,t=!0){const n=(t=!1,r="",a=!1)=>{const o=e.currentPeek();return"{"===o?"%"!==r&&t:"@"!==o&&o?"%"===o?(e.peek(),n(t,"%",!0)):"|"===o?!("%"!==r&&!a)||!(r===w||r===W):o===w?(e.peek(),n(!0,w,a)):o!==W||(e.peek(),n(!0,W,a)):"%"===r||t},r=n();return t&&e.resetPeek(),r}function k(e,t){const n=e.currentChar();return n===j?j:t(n)?(e.next(),n):null}function h(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function L(e){return k(e,h)}function N(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function T(e){return k(e,N)}function y(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function I(e){return k(e,y)}function O(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function C(e){return k(e,O)}function P(e){let t="",n="";for(;t=I(e);)n+=t;return n}function A(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if("%"===n){if(!v(e))break;t+=n,e.next()}else if(n===w||n===W)if(v(e))t+=n,e.next();else{if(b(e))break;t+=n,e.next()}else t+=n,e.next()}return t}function F(e){return e!==V&&e!==W}function D(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return M(e,t,4);case"U":return M(e,t,6);default:return S.UNKNOWN_ESCAPE_SEQUENCE,o(),""}}function M(e,t,n){_(e,t);let r="";for(let a=0;a<n;a++){const t=C(e);if(!t){S.INVALID_UNICODE_ESCAPE_SEQUENCE,o(),e.currentChar();break}r+=t}return`\\${t}${r}`}function x(e){return"{"!==e&&"}"!==e&&e!==w&&e!==W}function U(e){d(e);const t=_(e,"|");return d(e),t}function $(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&(S.NOT_ALLOW_NEST_PLACEHOLDER,o()),e.next(),n=f(t,2,"{"),d(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&(S.EMPTY_PLACEHOLDER,o()),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&d(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&(S.UNTERMINATED_CLOSING_BRACE,o()),n=X(e,t)||m(t),t.braceNest=0,n;default:{let r=!0,a=!0,l=!0;if(b(e))return t.braceNest>0&&(S.UNTERMINATED_CLOSING_BRACE,o()),n=f(t,1,U(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return S.UNTERMINATED_CLOSING_BRACE,o(),t.braceNest=0,G(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;p(e);const r=g(e.currentPeek());return e.resetPeek(),r}(e,t))return n=f(t,5,function(e){d(e);let t="",n="";for(;t=T(e);)n+=t;return e.currentChar()===j&&(S.UNTERMINATED_CLOSING_BRACE,o()),n}(e)),d(e),n;if(a=E(e,t))return n=f(t,6,function(e){d(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${P(e)}`):t+=P(e),e.currentChar()===j&&(S.UNTERMINATED_CLOSING_BRACE,o()),t}(e)),d(e),n;if(l=function(e,t){const{currentType:n}=t;if(2!==n)return!1;p(e);const r=e.currentPeek()===V;return e.resetPeek(),r}(e,t))return n=f(t,7,function(e){d(e),_(e,"'");let t="",n="";for(;t=k(e,F);)n+="\\"===t?D(e):t;const r=e.currentChar();return r===W||r===j?(S.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,o(),r===W&&(e.next(),_(e,"'")),n):(_(e,"'"),n)}(e)),d(e),n;if(!r&&!a&&!l)return n=f(t,13,function(e){d(e);let t="",n="";for(;t=k(e,x);)n+=t;return n}(e)),S.INVALID_TOKEN_IN_PLACEHOLDER,o(),n.value,d(e),n;break}}return n}function X(e,t){const{currentType:n}=t;let r=null;const a=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||a!==W&&a!==w||(S.INVALID_LINKED_FORMAT,o()),a){case"@":return e.next(),r=f(t,8,"@"),t.inLinked=!0,r;case".":return d(e),e.next(),f(t,9,".");case":":return d(e),e.next(),f(t,10,":");default:return b(e)?(r=f(t,1,U(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(8!==n)return!1;p(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;p(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(d(e),X(e,t)):function(e,t){const{currentType:n}=t;if(9!==n)return!1;p(e);const r=g(e.currentPeek());return e.resetPeek(),r}(e,t)?(d(e),f(t,12,function(e){let t="",n="";for(;t=L(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(10!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?g(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||t===w||!t)&&(t===W?(e.peek(),r()):v(e,!1))},a=r();return e.resetPeek(),a}(e,t)?(d(e),"{"===a?$(e,t)||r:f(t,11,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"%"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===w?n:(n+=r,e.next(),t(n)):n};return t("")}(e))):(8===n&&(S.INVALID_LINKED_FORMAT,o()),t.braceNest=0,t.inLinked=!1,G(e,t))}}function G(e,t){let n={type:14};if(t.braceNest>0)return $(e,t)||m(t);if(t.inLinked)return X(e,t)||m(t);switch(e.currentChar()){case"{":return $(e,t)||m(t);case"}":return S.UNBALANCED_CLOSING_BRACE,o(),e.next(),f(t,3,"}");case"@":return X(e,t)||m(t);default:{if(b(e))return n=f(t,1,U(e)),t.braceNest=0,t.inLinked=!1,n;const{isModulo:r,hasSpace:a}=function(e){const t=p(e),n="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:n,hasSpace:t.length>0}}(e);if(r)return a?f(t,0,A(e)):f(t,4,function(e){d(e);const t=e.currentChar();return"%"!==t&&(S.EXPECTED_TOKEN,o()),e.next(),"%"}(e));if(v(e))return f(t,0,A(e));break}}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:l}=c;return c.lastType=e,c.lastOffset=t,c.lastStartLoc=n,c.lastEndLoc=l,c.offset=a(),c.startLoc=o(),r.currentChar()===j?f(c,14):G(r,c)},currentOffset:a,currentPosition:o,context:u}}const G=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function Y(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function B(e={}){const t=!1!==e.location,{onError:n,onWarn:r}=e;function a(e,n,r){const a={type:e};return t&&(a.start=n,a.end=n,a.loc={start:r,end:r}),a}function o(e,n,r,a){a&&(e.type=a),t&&(e.end=n,e.loc&&(e.loc.end=r))}function l(e,t){const n=e.context(),r=a(3,n.offset,n.startLoc);return r.value=t,o(r,e.currentOffset(),e.currentPosition()),r}function s(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:l}=n,s=a(5,r,l);return s.index=parseInt(t,10),e.nextToken(),o(s,e.currentOffset(),e.currentPosition()),s}function c(e,t,n){const r=e.context(),{lastOffset:l,lastStartLoc:s}=r,c=a(4,l,s);return c.key=t,!0===n&&(c.modulo=!0),e.nextToken(),o(c,e.currentOffset(),e.currentPosition()),c}function u(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:l}=n,s=a(9,r,l);return s.value=t.replace(G,Y),e.nextToken(),o(s,e.currentOffset(),e.currentPosition()),s}function i(e){const t=e.context(),n=a(6,t.offset,t.startLoc);let r=e.nextToken();if(9===r.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:r,lastStartLoc:l}=n,s=a(8,r,l);return 12!==t.type?(S.UNEXPECTED_EMPTY_LINKED_MODIFIER,n.lastStartLoc,s.value="",o(s,r,l),{nextConsumeToken:t,node:s}):(null==t.value&&(S.UNEXPECTED_LEXICAL_ANALYSIS,n.lastStartLoc,K(t)),s.value=t.value||"",o(s,e.currentOffset(),e.currentPosition()),{node:s})}(e);n.modifier=t.node,r=t.nextConsumeToken||e.nextToken()}switch(10!==r.type&&(S.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(r)),r=e.nextToken(),2===r.type&&(r=e.nextToken()),r.type){case 11:null==r.value&&(S.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(r)),n.key=function(e,t){const n=e.context(),r=a(7,n.offset,n.startLoc);return r.value=t,o(r,e.currentOffset(),e.currentPosition()),r}(e,r.value||"");break;case 5:null==r.value&&(S.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(r)),n.key=c(e,r.value||"");break;case 6:null==r.value&&(S.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(r)),n.key=s(e,r.value||"");break;case 7:null==r.value&&(S.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(r)),n.key=u(e,r.value||"");break;default:{S.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc;const l=e.context(),s=a(7,l.offset,l.startLoc);return s.value="",o(s,l.offset,l.startLoc),n.key=s,o(n,l.offset,l.startLoc),{nextConsumeToken:r,node:n}}}return o(n,e.currentOffset(),e.currentPosition()),{node:n}}function m(e){const t=e.context(),n=a(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let r=null,f=null;do{const a=r||e.nextToken();switch(r=null,a.type){case 0:null==a.value&&(S.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(a)),n.items.push(l(e,a.value||""));break;case 6:null==a.value&&(S.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(a)),n.items.push(s(e,a.value||""));break;case 4:f=!0;break;case 5:null==a.value&&(S.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(a)),n.items.push(c(e,a.value||"",!!f)),f&&(F.USE_MODULO_SYNTAX,t.lastStartLoc,K(a),f=null);break;case 7:null==a.value&&(S.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,K(a)),n.items.push(u(e,a.value||""));break;case 8:{const t=i(e);n.items.push(t.node),r=t.nextConsumeToken||null;break}}}while(14!==t.currentType&&1!==t.currentType);return o(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function _(e){const t=e.context(),{offset:n,startLoc:r}=t,l=m(e);return 14===t.currentType?l:function(e,t,n,r){const l=e.context();let s=0===r.items.length;const c=a(1,t,n);c.cases=[],c.cases.push(r);do{const t=m(e);s||(s=0===t.items.length),c.cases.push(t)}while(14!==l.currentType);return o(c,e.currentOffset(),e.currentPosition()),c}(e,n,r,l)}return{parse:function(n){const r=X(n,f({},e)),l=r.context(),s=a(0,l.offset,l.startLoc);return t&&s.loc&&(s.loc.source=n),s.body=_(r),e.onCacheKey&&(s.cacheKey=e.onCacheKey(n)),14!==l.currentType&&(S.UNEXPECTED_LEXICAL_ANALYSIS,l.lastStartLoc,n[l.offset]),o(s,r.currentOffset(),r.currentPosition()),s}}}function K(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function z(e,t){for(let n=0;n<e.length;n++)J(e[n],t)}function J(e,t){switch(e.type){case 1:z(e.cases,t),t.helper("plural");break;case 2:z(e.items,t);break;case 6:J(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function Q(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&J(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function q(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=O(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function Z(e){switch(e.t=e.type,e.type){case 0:{const t=e;Z(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)Z(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)Z(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;Z(t.key),t.k=t.key,delete t.key,t.modifier&&(Z(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function ee(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?ee(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const a=t.cases.length;for(let n=0;n<a&&(ee(e,t.cases[n]),n!==a-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const a=t.items.length;for(let o=0;o<a&&(ee(e,t.items[o]),o!==a-1);o++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),ee(e,t.key),t.modifier?(e.push(", "),ee(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}const te=(e,t={})=>{const n=k(t.mode)?t.mode:"normal",r=k(t.filename)?t.filename:"message.intl",a=!!t.sourceMap,o=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",l=t.needIndent?t.needIndent:"arrow"!==n,s=e.helpers||[],c=function(e,t){const{sourceMap:n,filename:r,breakLineCode:a,needIndent:o}=t,l=!1!==t.location,s={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:a,needIndent:o,indentLevel:0};function c(e,t){s.code+=e}function u(e,t=!0){const n=t?a:"";c(o?n+"  ".repeat(e):n)}return l&&e.loc&&(s.source=e.loc.source),{context:()=>s,push:c,indent:function(e=!0){const t=++s.indentLevel;e&&u(t)},deindent:function(e=!0){const t=--s.indentLevel;e&&u(t)},newline:function(){u(s.indentLevel)},helper:e=>`_${e}`,needIndent:()=>s.needIndent}}(e,{mode:n,filename:r,sourceMap:a,breakLineCode:o,needIndent:l});c.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),c.indent(l),s.length>0&&(c.push(`const { ${O(s.map((e=>`${e}: _${e}`)),", ")} } = ctx`),c.newline()),c.push("return "),ee(c,e),c.deindent(l),c.push("}"),delete e.helpers;const{code:u,map:i}=c.context();return{ast:e,code:u,map:i?i.toJSON():void 0}};function ne(e,t={}){const n=f({},t),r=!!n.jit,a=!!n.minify,o=null==n.optimize||n.optimize,l=B(n).parse(e);return r?(o&&function(e){const t=e.body;2===t.type?q(t):t.cases.forEach((e=>q(e)))}(l),a&&Z(l),{ast:l,code:""}):(Q(l,n),te(l,n))}function re(e){return L(e)&&0===ue(e)&&(E(e,"b")||E(e,"body"))}const ae=["b","body"];const oe=["c","cases"];const le=["s","static"];const se=["i","items"];const ce=["t","type"];function ue(e){return pe(e,ce)}const ie=["v","value"];function fe(e,t){const n=pe(e,ie);if(null!=n)return n;throw ge(t)}const me=["m","modifier"];const _e=["k","key"];function pe(e,t,n){for(let r=0;r<t.length;r++){const n=t[r];if(E(e,n)&&null!=e[n])return e[n]}return n}const de=[...ae,...oe,...le,...se,..._e,...me,...ie,...ce];function ge(e){return new Error(`unhandled node type: ${e}`)}const Ee=[];Ee[0]={w:[0],i:[3,0],"[":[4],o:[7]},Ee[1]={w:[1],".":[2],"[":[4],o:[7]},Ee[2]={w:[2],i:[3,0],0:[3,0]},Ee[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},Ee[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},Ee[5]={"'":[4,0],o:8,l:[5,0]},Ee[6]={'"':[4,0],o:8,l:[6,0]};const be=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function ve(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function ke(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,be.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}const he=new Map;function Le(e,t){return L(e)?e[t]:null}const Ne=e=>e,Te=e=>"",ye="text",Ie=e=>0===e.length?"":O(e),Oe=e=>null==e?"":b(e)||I(e)&&e.toString===T?JSON.stringify(e,null,2):String(e);function Ce(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function Pe(e={}){const t=e.locale,n=function(e){const t=s(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(s(e.named.count)||s(e.named.n))?s(e.named.count)?e.named.count:s(e.named.n)?e.named.n:t:t}(e),r=L(e.pluralRules)&&k(t)&&v(e.pluralRules[t])?e.pluralRules[t]:Ce,a=L(e.pluralRules)&&k(t)&&v(e.pluralRules[t])?Ce:void 0,o=e.list||[],l=e.named||_();s(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,l);function c(t){const n=v(e.messages)?e.messages(t):!!L(e.messages)&&e.messages[t];return n||(e.parent?e.parent.message(t):Te)}const u=I(e.processor)&&v(e.processor.normalize)?e.processor.normalize:Ie,i=I(e.processor)&&v(e.processor.interpolate)?e.processor.interpolate:Oe,m={list:e=>o[e],named:e=>l[e],plural:e=>e[r(n,e.length,a)],linked:(t,...n)=>{const[r,a]=n;let o="text",l="";1===n.length?L(r)?(l=r.modifier||l,o=r.type||o):k(r)&&(l=r||l):2===n.length&&(k(r)&&(l=r||l),k(a)&&(o=a||o));const s=c(t)(m),u="vnode"===o&&b(s)&&l?s[0]:s;return l?(i=l,e.modifiers?e.modifiers[i]:Ne)(u,o):u;var i},message:c,type:I(e.processor)&&k(e.processor.type)?e.processor.type:ye,interpolate:i,normalize:u,values:f(_(),o,l)};return m}const Ae=S.__EXTEND_POINT__,Re=C(Ae),Fe={INVALID_ARGUMENT:Ae,INVALID_DATE_ARGUMENT:Re(),INVALID_ISO_DATE_ARGUMENT:Re(),NOT_SUPPORT_NON_STRING_MESSAGE:Re(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:Re(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:Re(),NOT_SUPPORT_LOCALE_TYPE:Re(),__EXTEND_POINT__:Re()};function Se(e,t){return null!=t.locale?Me(t.locale):Me(e.locale)}let De;function Me(e){if(k(e))return e;if(v(e)){if(e.resolvedOnce&&null!=De)return De;if("Function"===e.constructor.name){const t=e();if(N(t))throw Error(Fe.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return De=t}throw Error(Fe.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(Fe.NOT_SUPPORT_LOCALE_TYPE)}function we(e,t,n){return[...new Set([n,...b(t)?t:L(t)?Object.keys(t):k(t)?[t]:[n]])]}function xe(e,t,n){const r=k(n)?n:Ve,a=e;a.__localeChainCache||(a.__localeChainCache=new Map);let o=a.__localeChainCache.get(r);if(!o){o=[];let e=[n];for(;b(e);)e=We(o,e,t);const l=b(t)||!I(t)?t:t.default?t.default:null;e=k(l)?[l]:l,b(e)&&We(o,e,!1),a.__localeChainCache.set(r,o)}return o}function We(e,t,n){let r=!0;for(let a=0;a<t.length&&h(r);a++){const o=t[a];k(o)&&(r=Ue(e,t[a],n))}return r}function Ue(e,t,n){let r;const a=t.split("-");do{r=$e(e,a.join("-"),n),a.splice(-1,1)}while(a.length&&!0===r);return r}function $e(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const a=t.replace(/!/g,"");e.push(a),(b(n)||I(n))&&n[a]&&(r=n[a])}return r}const He="9.14.5",je=-1,Ve="en-US",Xe="",Ge=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let Ye,Be,Ke;let ze=null;const Je=e=>{ze=e},Qe=()=>ze;let qe=0;function Ze(e={}){const t=v(e.onWarn)?e.onWarn:n,r=k(e.version)?e.version:He,a=k(e.locale)||v(e.locale)?e.locale:Ve,o=v(a)?Ve:a,l=b(e.fallbackLocale)||I(e.fallbackLocale)||k(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:o,s=I(e.messages)?e.messages:et(o),c=I(e.datetimeFormats)?e.datetimeFormats:et(o),i=I(e.numberFormats)?e.numberFormats:et(o),m=f(_(),e.modifiers,{upper:(e,t)=>"text"===t&&k(e)?e.toUpperCase():"vnode"===t&&L(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&k(e)?e.toLowerCase():"vnode"===t&&L(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&k(e)?Ge(e):"vnode"===t&&L(e)&&"__v_isVNode"in e?Ge(e.children):e}),p=e.pluralRules||_(),d=v(e.missing)?e.missing:null,g=!h(e.missingWarn)&&!u(e.missingWarn)||e.missingWarn,E=!h(e.fallbackWarn)&&!u(e.fallbackWarn)||e.fallbackWarn,N=!!e.fallbackFormat,T=!!e.unresolving,y=v(e.postTranslation)?e.postTranslation:null,O=I(e.processor)?e.processor:null,C=!h(e.warnHtmlMessage)||e.warnHtmlMessage,P=!!e.escapeParameter,A=v(e.messageCompiler)?e.messageCompiler:Ye,R=v(e.messageResolver)?e.messageResolver:Be||Le,F=v(e.localeFallbacker)?e.localeFallbacker:Ke||we,S=L(e.fallbackContext)?e.fallbackContext:void 0,D=e,M=L(D.__datetimeFormatters)?D.__datetimeFormatters:new Map,w=L(D.__numberFormatters)?D.__numberFormatters:new Map,x=L(D.__meta)?D.__meta:{};qe++;const W={version:r,cid:qe,locale:a,fallbackLocale:l,messages:s,modifiers:m,pluralRules:p,missing:d,missingWarn:g,fallbackWarn:E,fallbackFormat:N,unresolving:T,postTranslation:y,processor:O,warnHtmlMessage:C,escapeParameter:P,messageCompiler:A,messageResolver:R,localeFallbacker:F,fallbackContext:S,onWarn:t,__meta:x};return W.datetimeFormats=c,W.numberFormats=i,W.__datetimeFormatters=M,W.__numberFormatters=w,W}const et=e=>({[e]:_()});function tt(e,t,n,r,a){const{missing:o,onWarn:l}=e;if(null!==o){const r=o(e,n,t,a);return k(r)?r:t}return t}function nt(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function rt(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let o=n+1;o<t.length;o++)if(r=e,a=t[o],r!==a&&r.split("-")[0]===a.split("-")[0])return!0;var r,a;return!1}function at(e){return t=>function(e,t){const n=(r=t,pe(r,ae));var r;if(null==n)throw ge(0);if(1===ue(n)){const t=function(e){return pe(e,oe,[])}(n);return e.plural(t.reduce(((t,n)=>[...t,ot(e,n)]),[]))}return ot(e,n)}(t,e)}function ot(e,t){const n=function(e){return pe(e,le)}(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=function(e){return pe(e,se,[])}(t).reduce(((t,n)=>[...t,lt(e,n)]),[]);return e.normalize(n)}}function lt(e,t){const n=ue(t);switch(n){case 3:case 9:case 7:case 8:return fe(t,n);case 4:{const r=t;if(E(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(E(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw ge(n)}case 5:{const r=t;if(E(r,"i")&&s(r.i))return e.interpolate(e.list(r.i));if(E(r,"index")&&s(r.index))return e.interpolate(e.list(r.index));throw ge(n)}case 6:{const n=t,r=function(e){return pe(e,me)}(n),a=function(e){const t=pe(e,_e);if(t)return t;throw ge(6)}(n);return e.linked(lt(e,a),r?lt(e,r):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}const st=e=>e;let ct=_();const ut=()=>"",it=e=>v(e);function ft(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:a,messageCompiler:o,fallbackLocale:l,messages:c}=e,[u,i]=pt(...t),f=h(i.missingWarn)?i.missingWarn:e.missingWarn,m=h(i.fallbackWarn)?i.fallbackWarn:e.fallbackWarn,g=h(i.escapeParameter)?i.escapeParameter:e.escapeParameter,E=!!i.resolvedMessage,v=k(i.default)||h(i.default)?h(i.default)?o?u:()=>u:i.default:n?o?u:()=>u:"",N=n||""!==v,T=Se(e,i);g&&function(e){b(e.list)?e.list=e.list.map((e=>k(e)?p(e):e)):L(e.named)&&Object.keys(e.named).forEach((t=>{k(e.named[t])&&(e.named[t]=p(e.named[t]))}))}(i);let[y,I,O]=E?[u,T,c[T]||_()]:mt(e,u,T,l,m,f),C=y,P=u;if(E||k(C)||re(C)||it(C)||N&&(C=v,P=C),!(E||(k(C)||re(C)||it(C))&&k(I)))return a?je:u;let A=!1;const R=it(C)?C:_t(e,u,I,C,P,(()=>{A=!0}));if(A)return C;const F=function(e,t,n,r){const{modifiers:a,pluralRules:o,messageResolver:l,fallbackLocale:c,fallbackWarn:u,missingWarn:i,fallbackContext:f}=e,m=r=>{let a=l(n,r);if(null==a&&f){const[,,e]=mt(f,r,t,c,u,i);a=l(e,r)}if(k(a)||re(a)){let n=!1;const o=_t(e,r,t,a,r,(()=>{n=!0}));return n?ut:o}return it(a)?a:ut},_={locale:t,modifiers:a,pluralRules:o,messages:m};e.processor&&(_.processor=e.processor);r.list&&(_.list=r.list);r.named&&(_.named=r.named);s(r.plural)&&(_.pluralIndex=r.plural);return _}(e,I,O,i),S=function(e,t,n){const r=t(n);return r}(0,R,Pe(F));let D=r?r(S,u):S;var M;return g&&k(D)&&(M=(M=(M=D).replace(/(\w+)\s*=\s*"([^"]*)"/g,((e,t,n)=>`${t}="${d(n)}"`))).replace(/(\w+)\s*=\s*'([^']*)'/g,((e,t,n)=>`${t}='${d(n)}'`)),/\s*on\w+\s*=\s*["']?[^"'>]+["']?/gi.test(M)&&(M=M.replace(/(\s+)(on)(\w+\s*=)/gi,"$1&#111;n$3")),[/(\s+(?:href|src|action|formaction)\s*=\s*["']?)\s*javascript:/gi,/(style\s*=\s*["'][^"']*url\s*\(\s*)javascript:/gi].forEach((e=>{M=M.replace(e,"$1javascript&#58;")})),D=M),D}function mt(e,t,n,r,a,o){const{messages:l,onWarn:s,messageResolver:c,localeFallbacker:u}=e,i=u(e,r,n);let f,m=_(),p=null;for(let d=0;d<i.length&&(f=i[d],m=l[f]||_(),null===(p=c(m,t))&&(p=m[t]),!(k(p)||re(p)||it(p)));d++)if(!rt(f,i)){const n=tt(e,t,f,0,"translate");n!==t&&(p=n)}return[p,f,m]}function _t(e,t,n,r,a,l){const{messageCompiler:s,warnHtmlMessage:c}=e;if(it(r)){const e=r;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==s){const e=()=>r;return e.locale=n,e.key=t,e}const u=s(r,function(e,t,n,r,a,l){return{locale:t,key:n,warnHtmlMessage:a,onError:e=>{throw l&&l(e),e},onCacheKey:e=>o(t,n,e)}}(0,n,a,0,c,l));return u.locale=n,u.key=t,u.source=r,u}function pt(...e){const[t,n,r]=e,a=_();if(!(k(t)||s(t)||it(t)||re(t)))throw Error(Fe.INVALID_ARGUMENT);const o=s(t)?String(t):(it(t),t);return s(n)?a.plural=n:k(n)?a.default=n:I(n)&&!i(n)?a.named=n:b(n)&&(a.list=n),s(r)?a.plural=r:k(r)?a.default=r:I(r)&&f(a,r),[o,a]}function dt(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:a,onWarn:o,localeFallbacker:l}=e,{__datetimeFormatters:s}=e,[c,u,m,_]=Et(...t);h(m.missingWarn)?m.missingWarn:e.missingWarn;h(m.fallbackWarn)?m.fallbackWarn:e.fallbackWarn;const p=!!m.part,d=Se(e,m),g=l(e,a,d);if(!k(c)||""===c)return new Intl.DateTimeFormat(d,_).format(u);let E,b={},v=null;for(let i=0;i<g.length&&(E=g[i],b=n[E]||{},v=b[c],!I(v));i++)tt(e,c,E,0,"datetime format");if(!I(v)||!k(E))return r?je:c;let L=`${E}__${c}`;i(_)||(L=`${L}__${JSON.stringify(_)}`);let N=s.get(L);return N||(N=new Intl.DateTimeFormat(E,f({},v,_)),s.set(L,N)),p?N.formatToParts(u):N.format(u)}const gt=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Et(...e){const[t,n,r,a]=e,o=_();let l,u=_();if(k(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(Fe.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();l=new Date(n);try{l.toISOString()}catch(i){throw Error(Fe.INVALID_ISO_DATE_ARGUMENT)}}else if(c(t)){if(isNaN(t.getTime()))throw Error(Fe.INVALID_DATE_ARGUMENT);l=t}else{if(!s(t))throw Error(Fe.INVALID_ARGUMENT);l=t}return k(n)?o.key=n:I(n)&&Object.keys(n).forEach((e=>{gt.includes(e)?u[e]=n[e]:o[e]=n[e]})),k(r)?o.locale=r:I(r)&&(u=r),I(a)&&(u=a),[o.key||"",l,o,u]}function bt(e,t,n){const r=e;for(const a in n){const e=`${t}__${a}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}}function vt(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:a,onWarn:o,localeFallbacker:l}=e,{__numberFormatters:s}=e,[c,u,m,_]=ht(...t);h(m.missingWarn)?m.missingWarn:e.missingWarn;h(m.fallbackWarn)?m.fallbackWarn:e.fallbackWarn;const p=!!m.part,d=Se(e,m),g=l(e,a,d);if(!k(c)||""===c)return new Intl.NumberFormat(d,_).format(u);let E,b={},v=null;for(let i=0;i<g.length&&(E=g[i],b=n[E]||{},v=b[c],!I(v));i++)tt(e,c,E,0,"number format");if(!I(v)||!k(E))return r?je:c;let L=`${E}__${c}`;i(_)||(L=`${L}__${JSON.stringify(_)}`);let N=s.get(L);return N||(N=new Intl.NumberFormat(E,f({},v,_)),s.set(L,N)),p?N.formatToParts(u):N.format(u)}const kt=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function ht(...e){const[t,n,r,a]=e,o=_();let l=_();if(!s(t))throw Error(Fe.INVALID_ARGUMENT);const c=t;return k(n)?o.key=n:I(n)&&Object.keys(n).forEach((e=>{kt.includes(e)?l[e]=n[e]:o[e]=n[e]})),k(r)?o.locale=r:I(r)&&(l=r),I(a)&&(l=a),[o.key||"",c,o,l]}function Lt(e,t,n){const r=e;for(const a in n){const e=`${t}__${a}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}}const Nt="9.14.5",Tt=Fe.__EXTEND_POINT__,yt=C(Tt),It={UNEXPECTED_RETURN_TYPE:Tt,INVALID_ARGUMENT:yt(),MUST_BE_CALL_SETUP_TOP:yt(),NOT_INSTALLED:yt(),NOT_AVAILABLE_IN_LEGACY_MODE:yt(),REQUIRED_VALUE:yt(),INVALID_VALUE:yt(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:yt(),NOT_INSTALLED_WITH_PROVIDE:yt(),UNEXPECTED_ERROR:yt(),NOT_COMPATIBLE_LEGACY_VUE_I18N:yt(),BRIDGE_SUPPORT_VUE_2_ONLY:yt(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:yt(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:yt(),__EXTEND_POINT__:yt()};const Ot=a("__translateVNode"),Ct=a("__datetimeParts"),Pt=a("__numberParts"),At=a("__setPluralRules"),Rt=a("__injectWithOption"),Ft=a("__dispose");function St(e){if(!L(e))return e;if(re(e))return e;for(const t in e)if(E(e,t))if(t.includes(".")){const n=t.split("."),r=n.length-1;let a=e,o=!1;for(let e=0;e<r;e++){if("__proto__"===n[e])throw new Error(`unsafe key: ${n[e]}`);if(n[e]in a||(a[n[e]]=_()),!L(a[n[e]])){o=!0;break}a=a[n[e]]}if(o||(re(a)?de.includes(n[r])||delete e[t]:(a[n[r]]=e[t],delete e[t])),!re(a)){const e=a[n[r]];L(e)&&St(e)}}else L(e[t])&&St(e[t]);return e}function Dt(e,t){const{messages:n,__i18n:r,messageResolver:a,flatJson:o}=t,l=I(n)?n:b(r)?_():{[e]:_()};if(b(r)&&r.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:n}=e;t?(l[t]=l[t]||_(),A(n,l[t])):A(n,l)}else k(e)&&A(JSON.parse(e),l)})),null==a&&o)for(const s in l)E(l,s)&&St(l[s]);return l}function Mt(e){return e.type}function wt(e,t,n){let r=L(t.messages)?t.messages:_();"__i18nGlobal"in n&&(r=Dt(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const a=Object.keys(r);if(a.length&&a.forEach((t=>{e.mergeLocaleMessage(t,r[t])})),L(t.datetimeFormats)){const n=Object.keys(t.datetimeFormats);n.length&&n.forEach((n=>{e.mergeDateTimeFormat(n,t.datetimeFormats[n])}))}if(L(t.numberFormats)){const n=Object.keys(t.numberFormats);n.length&&n.forEach((n=>{e.mergeNumberFormat(n,t.numberFormats[n])}))}}function xt(e){return t.createVNode(t.Text,null,e,0)}const Wt=()=>[],Ut=()=>!1;let $t=0;function Ht(e){return(n,r,a,o)=>e(r,a,t.getCurrentInstance()||void 0,o)}function jt(e={},n){const{__root:a,__injectWithOption:o}=e,l=void 0===a,c=e.flatJson,i=r?t.ref:t.shallowRef,m=!!e.translateExistCompatible;let _=!h(e.inheritLocale)||e.inheritLocale;const p=i(a&&_?a.locale.value:k(e.locale)?e.locale:Ve),d=i(a&&_?a.fallbackLocale.value:k(e.fallbackLocale)||b(e.fallbackLocale)||I(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:p.value),g=i(Dt(p.value,e)),N=i(I(e.datetimeFormats)?e.datetimeFormats:{[p.value]:{}}),T=i(I(e.numberFormats)?e.numberFormats:{[p.value]:{}});let y=a?a.missingWarn:!h(e.missingWarn)&&!u(e.missingWarn)||e.missingWarn,O=a?a.fallbackWarn:!h(e.fallbackWarn)&&!u(e.fallbackWarn)||e.fallbackWarn,C=a?a.fallbackRoot:!h(e.fallbackRoot)||e.fallbackRoot,P=!!e.fallbackFormat,R=v(e.missing)?e.missing:null,F=v(e.missing)?Ht(e.missing):null,S=v(e.postTranslation)?e.postTranslation:null,D=a?a.warnHtmlMessage:!h(e.warnHtmlMessage)||e.warnHtmlMessage,M=!!e.escapeParameter;const w=a?a.modifiers:I(e.modifiers)?e.modifiers:{};let x,W=e.pluralRules||a&&a.pluralRules;x=(()=>{l&&Je(null);const t={version:Nt,locale:p.value,fallbackLocale:d.value,messages:g.value,modifiers:w,pluralRules:W,missing:null===F?void 0:F,missingWarn:y,fallbackWarn:O,fallbackFormat:P,unresolving:!0,postTranslation:null===S?void 0:S,warnHtmlMessage:D,escapeParameter:M,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=N.value,t.numberFormats=T.value,t.__datetimeFormatters=I(x)?x.__datetimeFormatters:void 0,t.__numberFormatters=I(x)?x.__numberFormatters:void 0;const n=Ze(t);return l&&Je(n),n})(),nt(x,p.value,d.value);const U=t.computed({get:()=>p.value,set:e=>{p.value=e,x.locale=p.value}}),$=t.computed({get:()=>d.value,set:e=>{d.value=e,x.fallbackLocale=d.value,nt(x,p.value,e)}}),H=t.computed((()=>g.value)),j=t.computed((()=>N.value)),V=t.computed((()=>T.value));const X=(e,t,n,r,o,c)=>{let u;p.value,d.value,g.value,N.value,T.value;try{0,l||(x.fallbackContext=a?Qe():void 0),u=e(x)}finally{l||(x.fallbackContext=void 0)}if("translate exists"!==n&&s(u)&&u===je||"translate exists"===n&&!u){const[e,n]=t();return a&&C?r(a):o(e)}if(c(u))return u;throw Error(It.UNEXPECTED_RETURN_TYPE)};function G(...e){return X((t=>Reflect.apply(ft,null,[t,...e])),(()=>pt(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>k(e)))}const Y={normalize:function(e){return e.map((e=>k(e)||s(e)||h(e)?xt(String(e)):e))},interpolate:e=>e,type:"vnode"};function B(e){return g.value[e]||{}}$t++,a&&r&&(t.watch(a.locale,(e=>{_&&(p.value=e,x.locale=e,nt(x,p.value,d.value))})),t.watch(a.fallbackLocale,(e=>{_&&(d.value=e,x.fallbackLocale=e,nt(x,p.value,d.value))})));const K={id:$t,locale:U,fallbackLocale:$,get inheritLocale(){return _},set inheritLocale(e){_=e,e&&a&&(p.value=a.locale.value,d.value=a.fallbackLocale.value,nt(x,p.value,d.value))},get availableLocales(){return Object.keys(g.value).sort()},messages:H,get modifiers(){return w},get pluralRules(){return W||{}},get isGlobal(){return l},get missingWarn(){return y},set missingWarn(e){y=e,x.missingWarn=y},get fallbackWarn(){return O},set fallbackWarn(e){O=e,x.fallbackWarn=O},get fallbackRoot(){return C},set fallbackRoot(e){C=e},get fallbackFormat(){return P},set fallbackFormat(e){P=e,x.fallbackFormat=P},get warnHtmlMessage(){return D},set warnHtmlMessage(e){D=e,x.warnHtmlMessage=e},get escapeParameter(){return M},set escapeParameter(e){M=e,x.escapeParameter=e},t:G,getLocaleMessage:B,setLocaleMessage:function(e,t){if(c){const n={[e]:t};for(const e in n)E(n,e)&&St(n[e]);t=n[e]}g.value[e]=t,x.messages=g.value},mergeLocaleMessage:function(e,t){g.value[e]=g.value[e]||{};const n={[e]:t};if(c)for(const r in n)E(n,r)&&St(n[r]);A(t=n[e],g.value[e]),x.messages=g.value},getPostTranslationHandler:function(){return v(S)?S:null},setPostTranslationHandler:function(e){S=e,x.postTranslation=e},getMissingHandler:function(){return R},setMissingHandler:function(e){null!==e&&(F=Ht(e)),R=e,x.missing=F},[At]:function(e){W=e,x.pluralRules=W}};return K.datetimeFormats=j,K.numberFormats=V,K.rt=function(...e){const[t,n,r]=e;if(r&&!L(r))throw Error(It.INVALID_ARGUMENT);return G(t,n,f({resolvedMessage:!0},r||{}))},K.te=function(e,t){return X((()=>{if(!e)return!1;const n=B(k(t)?t:p.value),r=x.messageResolver(n,e);return m?null!=r:re(r)||it(r)||k(r)}),(()=>[e]),"translate exists",(n=>Reflect.apply(n.te,n,[e,t])),Ut,(e=>h(e)))},K.tm=function(e){const t=function(e){let t=null;const n=xe(x,d.value,p.value);for(let r=0;r<n.length;r++){const a=g.value[n[r]]||{},o=x.messageResolver(a,e);if(null!=o){t=o;break}}return t}(e);return null!=t?t:a&&a.tm(e)||{}},K.d=function(...e){return X((t=>Reflect.apply(dt,null,[t,...e])),(()=>Et(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>Xe),(e=>k(e)))},K.n=function(...e){return X((t=>Reflect.apply(vt,null,[t,...e])),(()=>ht(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>Xe),(e=>k(e)))},K.getDateTimeFormat=function(e){return N.value[e]||{}},K.setDateTimeFormat=function(e,t){N.value[e]=t,x.datetimeFormats=N.value,bt(x,e,t)},K.mergeDateTimeFormat=function(e,t){N.value[e]=f(N.value[e]||{},t),x.datetimeFormats=N.value,bt(x,e,t)},K.getNumberFormat=function(e){return T.value[e]||{}},K.setNumberFormat=function(e,t){T.value[e]=t,x.numberFormats=T.value,Lt(x,e,t)},K.mergeNumberFormat=function(e,t){T.value[e]=f(T.value[e]||{},t),x.numberFormats=T.value,Lt(x,e,t)},K[Rt]=o,K[Ot]=function(...e){return X((t=>{let n;const r=t;try{r.processor=Y,n=Reflect.apply(ft,null,[r,...e])}finally{r.processor=null}return n}),(()=>pt(...e)),"translate",(t=>t[Ot](...e)),(e=>[xt(e)]),(e=>b(e)))},K[Ct]=function(...e){return X((t=>Reflect.apply(dt,null,[t,...e])),(()=>Et(...e)),"datetime format",(t=>t[Ct](...e)),Wt,(e=>k(e)||b(e)))},K[Pt]=function(...e){return X((t=>Reflect.apply(vt,null,[t,...e])),(()=>ht(...e)),"number format",(t=>t[Pt](...e)),Wt,(e=>k(e)||b(e)))},K}function Vt(e={},t){{const t=jt(function(e){const t=k(e.locale)?e.locale:Ve,n=k(e.fallbackLocale)||b(e.fallbackLocale)||I(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,r=v(e.missing)?e.missing:void 0,a=!h(e.silentTranslationWarn)&&!u(e.silentTranslationWarn)||!e.silentTranslationWarn,o=!h(e.silentFallbackWarn)&&!u(e.silentFallbackWarn)||!e.silentFallbackWarn,l=!h(e.fallbackRoot)||e.fallbackRoot,s=!!e.formatFallbackMessages,c=I(e.modifiers)?e.modifiers:{},i=e.pluralizationRules,m=v(e.postTranslation)?e.postTranslation:void 0,_=!k(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,p=!!e.escapeParameterHtml,d=!h(e.sync)||e.sync;let g=e.messages;if(I(e.sharedMessages)){const t=e.sharedMessages;g=Object.keys(t).reduce(((e,n)=>{const r=e[n]||(e[n]={});return f(r,t[n]),e}),g||{})}const{__i18n:E,__root:L,__injectWithOption:N}=e,T=e.datetimeFormats,y=e.numberFormats,O=e.flatJson,C=e.translateExistCompatible;return{locale:t,fallbackLocale:n,messages:g,flatJson:O,datetimeFormats:T,numberFormats:y,missing:r,missingWarn:a,fallbackWarn:o,fallbackRoot:l,fallbackFormat:s,modifiers:c,pluralRules:i,postTranslation:m,warnHtmlMessage:_,escapeParameter:p,messageResolver:e.messageResolver,inheritLocale:d,translateExistCompatible:C,__i18n:E,__root:L,__injectWithOption:N}}(e)),{__extender:n}=e,r={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate:()=>[]}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return h(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=h(e)?!e:e},get silentFallbackWarn(){return h(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=h(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[n,r,a]=e,o={};let l=null,s=null;if(!k(n))throw Error(It.INVALID_ARGUMENT);const c=n;return k(r)?o.locale=r:b(r)?l=r:I(r)&&(s=r),b(a)?l=a:I(a)&&(s=a),Reflect.apply(t.t,t,[c,l||s||{},o])},rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[n,r,a]=e,o={plural:1};let l=null,c=null;if(!k(n))throw Error(It.INVALID_ARGUMENT);const u=n;return k(r)?o.locale=r:s(r)?o.plural=r:b(r)?l=r:I(r)&&(c=r),k(a)?o.locale=a:b(a)?l=a:I(a)&&(c=a),Reflect.apply(t.t,t,[u,l||c||{},o])},te:(e,n)=>t.te(e,n),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,n){t.setLocaleMessage(e,n)},mergeLocaleMessage(e,n){t.mergeLocaleMessage(e,n)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,n){t.setDateTimeFormat(e,n)},mergeDateTimeFormat(e,n){t.mergeDateTimeFormat(e,n)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,n){t.setNumberFormat(e,n)},mergeNumberFormat(e,n){t.mergeNumberFormat(e,n)},getChoiceIndex:(e,t)=>-1};return r.__extender=n,r}}const Xt={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function Gt(e){return t.Fragment}const Yt=t.defineComponent({name:"i18n-t",props:f({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>s(e)||!isNaN(e)}},Xt),setup(e,n){const{slots:r,attrs:a}=n,o=e.i18n||an({useScope:e.scope,__useComponent:!0});return()=>{const l=Object.keys(r).filter((e=>"_"!==e)),s=_();e.locale&&(s.locale=e.locale),void 0!==e.plural&&(s.plural=k(e.plural)?+e.plural:e.plural);const c=function({slots:e},n){if(1===n.length&&"default"===n[0])return(e.default?e.default():[]).reduce(((e,n)=>[...e,...n.type===t.Fragment?n.children:[n]]),[]);return n.reduce(((t,n)=>{const r=e[n];return r&&(t[n]=r()),t}),_())}(n,l),u=o[Ot](e.keypath,c,s),i=f(_(),a),m=k(e.tag)||L(e.tag)?e.tag:Gt();return t.h(m,i,u)}}}),Bt=Yt;function Kt(e,n,r,a){const{slots:o,attrs:l}=n;return()=>{const n={part:!0};let s=_();e.locale&&(n.locale=e.locale),k(e.format)?n.key=e.format:L(e.format)&&(k(e.format.key)&&(n.key=e.format.key),s=Object.keys(e.format).reduce(((t,n)=>r.includes(n)?f(_(),t,{[n]:e.format[n]}):t),_()));const c=a(e.value,n,s);let u=[n.key];b(c)?u=c.map(((e,t)=>{const n=o[e.type],r=n?n({[e.type]:e.value,index:t,parts:c}):[e.value];var a;return b(a=r)&&!k(a[0])&&(r[0].key=`${e.type}-${t}`),r})):k(c)&&(u=[c]);const i=f(_(),l),m=k(e.tag)||L(e.tag)?e.tag:Gt();return t.h(m,i,u)}}const zt=t.defineComponent({name:"i18n-n",props:f({value:{type:Number,required:!0},format:{type:[String,Object]}},Xt),setup(e,t){const n=e.i18n||an({useScope:e.scope,__useComponent:!0});return Kt(e,t,kt,((...e)=>n[Pt](...e)))}}),Jt=zt,Qt=t.defineComponent({name:"i18n-d",props:f({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},Xt),setup(e,t){const n=e.i18n||an({useScope:e.scope,__useComponent:!0});return Kt(e,t,gt,((...e)=>n[Ct](...e)))}}),qt=Qt;function Zt(e){const n=t=>{const{instance:n,modifiers:r,value:a}=t;if(!n||!n.$)throw Error(It.UNEXPECTED_ERROR);const o=function(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return null!=r?r.__composer:e.global.__composer}}(e,n.$),l=en(a);return[Reflect.apply(o.t,o,[...tn(l)]),o]};return{created:(a,o)=>{const[l,s]=n(o);r&&e.global===s&&(a.__i18nWatcher=t.watch(s.locale,(()=>{o.instance&&o.instance.$forceUpdate()}))),a.__composer=s,a.textContent=l},unmounted:e=>{r&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const n=e.__composer,r=en(t);e.textContent=Reflect.apply(n.t,n,[...tn(r)])}},getSSRProps:e=>{const[t]=n(e);return{textContent:t}}}}function en(e){if(k(e))return{path:e};if(I(e)){if(!("path"in e))throw Error(It.REQUIRED_VALUE,"path");return e}throw Error(It.INVALID_VALUE)}function tn(e){const{path:t,locale:n,args:r,choice:a,plural:o}=e,l={},c=r||{};return k(n)&&(l.locale=n),s(a)&&(l.plural=a),s(o)&&(l.plural=o),[t,c,l]}function nn(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[At](t.pluralizationRules||e.pluralizationRules);const n=Dt(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach((t=>e.mergeLocaleMessage(t,n[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((n=>e.mergeNumberFormat(n,t.numberFormats[n]))),e}const rn=a("global-vue-i18n");function an(e={}){const n=t.getCurrentInstance();if(null==n)throw Error(It.MUST_BE_CALL_SETUP_TOP);if(!n.isCE&&null!=n.appContext.app&&!n.appContext.app.__VUE_I18N_SYMBOL__)throw Error(It.NOT_INSTALLED);const r=function(e){{const n=t.inject(e.isCE?rn:e.appContext.app.__VUE_I18N_SYMBOL__);if(!n)throw function(e,...t){return D(e,null,void 0)}(e.isCE?It.NOT_INSTALLED_WITH_PROVIDE:It.UNEXPECTED_ERROR);return n}}(n),a=function(e){return"composition"===e.mode?e.global:e.global.__composer}(r),o=Mt(n),l=function(e,t){return i(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,o);if("legacy"===r.mode&&!e.__useComponent){if(!r.allowComposition)throw Error(It.NOT_AVAILABLE_IN_LEGACY_MODE);return function(e,n,r,a={}){const o="local"===n,l=t.shallowRef(null);if(o&&e.proxy&&!e.proxy.$options.i18n&&!e.proxy.$options.__i18n)throw Error(It.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const s=h(a.inheritLocale)?a.inheritLocale:!k(a.locale),c=t.ref(!o||s?r.locale.value:k(a.locale)?a.locale:Ve),i=t.ref(!o||s?r.fallbackLocale.value:k(a.fallbackLocale)||b(a.fallbackLocale)||I(a.fallbackLocale)||!1===a.fallbackLocale?a.fallbackLocale:c.value),f=t.ref(Dt(c.value,a)),m=t.ref(I(a.datetimeFormats)?a.datetimeFormats:{[c.value]:{}}),_=t.ref(I(a.numberFormats)?a.numberFormats:{[c.value]:{}}),p=o?r.missingWarn:!h(a.missingWarn)&&!u(a.missingWarn)||a.missingWarn,d=o?r.fallbackWarn:!h(a.fallbackWarn)&&!u(a.fallbackWarn)||a.fallbackWarn,g=o?r.fallbackRoot:!h(a.fallbackRoot)||a.fallbackRoot,E=!!a.fallbackFormat,L=v(a.missing)?a.missing:null,N=v(a.postTranslation)?a.postTranslation:null,T=o?r.warnHtmlMessage:!h(a.warnHtmlMessage)||a.warnHtmlMessage,y=!!a.escapeParameter,O=o?r.modifiers:I(a.modifiers)?a.modifiers:{},C=a.pluralRules||o&&r.pluralRules;function P(){return[c.value,i.value,f.value,m.value,_.value]}const A=t.computed({get:()=>l.value?l.value.locale.value:c.value,set:e=>{l.value&&(l.value.locale.value=e),c.value=e}}),R=t.computed({get:()=>l.value?l.value.fallbackLocale.value:i.value,set:e=>{l.value&&(l.value.fallbackLocale.value=e),i.value=e}}),F=t.computed((()=>l.value?l.value.messages.value:f.value)),S=t.computed((()=>m.value)),D=t.computed((()=>_.value));function M(){return l.value?l.value.getPostTranslationHandler():N}function w(e){l.value&&l.value.setPostTranslationHandler(e)}function x(){return l.value?l.value.getMissingHandler():L}function W(e){l.value&&l.value.setMissingHandler(e)}function U(e){return P(),e()}function $(...e){return l.value?U((()=>Reflect.apply(l.value.t,null,[...e]))):U((()=>""))}function H(...e){return l.value?Reflect.apply(l.value.rt,null,[...e]):""}function j(...e){return l.value?U((()=>Reflect.apply(l.value.d,null,[...e]))):U((()=>""))}function V(...e){return l.value?U((()=>Reflect.apply(l.value.n,null,[...e]))):U((()=>""))}function X(e){return l.value?l.value.tm(e):{}}function G(e,t){return!!l.value&&l.value.te(e,t)}function Y(e){return l.value?l.value.getLocaleMessage(e):{}}function B(e,t){l.value&&(l.value.setLocaleMessage(e,t),f.value[e]=t)}function K(e,t){l.value&&l.value.mergeLocaleMessage(e,t)}function z(e){return l.value?l.value.getDateTimeFormat(e):{}}function J(e,t){l.value&&(l.value.setDateTimeFormat(e,t),m.value[e]=t)}function Q(e,t){l.value&&l.value.mergeDateTimeFormat(e,t)}function q(e){return l.value?l.value.getNumberFormat(e):{}}function Z(e,t){l.value&&(l.value.setNumberFormat(e,t),_.value[e]=t)}function ee(e,t){l.value&&l.value.mergeNumberFormat(e,t)}const te={get id(){return l.value?l.value.id:-1},locale:A,fallbackLocale:R,messages:F,datetimeFormats:S,numberFormats:D,get inheritLocale(){return l.value?l.value.inheritLocale:s},set inheritLocale(e){l.value&&(l.value.inheritLocale=e)},get availableLocales(){return l.value?l.value.availableLocales:Object.keys(f.value)},get modifiers(){return l.value?l.value.modifiers:O},get pluralRules(){return l.value?l.value.pluralRules:C},get isGlobal(){return!!l.value&&l.value.isGlobal},get missingWarn(){return l.value?l.value.missingWarn:p},set missingWarn(e){l.value&&(l.value.missingWarn=e)},get fallbackWarn(){return l.value?l.value.fallbackWarn:d},set fallbackWarn(e){l.value&&(l.value.missingWarn=e)},get fallbackRoot(){return l.value?l.value.fallbackRoot:g},set fallbackRoot(e){l.value&&(l.value.fallbackRoot=e)},get fallbackFormat(){return l.value?l.value.fallbackFormat:E},set fallbackFormat(e){l.value&&(l.value.fallbackFormat=e)},get warnHtmlMessage(){return l.value?l.value.warnHtmlMessage:T},set warnHtmlMessage(e){l.value&&(l.value.warnHtmlMessage=e)},get escapeParameter(){return l.value?l.value.escapeParameter:y},set escapeParameter(e){l.value&&(l.value.escapeParameter=e)},t:$,getPostTranslationHandler:M,setPostTranslationHandler:w,getMissingHandler:x,setMissingHandler:W,rt:H,d:j,n:V,tm:X,te:G,getLocaleMessage:Y,setLocaleMessage:B,mergeLocaleMessage:K,getDateTimeFormat:z,setDateTimeFormat:J,mergeDateTimeFormat:Q,getNumberFormat:q,setNumberFormat:Z,mergeNumberFormat:ee};function ne(e){e.locale.value=c.value,e.fallbackLocale.value=i.value,Object.keys(f.value).forEach((t=>{e.mergeLocaleMessage(t,f.value[t])})),Object.keys(m.value).forEach((t=>{e.mergeDateTimeFormat(t,m.value[t])})),Object.keys(_.value).forEach((t=>{e.mergeNumberFormat(t,_.value[t])})),e.escapeParameter=y,e.fallbackFormat=E,e.fallbackRoot=g,e.fallbackWarn=d,e.missingWarn=p,e.warnHtmlMessage=T}return t.onBeforeMount((()=>{if(null==e.proxy||null==e.proxy.$i18n)throw Error(It.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const t=l.value=e.proxy.$i18n.__composer;"global"===n?(c.value=t.locale.value,i.value=t.fallbackLocale.value,f.value=t.messages.value,m.value=t.datetimeFormats.value,_.value=t.numberFormats.value):o&&ne(t)})),te}(n,l,a,e)}if("global"===l)return wt(a,e,o),a;if("parent"===l){let t=function(e,t,n=!1){let r=null;const a=t.root;let o=function(e,t=!1){if(null==e)return null;return t&&e.vnode.ctx||e.parent}(t,n);for(;null!=o;){const t=e;if("composition"===e.mode)r=t.__getInstance(o);else{const e=t.__getInstance(o);null!=e&&(r=e.__composer,n&&r&&!r[Rt]&&(r=null))}if(null!=r)break;if(a===o)break;o=o.parent}return r}(r,n,e.__useComponent);return null==t&&(t=a),t}const s=r;let c=s.__getInstance(n);if(null==c){const r=f({},e);"__i18n"in o&&(r.__i18n=o.__i18n),a&&(r.__root=a),c=jt(r),s.__composerExtend&&(c[Ft]=s.__composerExtend(c)),function(e,n,r){t.onMounted((()=>{}),n),t.onUnmounted((()=>{const t=r;e.__deleteInstance(n);const a=t[Ft];a&&(a(),delete t[Ft])}),n)}(s,n,c),s.__setInstance(n,c)}return c}const on=["locale","fallbackLocale","availableLocales"],ln=["t","rt","d","n","tm","te"];return Ye=function(e,t){if(k(e)){!h(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||st)(e),r=ct[n];if(r)return r;const{ast:a,detectError:o}=function(e,t={}){let n=!1;const r=t.onError||M;return t.onError=e=>{n=!0,r(e)},{...ne(e,t),detectError:n}}(e,{...t,location:!1,jit:!0}),l=at(a);return o?l:ct[n]=l}{const t=e.cacheKey;return t?ct[t]||(ct[t]=at(e)):at(e)}},Be=function(e,t){if(!L(e))return null;let n=he.get(t);if(n||(n=function(e){const t=[];let n,r,a,o,l,s,c,u=-1,i=0,f=0;const m=[];function _(){const t=e[u+1];if(5===i&&"'"===t||6===i&&'"'===t)return u++,a="\\"+t,m[0](),!0}for(m[0]=()=>{void 0===r?r=a:r+=a},m[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},m[2]=()=>{m[0](),f++},m[3]=()=>{if(f>0)f--,i=4,m[0]();else{if(f=0,void 0===r)return!1;if(r=ke(r),!1===r)return!1;m[1]()}};null!==i;)if(u++,n=e[u],"\\"!==n||!_()){if(o=ve(n),c=Ee[i],l=c[o]||c.l||8,8===l)return;if(i=l[0],void 0!==l[1]&&(s=m[l[1]],s&&(a=n,!1===s())))return;if(7===i)return t}}(t),n&&he.set(t,n)),!n)return null;const r=n.length;let a=e,o=0;for(;o<r;){const e=n[o];if(de.includes(e)&&re(a))return null;const t=a[e];if(void 0===t)return null;if(v(a))return null;a=t,o++}return a},Ke=xe,e.DatetimeFormat=Qt,e.I18nD=qt,e.I18nInjectionKey=rn,e.I18nN=Jt,e.I18nT=Bt,e.NumberFormat=zt,e.Translation=Yt,e.VERSION=Nt,e.castToVueI18n=e=>{if(!("__VUE_I18N_BRIDGE__"in e))throw Error(It.NOT_COMPATIBLE_LEGACY_VUE_I18N);return e},e.createI18n=function(e={},n){const r=!h(e.legacy)||e.legacy,o=!h(e.globalInjection)||e.globalInjection,l=!r||!!e.allowComposition,s=new Map,[c,u]=function(e,n,r){const a=t.effectScope();{const t=n?a.run((()=>Vt(e))):a.run((()=>jt(e)));if(null==t)throw Error(It.UNEXPECTED_ERROR);return[a,t]}}(e,r),i=a("");{const e={get mode(){return r?"legacy":"composition"},get allowComposition(){return l},async install(n,...a){if(n.__VUE_I18N_SYMBOL__=i,n.provide(n.__VUE_I18N_SYMBOL__,e),I(a[0])){const t=a[0];e.__composerExtend=t.__composerExtend,e.__vueI18nExtend=t.__vueI18nExtend}let l=null;!r&&o&&(l=function(e,n){const r=Object.create(null);on.forEach((e=>{const a=Object.getOwnPropertyDescriptor(n,e);if(!a)throw Error(It.UNEXPECTED_ERROR);const o=t.isRef(a.value)?{get:()=>a.value.value,set(e){a.value.value=e}}:{get:()=>a.get&&a.get()};Object.defineProperty(r,e,o)})),e.config.globalProperties.$i18n=r,ln.forEach((t=>{const r=Object.getOwnPropertyDescriptor(n,t);if(!r||!r.value)throw Error(It.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${t}`,r)}));const a=()=>{delete e.config.globalProperties.$i18n,ln.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))};return a}(n,e.global)),function(e,t,...n){const r=I(n[0])?n[0]:{},a=!!r.useI18nComponentName;(!h(r.globalInstall)||r.globalInstall)&&([a?"i18n":Yt.name,"I18nT"].forEach((t=>e.component(t,Yt))),[zt.name,"I18nN"].forEach((t=>e.component(t,zt))),[Qt.name,"I18nD"].forEach((t=>e.component(t,Qt)))),e.directive("t",Zt(t))}(n,e,...a),r&&n.mixin(function(e,n,r){return{beforeCreate(){const a=t.getCurrentInstance();if(!a)throw Error(It.UNEXPECTED_ERROR);const o=this.$options;if(o.i18n){const t=o.i18n;if(o.__i18n&&(t.__i18n=o.__i18n),t.__root=n,this===this.$root)this.$i18n=nn(e,t);else{t.__injectWithOption=!0,t.__extender=r.__vueI18nExtend,this.$i18n=Vt(t);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(o.__i18n)if(this===this.$root)this.$i18n=nn(e,o);else{this.$i18n=Vt({__i18n:o.__i18n,__injectWithOption:!0,__extender:r.__vueI18nExtend,__root:n});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;o.__i18nGlobal&&wt(n,o,o),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),r.__setInstance(a,this.$i18n)},mounted(){},unmounted(){const e=t.getCurrentInstance();if(!e)throw Error(It.UNEXPECTED_ERROR);const n=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,n.__disposer&&(n.__disposer(),delete n.__disposer,delete n.__extender),r.__deleteInstance(e),delete this.$i18n}}}(u,u.__composer,e));const s=n.unmount;n.unmount=()=>{l&&l(),e.dispose(),s()}},get global(){return u},dispose(){c.stop()},__instances:s,__getInstance:function(e){return s.get(e)||null},__setInstance:function(e,t){s.set(e,t)},__deleteInstance:function(e){s.delete(e)}};return e}},e.useI18n=an,e.vTDirective=Zt,e}({},Vue);
