/*!
  * vue-i18n v9.14.5
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
import{createVNode,Text,computed,watch,getCurrentInstance,ref,shallowRef,Fragment,defineComponent,h,effectScope,inject,onMounted,onUnmounted,onBeforeMount,isRef}from"vue";function warn(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const inBrowser="undefined"!=typeof window,makeSymbol=(e,t=!1)=>t?Symbol.for(e):Symbol(e),generateFormatCacheKey=(e,t,a)=>friendlyJSONstringify({l:e,k:t,s:a}),friendlyJSONstringify=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),isNumber=e=>"number"==typeof e&&isFinite(e),isDate=e=>"[object Date]"===toTypeString(e),isRegExp=e=>"[object RegExp]"===toTypeString(e),isEmptyObject=e=>isPlainObject(e)&&0===Object.keys(e).length,assign=Object.assign,_create=Object.create,create=(e=null)=>_create(e);function escapeHtml(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/\//g,"&#x2F;").replace(/=/g,"&#x3D;")}function escapeAttributeValue(e){return e.replace(/&(?![a-zA-Z0-9#]{2,6};)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function sanitizeTranslatedHtml(e){e=(e=e.replace(/(\w+)\s*=\s*"([^"]*)"/g,((e,t,a)=>`${t}="${escapeAttributeValue(a)}"`))).replace(/(\w+)\s*=\s*'([^']*)'/g,((e,t,a)=>`${t}='${escapeAttributeValue(a)}'`));/\s*on\w+\s*=\s*["']?[^"'>]+["']?/gi.test(e)&&(e=e.replace(/(\s+)(on)(\w+\s*=)/gi,"$1&#111;n$3"));return[/(\s+(?:href|src|action|formaction)\s*=\s*["']?)\s*javascript:/gi,/(style\s*=\s*["'][^"']*url\s*\(\s*)javascript:/gi].forEach((t=>{e=e.replace(t,"$1javascript&#58;")})),e}const hasOwnProperty=Object.prototype.hasOwnProperty;function hasOwn(e,t){return hasOwnProperty.call(e,t)}const isArray=Array.isArray,isFunction=e=>"function"==typeof e,isString=e=>"string"==typeof e,isBoolean=e=>"boolean"==typeof e,isObject=e=>null!==e&&"object"==typeof e,isPromise=e=>isObject(e)&&isFunction(e.then)&&isFunction(e.catch),objectToString=Object.prototype.toString,toTypeString=e=>objectToString.call(e),isPlainObject=e=>{if(!isObject(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t.constructor===Object},toDisplayString=e=>null==e?"":isArray(e)||isPlainObject(e)&&e.toString===objectToString?JSON.stringify(e,null,2):String(e);function join(e,t=""){return e.reduce(((e,a,r)=>0===r?e+a:e+t+a),"")}function incrementer(e){let t=e;return()=>++t}const isNotObjectOrIsArray=e=>!isObject(e)||isArray(e);function deepCopy(e,t){if(isNotObjectOrIsArray(e)||isNotObjectOrIsArray(t))throw new Error("Invalid value");const a=[{src:e,des:t}];for(;a.length;){const{src:e,des:t}=a.pop();Object.keys(e).forEach((r=>{"__proto__"!==r&&(isObject(e[r])&&!isObject(t[r])&&(t[r]=Array.isArray(e[r])?[]:create()),isNotObjectOrIsArray(t[r])||isNotObjectOrIsArray(e[r])?t[r]=e[r]:a.push({src:e[r],des:t[r]}))}))}}const CompileErrorCodes={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17};function createCompileError(e,t,a={}){const{domain:r,messages:n,args:s}=a,l=new SyntaxError(String(e));return l.code=e,t&&(l.location=t),l.domain=r,l}function isMessageAST(e){return isObject(e)&&0===resolveType(e)&&(hasOwn(e,"b")||hasOwn(e,"body"))}const PROPS_BODY=["b","body"],PROPS_CASES=["c","cases"],PROPS_STATIC=["s","static"],PROPS_ITEMS=["i","items"],PROPS_TYPE=["t","type"];function resolveType(e){return resolveProps(e,PROPS_TYPE)}const PROPS_VALUE=["v","value"],PROPS_MODIFIER=["m","modifier"],PROPS_KEY=["k","key"];function resolveProps(e,t,a){for(let r=0;r<t.length;r++){const a=t[r];if(hasOwn(e,a)&&null!=e[a])return e[a]}return a}const AST_NODE_PROPS_KEYS=[...PROPS_BODY,...PROPS_CASES,...PROPS_STATIC,...PROPS_ITEMS,...PROPS_KEY,...PROPS_MODIFIER,...PROPS_VALUE,...PROPS_TYPE],pathStateMachine=[];pathStateMachine[0]={w:[0],i:[3,0],"[":[4],o:[7]},pathStateMachine[1]={w:[1],".":[2],"[":[4],o:[7]},pathStateMachine[2]={w:[2],i:[3,0],0:[3,0]},pathStateMachine[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},pathStateMachine[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},pathStateMachine[5]={"'":[4,0],o:8,l:[5,0]},pathStateMachine[6]={'"':[4,0],o:8,l:[6,0]};const literalValueRE=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function isLiteral(e){return literalValueRE.test(e)}function stripQuotes(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}function getPathCharType(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function formatSubPath(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(isLiteral(t)?stripQuotes(t):"*"+t)}function parse(e){const t=[];let a,r,n,s,l,o,i,c=-1,u=0,m=0;const g=[];function _(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,n="\\"+t,g[0](),!0}for(g[0]=()=>{void 0===r?r=n:r+=n},g[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},g[2]=()=>{g[0](),m++},g[3]=()=>{if(m>0)m--,u=4,g[0]();else{if(m=0,void 0===r)return!1;if(r=formatSubPath(r),!1===r)return!1;g[1]()}};null!==u;)if(c++,a=e[c],"\\"!==a||!_()){if(s=getPathCharType(a),i=pathStateMachine[u],l=i[s]||i.l||8,8===l)return;if(u=l[0],void 0!==l[1]&&(o=g[l[1]],o&&(n=a,!1===o())))return;if(7===u)return t}}const cache=new Map;function resolveWithKeyValue(e,t){return isObject(e)?e[t]:null}function resolveValue(e,t){if(!isObject(e))return null;let a=cache.get(t);if(a||(a=parse(t),a&&cache.set(t,a)),!a)return null;const r=a.length;let n=e,s=0;for(;s<r;){const e=a[s];if(AST_NODE_PROPS_KEYS.includes(e)&&isMessageAST(n))return null;const t=n[e];if(void 0===t)return null;if(isFunction(n))return null;n=t,s++}return n}const DEFAULT_MODIFIER=e=>e,DEFAULT_MESSAGE=e=>"",DEFAULT_MESSAGE_DATA_TYPE="text",DEFAULT_NORMALIZE=e=>0===e.length?"":join(e),DEFAULT_INTERPOLATE=toDisplayString;function pluralDefault(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function getPluralIndex(e){const t=isNumber(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(isNumber(e.named.count)||isNumber(e.named.n))?isNumber(e.named.count)?e.named.count:isNumber(e.named.n)?e.named.n:t:t}function normalizeNamed(e,t){t.count||(t.count=e),t.n||(t.n=e)}function createMessageContext(e={}){const t=e.locale,a=getPluralIndex(e),r=isObject(e.pluralRules)&&isString(t)&&isFunction(e.pluralRules[t])?e.pluralRules[t]:pluralDefault,n=isObject(e.pluralRules)&&isString(t)&&isFunction(e.pluralRules[t])?pluralDefault:void 0,s=e.list||[],l=e.named||create();isNumber(e.pluralIndex)&&normalizeNamed(a,l);function o(t){const a=isFunction(e.messages)?e.messages(t):!!isObject(e.messages)&&e.messages[t];return a||(e.parent?e.parent.message(t):DEFAULT_MESSAGE)}const i=isPlainObject(e.processor)&&isFunction(e.processor.normalize)?e.processor.normalize:DEFAULT_NORMALIZE,c=isPlainObject(e.processor)&&isFunction(e.processor.interpolate)?e.processor.interpolate:DEFAULT_INTERPOLATE,u={list:e=>s[e],named:e=>l[e],plural:e=>e[r(a,e.length,n)],linked:(t,...a)=>{const[r,n]=a;let s="text",l="";1===a.length?isObject(r)?(l=r.modifier||l,s=r.type||s):isString(r)&&(l=r||l):2===a.length&&(isString(r)&&(l=r||l),isString(n)&&(s=n||s));const i=o(t)(u),c="vnode"===s&&isArray(i)&&l?i[0]:i;return l?(m=l,e.modifiers?e.modifiers[m]:DEFAULT_MODIFIER)(c,s):c;var m},message:o,type:isPlainObject(e.processor)&&isString(e.processor.type)?e.processor.type:DEFAULT_MESSAGE_DATA_TYPE,interpolate:c,normalize:i,values:assign(create(),s,l)};return u}const code$1=CompileErrorCodes.__EXTEND_POINT__,inc$1=incrementer(code$1),CoreErrorCodes={INVALID_ARGUMENT:code$1,INVALID_DATE_ARGUMENT:inc$1(),INVALID_ISO_DATE_ARGUMENT:inc$1(),NOT_SUPPORT_NON_STRING_MESSAGE:inc$1(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:inc$1(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:inc$1(),NOT_SUPPORT_LOCALE_TYPE:inc$1(),__EXTEND_POINT__:inc$1()};function getLocale(e,t){return null!=t.locale?resolveLocale(t.locale):resolveLocale(e.locale)}let _resolveLocale;function resolveLocale(e){if(isString(e))return e;if(isFunction(e)){if(e.resolvedOnce&&null!=_resolveLocale)return _resolveLocale;if("Function"===e.constructor.name){const t=e();if(isPromise(t))throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return _resolveLocale=t}throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE)}function fallbackWithSimple(e,t,a){return[...new Set([a,...isArray(t)?t:isObject(t)?Object.keys(t):isString(t)?[t]:[a]])]}function fallbackWithLocaleChain(e,t,a){const r=isString(a)?a:DEFAULT_LOCALE,n=e;n.__localeChainCache||(n.__localeChainCache=new Map);let s=n.__localeChainCache.get(r);if(!s){s=[];let e=[a];for(;isArray(e);)e=appendBlockToChain(s,e,t);const l=isArray(t)||!isPlainObject(t)?t:t.default?t.default:null;e=isString(l)?[l]:l,isArray(e)&&appendBlockToChain(s,e,!1),n.__localeChainCache.set(r,s)}return s}function appendBlockToChain(e,t,a){let r=!0;for(let n=0;n<t.length&&isBoolean(r);n++){const s=t[n];isString(s)&&(r=appendLocaleToChain(e,t[n],a))}return r}function appendLocaleToChain(e,t,a){let r;const n=t.split("-");do{r=appendItemToChain(e,n.join("-"),a),n.splice(-1,1)}while(n.length&&!0===r);return r}function appendItemToChain(e,t,a){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const n=t.replace(/!/g,"");e.push(n),(isArray(a)||isPlainObject(a))&&a[n]&&(r=a[n])}return r}const VERSION$1="9.14.5",NOT_REOSLVED=-1,DEFAULT_LOCALE="en-US",MISSING_RESOLVE_VALUE="",capitalize=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function getDefaultLinkedModifiers(){return{upper:(e,t)=>"text"===t&&isString(e)?e.toUpperCase():"vnode"===t&&isObject(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&isString(e)?e.toLowerCase():"vnode"===t&&isObject(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&isString(e)?capitalize(e):"vnode"===t&&isObject(e)&&"__v_isVNode"in e?capitalize(e.children):e}}let _compiler,_resolver,_fallbacker;function registerMessageResolver(e){_resolver=e}function registerLocaleFallbacker(e){_fallbacker=e}const setAdditionalMeta=e=>{};let _fallbackContext=null;const setFallbackContext=e=>{_fallbackContext=e},getFallbackContext=()=>_fallbackContext;let _cid=0;function createCoreContext(e={}){const t=isFunction(e.onWarn)?e.onWarn:warn,a=isString(e.version)?e.version:VERSION$1,r=isString(e.locale)||isFunction(e.locale)?e.locale:DEFAULT_LOCALE,n=isFunction(r)?DEFAULT_LOCALE:r,s=isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||isString(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:n,l=isPlainObject(e.messages)?e.messages:createResources(n),o=isPlainObject(e.datetimeFormats)?e.datetimeFormats:createResources(n),i=isPlainObject(e.numberFormats)?e.numberFormats:createResources(n),c=assign(create(),e.modifiers,getDefaultLinkedModifiers()),u=e.pluralRules||create(),m=isFunction(e.missing)?e.missing:null,g=!isBoolean(e.missingWarn)&&!isRegExp(e.missingWarn)||e.missingWarn,_=!isBoolean(e.fallbackWarn)&&!isRegExp(e.fallbackWarn)||e.fallbackWarn,f=!!e.fallbackFormat,p=!!e.unresolving,b=isFunction(e.postTranslation)?e.postTranslation:null,E=isPlainObject(e.processor)?e.processor:null,d=!isBoolean(e.warnHtmlMessage)||e.warnHtmlMessage,O=!!e.escapeParameter,v=isFunction(e.messageCompiler)?e.messageCompiler:_compiler,T=isFunction(e.messageResolver)?e.messageResolver:_resolver||resolveWithKeyValue,S=isFunction(e.localeFallbacker)?e.localeFallbacker:_fallbacker||fallbackWithSimple,N=isObject(e.fallbackContext)?e.fallbackContext:void 0,I=e,F=isObject(I.__datetimeFormatters)?I.__datetimeFormatters:new Map,L=isObject(I.__numberFormatters)?I.__numberFormatters:new Map,h=isObject(I.__meta)?I.__meta:{};_cid++;const P={version:a,cid:_cid,locale:r,fallbackLocale:s,messages:l,modifiers:c,pluralRules:u,missing:m,missingWarn:g,fallbackWarn:_,fallbackFormat:f,unresolving:p,postTranslation:b,processor:E,warnHtmlMessage:d,escapeParameter:O,messageCompiler:v,messageResolver:T,localeFallbacker:S,fallbackContext:N,onWarn:t,__meta:h};return P.datetimeFormats=o,P.numberFormats=i,P.__datetimeFormatters=F,P.__numberFormatters=L,P}const createResources=e=>({[e]:create()});function handleMissing(e,t,a,r,n){const{missing:s,onWarn:l}=e;if(null!==s){const r=s(e,a,t,n);return isString(r)?r:t}return t}function updateFallbackLocale(e,t,a){e.__localeChainCache=new Map,e.localeFallbacker(e,a,t)}function isAlmostSameLocale(e,t){return e!==t&&e.split("-")[0]===t.split("-")[0]}function isImplicitFallback(e,t){const a=t.indexOf(e);if(-1===a)return!1;for(let r=a+1;r<t.length;r++)if(isAlmostSameLocale(e,t[r]))return!0;return!1}const NOOP_MESSAGE_FUNCTION=()=>"",isMessageFunction=e=>isFunction(e);function translate(e,...t){const{fallbackFormat:a,postTranslation:r,unresolving:n,messageCompiler:s,fallbackLocale:l,messages:o}=e,[i,c]=parseTranslateArgs(...t),u=isBoolean(c.missingWarn)?c.missingWarn:e.missingWarn,m=isBoolean(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,g=isBoolean(c.escapeParameter)?c.escapeParameter:e.escapeParameter,_=!!c.resolvedMessage,f=isString(c.default)||isBoolean(c.default)?isBoolean(c.default)?s?i:()=>i:c.default:a?s?i:()=>i:"",p=a||""!==f,b=getLocale(e,c);g&&escapeParams(c);let[E,d,O]=_?[i,b,o[b]||create()]:resolveMessageFormat(e,i,b,l,m,u),v=E,T=i;if(_||isString(v)||isMessageAST(v)||isMessageFunction(v)||p&&(v=f,T=v),!(_||(isString(v)||isMessageAST(v)||isMessageFunction(v))&&isString(d)))return n?NOT_REOSLVED:i;let S=!1;const N=isMessageFunction(v)?v:compileMessageFormat(e,i,d,v,T,(()=>{S=!0}));if(S)return v;const I=evaluateMessage(e,N,createMessageContext(getMessageContextOptions(e,d,O,c)));let F=r?r(I,i):I;return g&&isString(F)&&(F=sanitizeTranslatedHtml(F)),F}function escapeParams(e){isArray(e.list)?e.list=e.list.map((e=>isString(e)?escapeHtml(e):e)):isObject(e.named)&&Object.keys(e.named).forEach((t=>{isString(e.named[t])&&(e.named[t]=escapeHtml(e.named[t]))}))}function resolveMessageFormat(e,t,a,r,n,s){const{messages:l,onWarn:o,messageResolver:i,localeFallbacker:c}=e,u=c(e,r,a);let m,g=create(),_=null;for(let f=0;f<u.length&&(m=u[f],g=l[m]||create(),null===(_=i(g,t))&&(_=g[t]),!(isString(_)||isMessageAST(_)||isMessageFunction(_)));f++)if(!isImplicitFallback(m,u)){const a=handleMissing(e,t,m,s,"translate");a!==t&&(_=a)}return[_,m,g]}function compileMessageFormat(e,t,a,r,n,s){const{messageCompiler:l,warnHtmlMessage:o}=e;if(isMessageFunction(r)){const e=r;return e.locale=e.locale||a,e.key=e.key||t,e}if(null==l){const e=()=>r;return e.locale=a,e.key=t,e}const i=l(r,getCompileContext(e,a,n,r,o,s));return i.locale=a,i.key=t,i.source=r,i}function evaluateMessage(e,t,a){return t(a)}function parseTranslateArgs(...e){const[t,a,r]=e,n=create();if(!(isString(t)||isNumber(t)||isMessageFunction(t)||isMessageAST(t)))throw Error(CoreErrorCodes.INVALID_ARGUMENT);const s=isNumber(t)?String(t):(isMessageFunction(t),t);return isNumber(a)?n.plural=a:isString(a)?n.default=a:isPlainObject(a)&&!isEmptyObject(a)?n.named=a:isArray(a)&&(n.list=a),isNumber(r)?n.plural=r:isString(r)?n.default=r:isPlainObject(r)&&assign(n,r),[s,n]}function getCompileContext(e,t,a,r,n,s){return{locale:t,key:a,warnHtmlMessage:n,onError:e=>{throw s&&s(e),e},onCacheKey:e=>generateFormatCacheKey(t,a,e)}}function getMessageContextOptions(e,t,a,r){const{modifiers:n,pluralRules:s,messageResolver:l,fallbackLocale:o,fallbackWarn:i,missingWarn:c,fallbackContext:u}=e,m={locale:t,modifiers:n,pluralRules:s,messages:r=>{let n=l(a,r);if(null==n&&u){const[,,e]=resolveMessageFormat(u,r,t,o,i,c);n=l(e,r)}if(isString(n)||isMessageAST(n)){let a=!1;const s=compileMessageFormat(e,r,t,n,r,(()=>{a=!0}));return a?NOOP_MESSAGE_FUNCTION:s}return isMessageFunction(n)?n:NOOP_MESSAGE_FUNCTION}};return e.processor&&(m.processor=e.processor),r.list&&(m.list=r.list),r.named&&(m.named=r.named),isNumber(r.plural)&&(m.pluralIndex=r.plural),m}function datetime(e,...t){const{datetimeFormats:a,unresolving:r,fallbackLocale:n,onWarn:s,localeFallbacker:l}=e,{__datetimeFormatters:o}=e,[i,c,u,m]=parseDateTimeArgs(...t),g=isBoolean(u.missingWarn)?u.missingWarn:e.missingWarn;isBoolean(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const _=!!u.part,f=getLocale(e,u),p=l(e,n,f);if(!isString(i)||""===i)return new Intl.DateTimeFormat(f,m).format(c);let b,E={},d=null;for(let T=0;T<p.length&&(b=p[T],E=a[b]||{},d=E[i],!isPlainObject(d));T++)handleMissing(e,i,b,g,"datetime format");if(!isPlainObject(d)||!isString(b))return r?NOT_REOSLVED:i;let O=`${b}__${i}`;isEmptyObject(m)||(O=`${O}__${JSON.stringify(m)}`);let v=o.get(O);return v||(v=new Intl.DateTimeFormat(b,assign({},d,m)),o.set(O,v)),_?v.formatToParts(c):v.format(c)}const DATETIME_FORMAT_OPTIONS_KEYS=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function parseDateTimeArgs(...e){const[t,a,r,n]=e,s=create();let l,o=create();if(isString(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);const a=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();l=new Date(a);try{l.toISOString()}catch(i){throw Error(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT)}}else if(isDate(t)){if(isNaN(t.getTime()))throw Error(CoreErrorCodes.INVALID_DATE_ARGUMENT);l=t}else{if(!isNumber(t))throw Error(CoreErrorCodes.INVALID_ARGUMENT);l=t}return isString(a)?s.key=a:isPlainObject(a)&&Object.keys(a).forEach((e=>{DATETIME_FORMAT_OPTIONS_KEYS.includes(e)?o[e]=a[e]:s[e]=a[e]})),isString(r)?s.locale=r:isPlainObject(r)&&(o=r),isPlainObject(n)&&(o=n),[s.key||"",l,s,o]}function clearDateTimeFormat(e,t,a){const r=e;for(const n in a){const e=`${t}__${n}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}}function number(e,...t){const{numberFormats:a,unresolving:r,fallbackLocale:n,onWarn:s,localeFallbacker:l}=e,{__numberFormatters:o}=e,[i,c,u,m]=parseNumberArgs(...t),g=isBoolean(u.missingWarn)?u.missingWarn:e.missingWarn;isBoolean(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const _=!!u.part,f=getLocale(e,u),p=l(e,n,f);if(!isString(i)||""===i)return new Intl.NumberFormat(f,m).format(c);let b,E={},d=null;for(let T=0;T<p.length&&(b=p[T],E=a[b]||{},d=E[i],!isPlainObject(d));T++)handleMissing(e,i,b,g,"number format");if(!isPlainObject(d)||!isString(b))return r?NOT_REOSLVED:i;let O=`${b}__${i}`;isEmptyObject(m)||(O=`${O}__${JSON.stringify(m)}`);let v=o.get(O);return v||(v=new Intl.NumberFormat(b,assign({},d,m)),o.set(O,v)),_?v.formatToParts(c):v.format(c)}const NUMBER_FORMAT_OPTIONS_KEYS=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function parseNumberArgs(...e){const[t,a,r,n]=e,s=create();let l=create();if(!isNumber(t))throw Error(CoreErrorCodes.INVALID_ARGUMENT);const o=t;return isString(a)?s.key=a:isPlainObject(a)&&Object.keys(a).forEach((e=>{NUMBER_FORMAT_OPTIONS_KEYS.includes(e)?l[e]=a[e]:s[e]=a[e]})),isString(r)?s.locale=r:isPlainObject(r)&&(l=r),isPlainObject(n)&&(l=n),[s.key||"",o,s,l]}function clearNumberFormat(e,t,a){const r=e;for(const n in a){const e=`${t}__${n}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}}const VERSION="9.14.5",code=CoreErrorCodes.__EXTEND_POINT__,inc=incrementer(code),I18nErrorCodes={UNEXPECTED_RETURN_TYPE:code,INVALID_ARGUMENT:inc(),MUST_BE_CALL_SETUP_TOP:inc(),NOT_INSTALLED:inc(),NOT_AVAILABLE_IN_LEGACY_MODE:inc(),REQUIRED_VALUE:inc(),INVALID_VALUE:inc(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:inc(),NOT_INSTALLED_WITH_PROVIDE:inc(),UNEXPECTED_ERROR:inc(),NOT_COMPATIBLE_LEGACY_VUE_I18N:inc(),BRIDGE_SUPPORT_VUE_2_ONLY:inc(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:inc(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:inc(),__EXTEND_POINT__:inc()};function createI18nError(e,...t){return createCompileError(e,null,void 0)}const TranslateVNodeSymbol=makeSymbol("__translateVNode"),DatetimePartsSymbol=makeSymbol("__datetimeParts"),NumberPartsSymbol=makeSymbol("__numberParts"),SetPluralRulesSymbol=makeSymbol("__setPluralRules"),InejctWithOptionSymbol=makeSymbol("__injectWithOption"),DisposeSymbol=makeSymbol("__dispose"),__VUE_I18N_BRIDGE__="__VUE_I18N_BRIDGE__";function handleFlatJson(e){if(!isObject(e))return e;if(isMessageAST(e))return e;for(const t in e)if(hasOwn(e,t))if(t.includes(".")){const a=t.split("."),r=a.length-1;let n=e,s=!1;for(let e=0;e<r;e++){if("__proto__"===a[e])throw new Error(`unsafe key: ${a[e]}`);if(a[e]in n||(n[a[e]]=create()),!isObject(n[a[e]])){s=!0;break}n=n[a[e]]}if(s||(isMessageAST(n)?AST_NODE_PROPS_KEYS.includes(a[r])||delete e[t]:(n[a[r]]=e[t],delete e[t])),!isMessageAST(n)){const e=n[a[r]];isObject(e)&&handleFlatJson(e)}}else isObject(e[t])&&handleFlatJson(e[t]);return e}function getLocaleMessages(e,t){const{messages:a,__i18n:r,messageResolver:n,flatJson:s}=t,l=isPlainObject(a)?a:isArray(r)?create():{[e]:create()};if(isArray(r)&&r.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:a}=e;t?(l[t]=l[t]||create(),deepCopy(a,l[t])):deepCopy(a,l)}else isString(e)&&deepCopy(JSON.parse(e),l)})),null==n&&s)for(const o in l)hasOwn(l,o)&&handleFlatJson(l[o]);return l}function getComponentOptions(e){return e.type}function adjustI18nResources(e,t,a){let r=isObject(t.messages)?t.messages:create();"__i18nGlobal"in a&&(r=getLocaleMessages(e.locale.value,{messages:r,__i18n:a.__i18nGlobal}));const n=Object.keys(r);if(n.length&&n.forEach((t=>{e.mergeLocaleMessage(t,r[t])})),isObject(t.datetimeFormats)){const a=Object.keys(t.datetimeFormats);a.length&&a.forEach((a=>{e.mergeDateTimeFormat(a,t.datetimeFormats[a])}))}if(isObject(t.numberFormats)){const a=Object.keys(t.numberFormats);a.length&&a.forEach((a=>{e.mergeNumberFormat(a,t.numberFormats[a])}))}}function createTextNode(e){return createVNode(Text,null,e,0)}const DEVTOOLS_META="__INTLIFY_META__",NOOP_RETURN_ARRAY=()=>[],NOOP_RETURN_FALSE=()=>!1;let composerID=0;function defineCoreMissingHandler(e){return(t,a,r,n)=>e(a,r,getCurrentInstance()||void 0,n)}const getMetaInfo=()=>{const e=getCurrentInstance();let t=null;return e&&(t=getComponentOptions(e)[DEVTOOLS_META])?{[DEVTOOLS_META]:t}:null};function createComposer(e={},t){const{__root:a,__injectWithOption:r}=e,n=void 0===a,s=e.flatJson,l=inBrowser?ref:shallowRef,o=!!e.translateExistCompatible;let i=!isBoolean(e.inheritLocale)||e.inheritLocale;const c=l(a&&i?a.locale.value:isString(e.locale)?e.locale:DEFAULT_LOCALE),u=l(a&&i?a.fallbackLocale.value:isString(e.fallbackLocale)||isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:c.value),m=l(getLocaleMessages(c.value,e)),g=l(isPlainObject(e.datetimeFormats)?e.datetimeFormats:{[c.value]:{}}),_=l(isPlainObject(e.numberFormats)?e.numberFormats:{[c.value]:{}});let f=a?a.missingWarn:!isBoolean(e.missingWarn)&&!isRegExp(e.missingWarn)||e.missingWarn,p=a?a.fallbackWarn:!isBoolean(e.fallbackWarn)&&!isRegExp(e.fallbackWarn)||e.fallbackWarn,b=a?a.fallbackRoot:!isBoolean(e.fallbackRoot)||e.fallbackRoot,E=!!e.fallbackFormat,d=isFunction(e.missing)?e.missing:null,O=isFunction(e.missing)?defineCoreMissingHandler(e.missing):null,v=isFunction(e.postTranslation)?e.postTranslation:null,T=a?a.warnHtmlMessage:!isBoolean(e.warnHtmlMessage)||e.warnHtmlMessage,S=!!e.escapeParameter;const N=a?a.modifiers:isPlainObject(e.modifiers)?e.modifiers:{};let I,F=e.pluralRules||a&&a.pluralRules;I=(()=>{n&&setFallbackContext(null);const t={version:VERSION,locale:c.value,fallbackLocale:u.value,messages:m.value,modifiers:N,pluralRules:F,missing:null===O?void 0:O,missingWarn:f,fallbackWarn:p,fallbackFormat:E,unresolving:!0,postTranslation:null===v?void 0:v,warnHtmlMessage:T,escapeParameter:S,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=g.value,t.numberFormats=_.value,t.__datetimeFormatters=isPlainObject(I)?I.__datetimeFormatters:void 0,t.__numberFormatters=isPlainObject(I)?I.__numberFormatters:void 0;const a=createCoreContext(t);return n&&setFallbackContext(a),a})(),updateFallbackLocale(I,c.value,u.value);const L=computed({get:()=>c.value,set:e=>{c.value=e,I.locale=c.value}}),h=computed({get:()=>u.value,set:e=>{u.value=e,I.fallbackLocale=u.value,updateFallbackLocale(I,c.value,e)}}),P=computed((()=>m.value)),y=computed((()=>g.value)),C=computed((()=>_.value));const A=(e,t,r,s,l,o)=>{let i;c.value,u.value,m.value,g.value,_.value;try{0,n||(I.fallbackContext=a?getFallbackContext():void 0),i=e(I)}finally{n||(I.fallbackContext=void 0)}if("translate exists"!==r&&isNumber(i)&&i===NOT_REOSLVED||"translate exists"===r&&!i){const[e,r]=t();return a&&b?s(a):l(e)}if(o(i))return i;throw Error(I18nErrorCodes.UNEXPECTED_RETURN_TYPE)};function R(...e){return A((t=>Reflect.apply(translate,null,[t,...e])),(()=>parseTranslateArgs(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>isString(e)))}const k={normalize:function(e){return e.map((e=>isString(e)||isNumber(e)||isBoolean(e)?createTextNode(String(e)):e))},interpolate:e=>e,type:"vnode"};function M(e){return m.value[e]||{}}composerID++,a&&inBrowser&&(watch(a.locale,(e=>{i&&(c.value=e,I.locale=e,updateFallbackLocale(I,c.value,u.value))})),watch(a.fallbackLocale,(e=>{i&&(u.value=e,I.fallbackLocale=e,updateFallbackLocale(I,c.value,u.value))})));const D={id:composerID,locale:L,fallbackLocale:h,get inheritLocale(){return i},set inheritLocale(e){i=e,e&&a&&(c.value=a.locale.value,u.value=a.fallbackLocale.value,updateFallbackLocale(I,c.value,u.value))},get availableLocales(){return Object.keys(m.value).sort()},messages:P,get modifiers(){return N},get pluralRules(){return F||{}},get isGlobal(){return n},get missingWarn(){return f},set missingWarn(e){f=e,I.missingWarn=f},get fallbackWarn(){return p},set fallbackWarn(e){p=e,I.fallbackWarn=p},get fallbackRoot(){return b},set fallbackRoot(e){b=e},get fallbackFormat(){return E},set fallbackFormat(e){E=e,I.fallbackFormat=E},get warnHtmlMessage(){return T},set warnHtmlMessage(e){T=e,I.warnHtmlMessage=e},get escapeParameter(){return S},set escapeParameter(e){S=e,I.escapeParameter=e},t:R,getLocaleMessage:M,setLocaleMessage:function(e,t){if(s){const a={[e]:t};for(const e in a)hasOwn(a,e)&&handleFlatJson(a[e]);t=a[e]}m.value[e]=t,I.messages=m.value},mergeLocaleMessage:function(e,t){m.value[e]=m.value[e]||{};const a={[e]:t};if(s)for(const r in a)hasOwn(a,r)&&handleFlatJson(a[r]);deepCopy(t=a[e],m.value[e]),I.messages=m.value},getPostTranslationHandler:function(){return isFunction(v)?v:null},setPostTranslationHandler:function(e){v=e,I.postTranslation=e},getMissingHandler:function(){return d},setMissingHandler:function(e){null!==e&&(O=defineCoreMissingHandler(e)),d=e,I.missing=O},[SetPluralRulesSymbol]:function(e){F=e,I.pluralRules=F}};return D.datetimeFormats=y,D.numberFormats=C,D.rt=function(...e){const[t,a,r]=e;if(r&&!isObject(r))throw Error(I18nErrorCodes.INVALID_ARGUMENT);return R(t,a,assign({resolvedMessage:!0},r||{}))},D.te=function(e,t){return A((()=>{if(!e)return!1;const a=M(isString(t)?t:c.value),r=I.messageResolver(a,e);return o?null!=r:isMessageAST(r)||isMessageFunction(r)||isString(r)}),(()=>[e]),"translate exists",(a=>Reflect.apply(a.te,a,[e,t])),NOOP_RETURN_FALSE,(e=>isBoolean(e)))},D.tm=function(e){const t=function(e){let t=null;const a=fallbackWithLocaleChain(I,u.value,c.value);for(let r=0;r<a.length;r++){const n=m.value[a[r]]||{},s=I.messageResolver(n,e);if(null!=s){t=s;break}}return t}(e);return null!=t?t:a&&a.tm(e)||{}},D.d=function(...e){return A((t=>Reflect.apply(datetime,null,[t,...e])),(()=>parseDateTimeArgs(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>MISSING_RESOLVE_VALUE),(e=>isString(e)))},D.n=function(...e){return A((t=>Reflect.apply(number,null,[t,...e])),(()=>parseNumberArgs(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>MISSING_RESOLVE_VALUE),(e=>isString(e)))},D.getDateTimeFormat=function(e){return g.value[e]||{}},D.setDateTimeFormat=function(e,t){g.value[e]=t,I.datetimeFormats=g.value,clearDateTimeFormat(I,e,t)},D.mergeDateTimeFormat=function(e,t){g.value[e]=assign(g.value[e]||{},t),I.datetimeFormats=g.value,clearDateTimeFormat(I,e,t)},D.getNumberFormat=function(e){return _.value[e]||{}},D.setNumberFormat=function(e,t){_.value[e]=t,I.numberFormats=_.value,clearNumberFormat(I,e,t)},D.mergeNumberFormat=function(e,t){_.value[e]=assign(_.value[e]||{},t),I.numberFormats=_.value,clearNumberFormat(I,e,t)},D[InejctWithOptionSymbol]=r,D[TranslateVNodeSymbol]=function(...e){return A((t=>{let a;const r=t;try{r.processor=k,a=Reflect.apply(translate,null,[r,...e])}finally{r.processor=null}return a}),(()=>parseTranslateArgs(...e)),"translate",(t=>t[TranslateVNodeSymbol](...e)),(e=>[createTextNode(e)]),(e=>isArray(e)))},D[DatetimePartsSymbol]=function(...e){return A((t=>Reflect.apply(datetime,null,[t,...e])),(()=>parseDateTimeArgs(...e)),"datetime format",(t=>t[DatetimePartsSymbol](...e)),NOOP_RETURN_ARRAY,(e=>isString(e)||isArray(e)))},D[NumberPartsSymbol]=function(...e){return A((t=>Reflect.apply(number,null,[t,...e])),(()=>parseNumberArgs(...e)),"number format",(t=>t[NumberPartsSymbol](...e)),NOOP_RETURN_ARRAY,(e=>isString(e)||isArray(e)))},D}function convertComposerOptions(e){const t=isString(e.locale)?e.locale:DEFAULT_LOCALE,a=isString(e.fallbackLocale)||isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,r=isFunction(e.missing)?e.missing:void 0,n=!isBoolean(e.silentTranslationWarn)&&!isRegExp(e.silentTranslationWarn)||!e.silentTranslationWarn,s=!isBoolean(e.silentFallbackWarn)&&!isRegExp(e.silentFallbackWarn)||!e.silentFallbackWarn,l=!isBoolean(e.fallbackRoot)||e.fallbackRoot,o=!!e.formatFallbackMessages,i=isPlainObject(e.modifiers)?e.modifiers:{},c=e.pluralizationRules,u=isFunction(e.postTranslation)?e.postTranslation:void 0,m=!isString(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,g=!!e.escapeParameterHtml,_=!isBoolean(e.sync)||e.sync;let f=e.messages;if(isPlainObject(e.sharedMessages)){const t=e.sharedMessages;f=Object.keys(t).reduce(((e,a)=>{const r=e[a]||(e[a]={});return assign(r,t[a]),e}),f||{})}const{__i18n:p,__root:b,__injectWithOption:E}=e,d=e.datetimeFormats,O=e.numberFormats,v=e.flatJson,T=e.translateExistCompatible;return{locale:t,fallbackLocale:a,messages:f,flatJson:v,datetimeFormats:d,numberFormats:O,missing:r,missingWarn:n,fallbackWarn:s,fallbackRoot:l,fallbackFormat:o,modifiers:i,pluralRules:c,postTranslation:u,warnHtmlMessage:m,escapeParameter:g,messageResolver:e.messageResolver,inheritLocale:_,translateExistCompatible:T,__i18n:p,__root:b,__injectWithOption:E}}function createVueI18n(e={},t){{const t=createComposer(convertComposerOptions(e)),{__extender:a}=e,r={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate:()=>[]}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return isBoolean(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=isBoolean(e)?!e:e},get silentFallbackWarn(){return isBoolean(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=isBoolean(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[a,r,n]=e,s={};let l=null,o=null;if(!isString(a))throw Error(I18nErrorCodes.INVALID_ARGUMENT);const i=a;return isString(r)?s.locale=r:isArray(r)?l=r:isPlainObject(r)&&(o=r),isArray(n)?l=n:isPlainObject(n)&&(o=n),Reflect.apply(t.t,t,[i,l||o||{},s])},rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[a,r,n]=e,s={plural:1};let l=null,o=null;if(!isString(a))throw Error(I18nErrorCodes.INVALID_ARGUMENT);const i=a;return isString(r)?s.locale=r:isNumber(r)?s.plural=r:isArray(r)?l=r:isPlainObject(r)&&(o=r),isString(n)?s.locale=n:isArray(n)?l=n:isPlainObject(n)&&(o=n),Reflect.apply(t.t,t,[i,l||o||{},s])},te:(e,a)=>t.te(e,a),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,a){t.setLocaleMessage(e,a)},mergeLocaleMessage(e,a){t.mergeLocaleMessage(e,a)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,a){t.setDateTimeFormat(e,a)},mergeDateTimeFormat(e,a){t.mergeDateTimeFormat(e,a)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,a){t.setNumberFormat(e,a)},mergeNumberFormat(e,a){t.mergeNumberFormat(e,a)},getChoiceIndex:(e,t)=>-1};return r.__extender=a,r}}const baseFormatProps={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function getInterpolateArg({slots:e},t){if(1===t.length&&"default"===t[0]){return(e.default?e.default():[]).reduce(((e,t)=>[...e,...t.type===Fragment?t.children:[t]]),[])}return t.reduce(((t,a)=>{const r=e[a];return r&&(t[a]=r()),t}),create())}function getFragmentableTag(e){return Fragment}const TranslationImpl=defineComponent({name:"i18n-t",props:assign({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>isNumber(e)||!isNaN(e)}},baseFormatProps),setup(e,t){const{slots:a,attrs:r}=t,n=e.i18n||useI18n({useScope:e.scope,__useComponent:!0});return()=>{const s=Object.keys(a).filter((e=>"_"!==e)),l=create();e.locale&&(l.locale=e.locale),void 0!==e.plural&&(l.plural=isString(e.plural)?+e.plural:e.plural);const o=getInterpolateArg(t,s),i=n[TranslateVNodeSymbol](e.keypath,o,l),c=assign(create(),r),u=isString(e.tag)||isObject(e.tag)?e.tag:getFragmentableTag();return h(u,c,i)}}}),Translation=TranslationImpl,I18nT=Translation;function isVNode(e){return isArray(e)&&!isString(e[0])}function renderFormatter(e,t,a,r){const{slots:n,attrs:s}=t;return()=>{const t={part:!0};let l=create();e.locale&&(t.locale=e.locale),isString(e.format)?t.key=e.format:isObject(e.format)&&(isString(e.format.key)&&(t.key=e.format.key),l=Object.keys(e.format).reduce(((t,r)=>a.includes(r)?assign(create(),t,{[r]:e.format[r]}):t),create()));const o=r(e.value,t,l);let i=[t.key];isArray(o)?i=o.map(((e,t)=>{const a=n[e.type],r=a?a({[e.type]:e.value,index:t,parts:o}):[e.value];return isVNode(r)&&(r[0].key=`${e.type}-${t}`),r})):isString(o)&&(i=[o]);const c=assign(create(),s),u=isString(e.tag)||isObject(e.tag)?e.tag:getFragmentableTag();return h(u,c,i)}}const NumberFormatImpl=defineComponent({name:"i18n-n",props:assign({value:{type:Number,required:!0},format:{type:[String,Object]}},baseFormatProps),setup(e,t){const a=e.i18n||useI18n({useScope:e.scope,__useComponent:!0});return renderFormatter(e,t,NUMBER_FORMAT_OPTIONS_KEYS,((...e)=>a[NumberPartsSymbol](...e)))}}),NumberFormat=NumberFormatImpl,I18nN=NumberFormat,DatetimeFormatImpl=defineComponent({name:"i18n-d",props:assign({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},baseFormatProps),setup(e,t){const a=e.i18n||useI18n({useScope:e.scope,__useComponent:!0});return renderFormatter(e,t,DATETIME_FORMAT_OPTIONS_KEYS,((...e)=>a[DatetimePartsSymbol](...e)))}}),DatetimeFormat=DatetimeFormatImpl,I18nD=DatetimeFormat;function getComposer$1(e,t){const a=e;if("composition"===e.mode)return a.__getInstance(t)||e.global;{const r=a.__getInstance(t);return null!=r?r.__composer:e.global.__composer}}function vTDirective(e){const t=t=>{const{instance:a,modifiers:r,value:n}=t;if(!a||!a.$)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const s=getComposer$1(e,a.$),l=parseValue(n);return[Reflect.apply(s.t,s,[...makeParams(l)]),s]};return{created:(a,r)=>{const[n,s]=t(r);inBrowser&&e.global===s&&(a.__i18nWatcher=watch(s.locale,(()=>{r.instance&&r.instance.$forceUpdate()}))),a.__composer=s,a.textContent=n},unmounted:e=>{inBrowser&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const a=e.__composer,r=parseValue(t);e.textContent=Reflect.apply(a.t,a,[...makeParams(r)])}},getSSRProps:e=>{const[a]=t(e);return{textContent:a}}}}function parseValue(e){if(isString(e))return{path:e};if(isPlainObject(e)){if(!("path"in e))throw Error(I18nErrorCodes.REQUIRED_VALUE,"path");return e}throw Error(I18nErrorCodes.INVALID_VALUE)}function makeParams(e){const{path:t,locale:a,args:r,choice:n,plural:s}=e,l={},o=r||{};return isString(a)&&(l.locale=a),isNumber(n)&&(l.plural=n),isNumber(s)&&(l.plural=s),[t,o,l]}function apply(e,t,...a){const r=isPlainObject(a[0])?a[0]:{},n=!!r.useI18nComponentName;(!isBoolean(r.globalInstall)||r.globalInstall)&&([n?"i18n":Translation.name,"I18nT"].forEach((t=>e.component(t,Translation))),[NumberFormat.name,"I18nN"].forEach((t=>e.component(t,NumberFormat))),[DatetimeFormat.name,"I18nD"].forEach((t=>e.component(t,DatetimeFormat)))),e.directive("t",vTDirective(t))}function defineMixin(e,t,a){return{beforeCreate(){const r=getCurrentInstance();if(!r)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const n=this.$options;if(n.i18n){const r=n.i18n;if(n.__i18n&&(r.__i18n=n.__i18n),r.__root=t,this===this.$root)this.$i18n=mergeToGlobal(e,r);else{r.__injectWithOption=!0,r.__extender=a.__vueI18nExtend,this.$i18n=createVueI18n(r);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(n.__i18n)if(this===this.$root)this.$i18n=mergeToGlobal(e,n);else{this.$i18n=createVueI18n({__i18n:n.__i18n,__injectWithOption:!0,__extender:a.__vueI18nExtend,__root:t});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;n.__i18nGlobal&&adjustI18nResources(t,n,n),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),a.__setInstance(r,this.$i18n)},mounted(){},unmounted(){const e=getCurrentInstance();if(!e)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const t=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,t.__disposer&&(t.__disposer(),delete t.__disposer,delete t.__extender),a.__deleteInstance(e),delete this.$i18n}}}function mergeToGlobal(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[SetPluralRulesSymbol](t.pluralizationRules||e.pluralizationRules);const a=getLocaleMessages(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(a).forEach((t=>e.mergeLocaleMessage(t,a[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((a=>e.mergeDateTimeFormat(a,t.datetimeFormats[a]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((a=>e.mergeNumberFormat(a,t.numberFormats[a]))),e}const I18nInjectionKey=makeSymbol("global-vue-i18n");function createI18n(e={},t){const a=!isBoolean(e.legacy)||e.legacy,r=!isBoolean(e.globalInjection)||e.globalInjection,n=!a||!!e.allowComposition,s=new Map,[l,o]=createGlobal(e,a),i=makeSymbol("");{const e={get mode(){return a?"legacy":"composition"},get allowComposition(){return n},async install(t,...n){if(t.__VUE_I18N_SYMBOL__=i,t.provide(t.__VUE_I18N_SYMBOL__,e),isPlainObject(n[0])){const t=n[0];e.__composerExtend=t.__composerExtend,e.__vueI18nExtend=t.__vueI18nExtend}let s=null;!a&&r&&(s=injectGlobalFields(t,e.global)),apply(t,e,...n),a&&t.mixin(defineMixin(o,o.__composer,e));const l=t.unmount;t.unmount=()=>{s&&s(),e.dispose(),l()}},get global(){return o},dispose(){l.stop()},__instances:s,__getInstance:function(e){return s.get(e)||null},__setInstance:function(e,t){s.set(e,t)},__deleteInstance:function(e){s.delete(e)}};return e}}function useI18n(e={}){const t=getCurrentInstance();if(null==t)throw Error(I18nErrorCodes.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&null!=t.appContext.app&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Error(I18nErrorCodes.NOT_INSTALLED);const a=getI18nInstance(t),r=getGlobalComposer(a),n=getComponentOptions(t),s=getScope(e,n);if("legacy"===a.mode&&!e.__useComponent){if(!a.allowComposition)throw Error(I18nErrorCodes.NOT_AVAILABLE_IN_LEGACY_MODE);return useI18nForLegacy(t,s,r,e)}if("global"===s)return adjustI18nResources(r,e,n),r;if("parent"===s){let n=getComposer(a,t,e.__useComponent);return null==n&&(n=r),n}const l=a;let o=l.__getInstance(t);if(null==o){const a=assign({},e);"__i18n"in n&&(a.__i18n=n.__i18n),r&&(a.__root=r),o=createComposer(a),l.__composerExtend&&(o[DisposeSymbol]=l.__composerExtend(o)),setupLifeCycle(l,t,o),l.__setInstance(t,o)}return o}const castToVueI18n=e=>{if(!(__VUE_I18N_BRIDGE__ in e))throw Error(I18nErrorCodes.NOT_COMPATIBLE_LEGACY_VUE_I18N);return e};function createGlobal(e,t,a){const r=effectScope();{const a=t?r.run((()=>createVueI18n(e))):r.run((()=>createComposer(e)));if(null==a)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);return[r,a]}}function getI18nInstance(e){{const t=inject(e.isCE?I18nInjectionKey:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw createI18nError(e.isCE?I18nErrorCodes.NOT_INSTALLED_WITH_PROVIDE:I18nErrorCodes.UNEXPECTED_ERROR);return t}}function getScope(e,t){return isEmptyObject(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function getGlobalComposer(e){return"composition"===e.mode?e.global:e.global.__composer}function getComposer(e,t,a=!1){let r=null;const n=t.root;let s=getParentComponentInstance(t,a);for(;null!=s;){const t=e;if("composition"===e.mode)r=t.__getInstance(s);else{const e=t.__getInstance(s);null!=e&&(r=e.__composer,a&&r&&!r[InejctWithOptionSymbol]&&(r=null))}if(null!=r)break;if(n===s)break;s=s.parent}return r}function getParentComponentInstance(e,t=!1){return null==e?null:t&&e.vnode.ctx||e.parent}function setupLifeCycle(e,t,a){onMounted((()=>{}),t),onUnmounted((()=>{const r=a;e.__deleteInstance(t);const n=r[DisposeSymbol];n&&(n(),delete r[DisposeSymbol])}),t)}function useI18nForLegacy(e,t,a,r={}){const n="local"===t,s=shallowRef(null);if(n&&e.proxy&&!e.proxy.$options.i18n&&!e.proxy.$options.__i18n)throw Error(I18nErrorCodes.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const l=isBoolean(r.inheritLocale)?r.inheritLocale:!isString(r.locale),o=ref(!n||l?a.locale.value:isString(r.locale)?r.locale:DEFAULT_LOCALE),i=ref(!n||l?a.fallbackLocale.value:isString(r.fallbackLocale)||isArray(r.fallbackLocale)||isPlainObject(r.fallbackLocale)||!1===r.fallbackLocale?r.fallbackLocale:o.value),c=ref(getLocaleMessages(o.value,r)),u=ref(isPlainObject(r.datetimeFormats)?r.datetimeFormats:{[o.value]:{}}),m=ref(isPlainObject(r.numberFormats)?r.numberFormats:{[o.value]:{}}),g=n?a.missingWarn:!isBoolean(r.missingWarn)&&!isRegExp(r.missingWarn)||r.missingWarn,_=n?a.fallbackWarn:!isBoolean(r.fallbackWarn)&&!isRegExp(r.fallbackWarn)||r.fallbackWarn,f=n?a.fallbackRoot:!isBoolean(r.fallbackRoot)||r.fallbackRoot,p=!!r.fallbackFormat,b=isFunction(r.missing)?r.missing:null,E=isFunction(r.postTranslation)?r.postTranslation:null,d=n?a.warnHtmlMessage:!isBoolean(r.warnHtmlMessage)||r.warnHtmlMessage,O=!!r.escapeParameter,v=n?a.modifiers:isPlainObject(r.modifiers)?r.modifiers:{},T=r.pluralRules||n&&a.pluralRules;function S(e){return o.value,i.value,c.value,u.value,m.value,e()}const N={get id(){return s.value?s.value.id:-1},locale:computed({get:()=>s.value?s.value.locale.value:o.value,set:e=>{s.value&&(s.value.locale.value=e),o.value=e}}),fallbackLocale:computed({get:()=>s.value?s.value.fallbackLocale.value:i.value,set:e=>{s.value&&(s.value.fallbackLocale.value=e),i.value=e}}),messages:computed((()=>s.value?s.value.messages.value:c.value)),datetimeFormats:computed((()=>u.value)),numberFormats:computed((()=>m.value)),get inheritLocale(){return s.value?s.value.inheritLocale:l},set inheritLocale(e){s.value&&(s.value.inheritLocale=e)},get availableLocales(){return s.value?s.value.availableLocales:Object.keys(c.value)},get modifiers(){return s.value?s.value.modifiers:v},get pluralRules(){return s.value?s.value.pluralRules:T},get isGlobal(){return!!s.value&&s.value.isGlobal},get missingWarn(){return s.value?s.value.missingWarn:g},set missingWarn(e){s.value&&(s.value.missingWarn=e)},get fallbackWarn(){return s.value?s.value.fallbackWarn:_},set fallbackWarn(e){s.value&&(s.value.missingWarn=e)},get fallbackRoot(){return s.value?s.value.fallbackRoot:f},set fallbackRoot(e){s.value&&(s.value.fallbackRoot=e)},get fallbackFormat(){return s.value?s.value.fallbackFormat:p},set fallbackFormat(e){s.value&&(s.value.fallbackFormat=e)},get warnHtmlMessage(){return s.value?s.value.warnHtmlMessage:d},set warnHtmlMessage(e){s.value&&(s.value.warnHtmlMessage=e)},get escapeParameter(){return s.value?s.value.escapeParameter:O},set escapeParameter(e){s.value&&(s.value.escapeParameter=e)},t:function(...e){return s.value?S((()=>Reflect.apply(s.value.t,null,[...e]))):S((()=>""))},getPostTranslationHandler:function(){return s.value?s.value.getPostTranslationHandler():E},setPostTranslationHandler:function(e){s.value&&s.value.setPostTranslationHandler(e)},getMissingHandler:function(){return s.value?s.value.getMissingHandler():b},setMissingHandler:function(e){s.value&&s.value.setMissingHandler(e)},rt:function(...e){return s.value?Reflect.apply(s.value.rt,null,[...e]):""},d:function(...e){return s.value?S((()=>Reflect.apply(s.value.d,null,[...e]))):S((()=>""))},n:function(...e){return s.value?S((()=>Reflect.apply(s.value.n,null,[...e]))):S((()=>""))},tm:function(e){return s.value?s.value.tm(e):{}},te:function(e,t){return!!s.value&&s.value.te(e,t)},getLocaleMessage:function(e){return s.value?s.value.getLocaleMessage(e):{}},setLocaleMessage:function(e,t){s.value&&(s.value.setLocaleMessage(e,t),c.value[e]=t)},mergeLocaleMessage:function(e,t){s.value&&s.value.mergeLocaleMessage(e,t)},getDateTimeFormat:function(e){return s.value?s.value.getDateTimeFormat(e):{}},setDateTimeFormat:function(e,t){s.value&&(s.value.setDateTimeFormat(e,t),u.value[e]=t)},mergeDateTimeFormat:function(e,t){s.value&&s.value.mergeDateTimeFormat(e,t)},getNumberFormat:function(e){return s.value?s.value.getNumberFormat(e):{}},setNumberFormat:function(e,t){s.value&&(s.value.setNumberFormat(e,t),m.value[e]=t)},mergeNumberFormat:function(e,t){s.value&&s.value.mergeNumberFormat(e,t)}};return onBeforeMount((()=>{if(null==e.proxy||null==e.proxy.$i18n)throw Error(I18nErrorCodes.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const a=s.value=e.proxy.$i18n.__composer;"global"===t?(o.value=a.locale.value,i.value=a.fallbackLocale.value,c.value=a.messages.value,u.value=a.datetimeFormats.value,m.value=a.numberFormats.value):n&&function(e){e.locale.value=o.value,e.fallbackLocale.value=i.value,Object.keys(c.value).forEach((t=>{e.mergeLocaleMessage(t,c.value[t])})),Object.keys(u.value).forEach((t=>{e.mergeDateTimeFormat(t,u.value[t])})),Object.keys(m.value).forEach((t=>{e.mergeNumberFormat(t,m.value[t])})),e.escapeParameter=O,e.fallbackFormat=p,e.fallbackRoot=f,e.fallbackWarn=_,e.missingWarn=g,e.warnHtmlMessage=d}(a)})),N}const globalExportProps=["locale","fallbackLocale","availableLocales"],globalExportMethods=["t","rt","d","n","tm","te"];function injectGlobalFields(e,t){const a=Object.create(null);globalExportProps.forEach((e=>{const r=Object.getOwnPropertyDescriptor(t,e);if(!r)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const n=isRef(r.value)?{get:()=>r.value.value,set(e){r.value.value=e}}:{get:()=>r.get&&r.get()};Object.defineProperty(a,e,n)})),e.config.globalProperties.$i18n=a,globalExportMethods.forEach((a=>{const r=Object.getOwnPropertyDescriptor(t,a);if(!r||!r.value)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${a}`,r)}));return()=>{delete e.config.globalProperties.$i18n,globalExportMethods.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))}}registerMessageResolver(resolveValue),registerLocaleFallbacker(fallbackWithLocaleChain);export{DatetimeFormat,I18nD,I18nInjectionKey,I18nN,I18nT,NumberFormat,Translation,VERSION,castToVueI18n,createI18n,useI18n,vTDirective};
