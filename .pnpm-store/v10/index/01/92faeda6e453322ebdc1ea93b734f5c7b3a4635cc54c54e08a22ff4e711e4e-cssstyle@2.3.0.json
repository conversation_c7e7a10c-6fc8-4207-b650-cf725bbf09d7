{"name": "cssstyle", "version": "2.3.0", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1755069205793, "integrity": "sha512-Y3fmiPjRbJr/96iaQNnhMoAC2XsZxdI7meVj563LEmpFQZUFWafQM0/EX1MUjx+sl2Q01q+FonWX7NW/VTuqwA==", "mode": 420, "size": 1050}, "lib/allExtraProperties.js": {"checkedAt": 1755069205793, "integrity": "sha512-WGMhSTmbr0DzUabob3uv238fo6kqT9HhCO3WK2x8wXJdp9sV6XocDxyVp8/Tit9BTJOr/aElaVkfzKeA6ZY8eg==", "mode": 420, "size": 1530}, "lib/allProperties.js": {"checkedAt": 1755069205794, "integrity": "sha512-JwpmnJQ4hJkNbeuGh3a/zSc1KpNzAipmoSYyQf7tr/ZoJHrG/PnS44gcj+s2GyMoegQkE/ro1Fvcht8ZYy87og==", "mode": 420, "size": 9175}, "lib/allWebkitProperties.js": {"checkedAt": 1755069205795, "integrity": "sha512-3CRJDEAwBqw5EiURj9XsKbxMU1of6RZJ2cQ4ks+FGP86jSJ45yp0P7UZbOGZ6cdrgPlfr6SxXxaYijmnZY5TZw==", "mode": 420, "size": 4068}, "lib/properties/azimuth.js": {"checkedAt": 1755069205796, "integrity": "sha512-ftU4lKfd8P9z+EucYfdgXxhBRBLlFcAVUsNLRIitAX0EwCj7PBYFg/B9sqnzuo+hGQlFwlESvRE2Pi17EBRdrA==", "mode": 420, "size": 2096}, "lib/properties/background.js": {"checkedAt": 1755069205797, "integrity": "sha512-oWmTwJD3beYUBtPorS/iJiJQdXRQI/0IXQnZ4Ny/R9fEp+3SuRoHuoj9ob/7NHusbnTD9Xs+zEnXDPZioLfuDQ==", "mode": 420, "size": 623}, "lib/properties/backgroundAttachment.js": {"checkedAt": 1755069205798, "integrity": "sha512-mLkyWu6KnzKOcCb2bHRCzgrjMP9ClYpHKk5taJJ0f9CMqJCHM5sbsAD+d4cgII6wHnDkFxqOOJ9QVkkxpad/pw==", "mode": 420, "size": 568}, "lib/properties/backgroundColor.js": {"checkedAt": 1755069205798, "integrity": "sha512-H9Ao1pUJdDaju8i8EXBoVBspXscTXJS6ZB4QmJGogj2Hzg6k21tO4QdnNCDJT9FavoalHcQczjmz/n9wA11kSg==", "mode": 420, "size": 752}, "lib/properties/backgroundImage.js": {"checkedAt": 1755069205799, "integrity": "sha512-GyKjsj7JNhQqiFzV93XaHnzIqUNDlz1zpELowsBhaziN9CJ7GBouJ0W6Lr4OLCpCbmCdhLPyy4kY2obLg193hg==", "mode": 420, "size": 666}, "lib/properties/backgroundPosition.js": {"checkedAt": 1755069205800, "integrity": "sha512-6thnwYe/TJQM8by5VnoYb0ABwc9wCRKLHUhLof0DbBpH6/f5b2P/hk2zn0PV5V2ODcI3IZ9xwb3fiYZRbSzkkQ==", "mode": 420, "size": 1506}, "lib/properties/backgroundRepeat.js": {"checkedAt": 1755069205802, "integrity": "sha512-jqedj11IYqarMck1DEDur9b2XrKL7dXOxXQxiNoRnnjOIHlML+n5utZSBSgUf9/j8CJo1SRsC5tSNP5Hh8C1fg==", "mode": 420, "size": 708}, "lib/properties/border.js": {"checkedAt": 1755069205802, "integrity": "sha512-TFgm8UWsPDMmqfoyOSl3P4iW/xzAjSyM9flWowIuxI3PIcZDBteFwoFAGLF5WHCPO0v8N7vaFiXvpG71JqUAjQ==", "mode": 420, "size": 1046}, "lib/properties/borderBottom.js": {"checkedAt": 1755069205803, "integrity": "sha512-pBWKVqYkvjKc3sLHEqlwcRwe15TREYJZPMHlzNkrj1cUHJLFI60hdAh75WsODAqwgLxpUxiHQhaXqUr2oI4V3g==", "mode": 420, "size": 522}, "lib/properties/borderBottomColor.js": {"checkedAt": 1755069205803, "integrity": "sha512-79Xr6KgYwhS6QSllBqM4GivAn8sL8Ay41zAO2uH4BA0RaVaqBwN4ct4+hgaGY8+e/EzZAAL/9altyiwjNPBabA==", "mode": 420, "size": 353}, "lib/properties/borderBottomStyle.js": {"checkedAt": 1755069205804, "integrity": "sha512-9adgUgXjNmoHWpomkBT/tuIEC+82bGiIqiitbZDUTmoS99jHd/T5wBD5J6JpGuR12ds6in8nq5gjYGoAMItsQA==", "mode": 420, "size": 476}, "lib/properties/borderBottomWidth.js": {"checkedAt": 1755069205805, "integrity": "sha512-8FusM/77BTSXJhHdN5AZeby3iiEdh9ZRl6uXKf3iueFl+hGaymUkxJfZpCT7rJak1vr+tv5frqFL3ZW9rxxssg==", "mode": 420, "size": 353}, "lib/properties/borderCollapse.js": {"checkedAt": 1755069205805, "integrity": "sha512-YMhwcfKJg86QQH9UNYI55Wpk8rTDoVT/B3Z53BljHhkGdCxTkBeyf4V4K+rF7aKjKPFS7xUExL1QtbpgIAbL9A==", "mode": 420, "size": 541}, "lib/properties/borderColor.js": {"checkedAt": 1755069205806, "integrity": "sha512-Ofo2CcyhnDQvOU29vGGy+RYJuGTWUtuAk4eSrl++3J4rjly6UEPDVK5bk9cdXUmns3VK1+xiehYrEPFIbjd8Vg==", "mode": 420, "size": 678}, "lib/properties/borderLeft.js": {"checkedAt": 1755069205807, "integrity": "sha512-x3KVpjL44BH/C0D+9Wha7z6HqhxsA5W7mjqaeDOKTKcLgEooJS2N1qlTNwAbuMqOYEI+KukS0gXR1Z727uzA8w==", "mode": 420, "size": 506}, "lib/properties/borderLeftColor.js": {"checkedAt": 1755069205807, "integrity": "sha512-yuKDZzxeUXdmuyzjLd5cdXspiBKwmAbpHCZ+AWKeYmNpmm4RZCAydQlmLcSiIlqOfinB/62LH4Ua4i9IW60ACA==", "mode": 420, "size": 349}, "lib/properties/borderLeftStyle.js": {"checkedAt": 1755069205808, "integrity": "sha512-tNRlDTIQHxZ8Qr9ykFW5FmaDZWCJTTwxaK3v4pEN3jhIcXp8o7tXj92YT3LhbxaaH8Y56CDi8NYAIRf+JHI73A==", "mode": 420, "size": 470}, "lib/properties/borderLeftWidth.js": {"checkedAt": 1755069205809, "integrity": "sha512-g2HeIR5ZLoRcK2djjf8o+EUw2/R8MUM+tjBTsl1ykDMtY1dkTLLU76xvaXelrKNdyIZ8neBcpE483OrxO+3bKQ==", "mode": 420, "size": 349}, "lib/properties/borderRight.js": {"checkedAt": 1755069205809, "integrity": "sha512-RlxzHSjFALHfsiSihVTrvx7n/4voTFcoAcAvmbAzw9HK8e8rvLO2yY5SdIxP2WjsjwmgXDQ3P+YC2LF2lGV+kQ==", "mode": 420, "size": 514}, "lib/properties/borderRightColor.js": {"checkedAt": 1755069205810, "integrity": "sha512-Yl7VZHJ63q9uKwdvFwIIZqnAKL6bHBIh1Ecip6S+HZyfpRCyU+5g7KANDYHufomyXumRtbmWfui79rzqYZbxmQ==", "mode": 420, "size": 351}, "lib/properties/borderRightStyle.js": {"checkedAt": 1755069205811, "integrity": "sha512-O6JAGCcEz1+EXFjRBFpXnLU/QmwZLQIel8Z2evyr937bF7G1rPTEJiR6uoFSUM14m0he0WywXt3pQu9d82tQxg==", "mode": 420, "size": 473}, "lib/properties/borderRightWidth.js": {"checkedAt": 1755069205811, "integrity": "sha512-YtRVpkCZwQJygQhMoUObgDNXegy9yr0SuyCmLnt4zHTT8rj40ZTpNWB5+fCW6gCR5CMRjJf9hvPewhux899sMQ==", "mode": 420, "size": 351}, "lib/properties/borderSpacing.js": {"checkedAt": 1755069205812, "integrity": "sha512-MR9ZfXxNguzBmz0ZXxP2toe1bU755IAdCStS9QLjeR7Jz6KTcGKSrLD1gR0Kca1yzHHlGIO7fGlnq6/IZppndg==", "mode": 420, "size": 904}, "lib/properties/borderStyle.js": {"checkedAt": 1755069205813, "integrity": "sha512-SkLZHdHaOQmf+sQEVEx+qCF69P8oCdzhlvB9gLL1qI2XjPqCjYny/+XTaVB0VMdjIO2ez7iElSL+7T/3tX51CA==", "mode": 420, "size": 710}, "lib/properties/borderTop.js": {"checkedAt": 1755069205813, "integrity": "sha512-Qm8dTEk05mXLsK1O70gpZmOrRq2brexBFPU2BurVUyeJJbVEoHCJSxlrwvr7TwKduX3EHPYABreO2094FuBQOg==", "mode": 420, "size": 498}, "lib/properties/borderTopColor.js": {"checkedAt": 1755069205814, "integrity": "sha512-4gG7wjILYoQg++WMpP9fv1liS+ZJnYJ35aRe+qHf0ly1O1Rpc2xnoLeYFAhBV2vuXA/3GeAAPx091y94a2xvMg==", "mode": 420, "size": 347}, "lib/properties/borderTopStyle.js": {"checkedAt": 1755069205814, "integrity": "sha512-uHJZI0QAuWyTG7u0NQWufYaZA8dcNP05PUvevayMs4Oou8LXr90ZWhu2bXQN1gQhg97vxsysgX38auEuhDchog==", "mode": 420, "size": 467}, "lib/properties/borderTopWidth.js": {"checkedAt": 1755069205816, "integrity": "sha512-x8E+30oXj7D39yTYQbCuSxS51ozVg4u4wa3XmSn7yKX8sizaKzlesolib1suiHnYk6rK7ifs487VnYm5dpK4/g==", "mode": 420, "size": 354}, "lib/properties/borderWidth.js": {"checkedAt": 1755069205816, "integrity": "sha512-KqY6iCbgSneI9M7sl5K+MymDo9lMEgoqNS1c+X4BnxUH6ujjTXbPnh3+dE0jucveTBFkWnP9IgTWz0KCW8BUWQ==", "mode": 420, "size": 950}, "lib/properties/bottom.js": {"checkedAt": 1755069205818, "integrity": "sha512-Ym681wWCBD7YtMq/2tvTVeBc5srwchQVlLUQTVwpXUFVOkb+MCn/pnv5pyfnZtUtZXWTTey8N3NxDSA8aLhyRA==", "mode": 420, "size": 303}, "lib/properties/clear.js": {"checkedAt": 1755069205818, "integrity": "sha512-LX3jHrK/4/S3PyGbCDCzHRCTI/CdC0e2nU5IioD72K042NKDk9FVshymmkPenaOeBsasRzOLuZfGgDvLQjOEXw==", "mode": 420, "size": 373}, "lib/properties/clip.js": {"checkedAt": 1755069205819, "integrity": "sha512-eNa1HCfp1O25qfFi9xFr+sHAd5lutzeoCXsx4DlafqS5KsjvEXKNJaAkjy3b1sPeiKgj54PpHCN4dSW3BuPeLg==", "mode": 420, "size": 1029}, "lib/properties/color.js": {"checkedAt": 1755069205820, "integrity": "sha512-eWfEXIbV5lnLrPWP3WF7vgeGcwCLw1vtxpyotJFrUBuMRUBC5euchIJYxJN8zXi12tXj2f26dq8AEZY06FKEuA==", "mode": 420, "size": 283}, "lib/utils/colorSpace.js": {"checkedAt": 1755069205820, "integrity": "sha512-4gIX9lhWRs29CgbWQURwfiJcyaWyVHDE7zW/uAzi0uosg9PFEbNM34FwFQr1lf9lgFaqkOlreEamlbWIVvMJIg==", "mode": 420, "size": 640}, "lib/constants.js": {"checkedAt": 1755069205822, "integrity": "sha512-u+rKwfsje6sZUix8cu3DPlxSpZqxJ0PDwujsox5jRlSNoOLpYls9vOyZIAz+s7sroS01DHixbAR1+M6x/QV/Rg==", "mode": 420, "size": 84}, "lib/properties/cssFloat.js": {"checkedAt": 1755069205847, "integrity": "sha512-x5zk9hMVooFn0uQLuPQcgWYMXZ0AgxIwTFGC/f7Jp+KIkERWpJCBgsHsqCiRzu8B2LiRR7WfCe4XAUGYrVLuGA==", "mode": 420, "size": 219}, "lib/CSSStyleDeclaration.js": {"checkedAt": 1755069205848, "integrity": "sha512-pFTN5enzYUCLzxf0DPd7yYis0Tb6RyMBGV4aJCS66hdffxF2JaLMqrphXVpMWeqSeacMbZLYLnui4ZH1xeWLxQ==", "mode": 420, "size": 7149}, "lib/CSSStyleDeclaration.test.js": {"checkedAt": 1755069205851, "integrity": "sha512-Q9LruwRqySUQvAasyg+ObY5s0mrBujikpakzL975iBe4DbeK7jU6I38EqO80EWD0PQ/BGOvwxWslh9D0UH+R1A==", "mode": 420, "size": 21698}, "lib/properties/flex.js": {"checkedAt": 1755069205854, "integrity": "sha512-KR5hlJwRT1GcXNkC40t8PxCDsvsplzPwO0P3e9w2Z6TyuQDb8xet3OSZs7LNqp4f3xbMbcYcu+QsWUqCGTyD3g==", "mode": 420, "size": 1188}, "lib/properties/flexBasis.js": {"checkedAt": 1755069205854, "integrity": "sha512-LumVgCTMz0cgFgwnfi+FZ5wzO2vnPyg7LjgJepmtFv1jW1imNajgX3sn4TSZo8j5abgQ1e3GRrq3p63sBBt9bQ==", "mode": 420, "size": 577}, "lib/properties/flexGrow.js": {"checkedAt": 1755069205855, "integrity": "sha512-W2IV0Wl8If0d/m7EwsxFmLVVy0+RKgK1DMBRK5sUWxQV3LiorIM/Y9UKzjMUQEcDuOv/INgDjR80mqxN7iGbng==", "mode": 420, "size": 543}, "lib/properties/flexShrink.js": {"checkedAt": 1755069205856, "integrity": "sha512-+YTSnX9mGJQ1oQ3CaXJ0pcU/rEzHafrIaWgrri7LL4enJZwzVLNnVEa6RC/XwbbsPbvLAai7iQ36XcaD8vnNhA==", "mode": 420, "size": 548}, "lib/properties/float.js": {"checkedAt": 1755069205847, "integrity": "sha512-x5zk9hMVooFn0uQLuPQcgWYMXZ0AgxIwTFGC/f7Jp+KIkERWpJCBgsHsqCiRzu8B2LiRR7WfCe4XAUGYrVLuGA==", "mode": 420, "size": 219}, "lib/properties/floodColor.js": {"checkedAt": 1755069205857, "integrity": "sha512-cfLf0FT2BO7IX1pij4d5Jp4jWdDMgcq49BVwOpZNJkAg/37ESuwIu00eMoHmdAi1ziBxrh3qL0ZKzZEfZ3Tumg==", "mode": 420, "size": 295}, "lib/properties/font.js": {"checkedAt": 1755069205859, "integrity": "sha512-+XDqxH2O/YVbHEKq2+DvEY+IWFWLw6zPQNc1wPiJtBOMTKa6SE3bbV3Bb6h7SEWvS5+yY/syWaGa2q8W44Oc6g==", "mode": 420, "size": 1143}, "lib/properties/fontFamily.js": {"checkedAt": 1755069205860, "integrity": "sha512-3iIe2W3RGK5QErK//VwKwFVOiVXq256hsg3/ceB5Fh/Jh3wvyxuebzLD8l+chiMS8l5WIsOqEd8AKM81NoTxfg==", "mode": 420, "size": 702}, "lib/properties/fontSize.js": {"checkedAt": 1755069205862, "integrity": "sha512-59bkJgPQAWOArUDF33hugLL8TogsyWflIqyWmz7dRhODXjJLqPkPiKemYemlbjYW4qEVAmUOUQ3i9qsC9LkPcw==", "mode": 420, "size": 1171}, "lib/properties/fontStyle.js": {"checkedAt": 1755069205863, "integrity": "sha512-ODgZ4rNgUqKZghNDiWP8WCfUHLVRT7vY9TnvZ3hLO7emJVYbdSBiY3gb1tL1azQ9Qd25kGOH0BFm/ZkZcp5Ogw==", "mode": 420, "size": 391}, "lib/properties/fontVariant.js": {"checkedAt": 1755069205864, "integrity": "sha512-I1td3TZuzDppG4h163R7OErE4HQmVBxLdSfw+CDDQMdzqUiGwZ8eloLRgUUBs9zZOwncWGdt0qMq0GRUYSz4WQ==", "mode": 420, "size": 400}, "lib/properties/fontWeight.js": {"checkedAt": 1755069205865, "integrity": "sha512-2UkiahcnhUMlMVZHMU4kwXzqOLTf4eCMOEmFrsoidMVTlelGl/guxwbt18m8FKxsxfwLFXjmmpwekazFnyPKyQ==", "mode": 420, "size": 505}, "lib/utils/getBasicPropertyDescriptor.js": {"checkedAt": 1755069205866, "integrity": "sha512-o4a4Et4usQWgRfEsXy+2iSRbX8Zcx6d2e2OC2T0Xt+vVcsdTcPJWlphsy3tKgSoXmJosLpt/yrPetDC/jk5QcQ==", "mode": 420, "size": 276}, "lib/properties/height.js": {"checkedAt": 1755069205867, "integrity": "sha512-jjRUoZFK7Zp4ofcNsOaCZ5BNollCHpPKlK5rDKDiMcNdo+OSNqt915jZDKD/RG0xN64ij7btORB0Ob86wvHa9Q==", "mode": 420, "size": 485}, "lib/implementedProperties.js": {"checkedAt": 1755069205868, "integrity": "sha512-IuIUHNEi3X/UJgkKgOnTZSM/a1qTlYMlt5vcfbu+3SewML6Zz4S41QH+mZL3fAH+Wf1rw/htzZ6uz8Jdeja65g==", "mode": 420, "size": 3720}, "lib/properties/left.js": {"checkedAt": 1755069205868, "integrity": "sha512-zjwaeca07wjPQMSniE+IggyaGTu9trc236u7ogFJ/PcZ376ULqWP3HwkDa4UpOgIfgL2aJyZuovK9+hLXV58Fw==", "mode": 420, "size": 299}, "lib/properties/lightingColor.js": {"checkedAt": 1755069205870, "integrity": "sha512-Fd11kUAB6suh6wUoSPPXH3j5AhtZJQYfu9xwaPz/WAm4/zddVIxA+p572YCjUiQdOl5kG4N+EMrhxYmmnijuJQ==", "mode": 420, "size": 301}, "lib/properties/lineHeight.js": {"checkedAt": 1755069205871, "integrity": "sha512-a1bFbbCVx26a0zJzSt5wgHeDLLclO4ZwDMff2xAP+ZqCbTuUGhgcR4iCDVYjy+BRQ3bSD1nf+KKmssXCLYsgHw==", "mode": 420, "size": 602}, "lib/properties/margin.js": {"checkedAt": 1755069205872, "integrity": "sha512-gYVKfcz7/yu+uPGw3cnXDvBG6zfrh/VYdvcyPYGqsh97f3LnAMaduRDjfEHBbqjU+6eB90hzCcHCd34iGYpIIw==", "mode": 420, "size": 1266}, "lib/properties/marginBottom.js": {"checkedAt": 1755069205874, "integrity": "sha512-fIcwM1iihiDLGvugPaKO6BZzHUx452Nesw6OfJU1Tv+ollIh3ICxb9QZuwK6+NcvPV6jYWt2DBhj1SHuEzVHxQ==", "mode": 420, "size": 329}, "lib/properties/marginLeft.js": {"checkedAt": 1755069205876, "integrity": "sha512-VZ9/oR5tzzeL69WC9qXqLgfix1eK1d1yFiYwtRAP+/3HAhOFl++tC1ouUWYecB9zWYcZdZk9FLhNddtY1EZQuA==", "mode": 420, "size": 325}, "lib/properties/marginRight.js": {"checkedAt": 1755069205877, "integrity": "sha512-WbDL75QaiOkuI34BuSTSB/Of/VKw7KNqsO8THj0THjcy77/gJkhSZ19Xav3DKDwxKO1aHH+2T9X2l9ueXDloVQ==", "mode": 420, "size": 327}, "lib/properties/marginTop.js": {"checkedAt": 1755069205878, "integrity": "sha512-B/b3oQ0HCH0/p9TCYW2HdVta9mbi++p2zSjVh8erWezf5eM6K4Kw9k0rWwAwjn31xAo7vMdrwsdEP9nSpC99Bg==", "mode": 420, "size": 323}, "lib/properties/opacity.js": {"checkedAt": 1755069205879, "integrity": "sha512-KssWbhDYe74ZjPaVZetK7oLUxh4+SKG8muoOGkqDOOGwUgZczdBPj3qfDezzMlPhgNc+356cPg7PF/D4lpNToA==", "mode": 420, "size": 290}, "lib/properties/outlineColor.js": {"checkedAt": 1755069205881, "integrity": "sha512-5pmYtaoNJYFcIe+7cy3xHueJGTK5gsal3e/IAoYijP9Vi1iWwXJyd7LDt/PD/H/kVg03uj/6X5Ic6QUZAOjk3A==", "mode": 420, "size": 299}, "lib/properties/padding.js": {"checkedAt": 1755069205881, "integrity": "sha512-SO5fGOwjBt/cZYVCx0OoS7Gnkdd3iFz0XGNKWxDNNsX3sDCt3X1p5YICXT7fEq5eY13VimM3J6XrUCCms6F31Q==", "mode": 420, "size": 1145}, "lib/properties/paddingBottom.js": {"checkedAt": 1755069205883, "integrity": "sha512-962RqPl4yOB1lqVLj/ShyaKfIaMf9KZT0zD4fdQaR4X2hCIDsPUhOUT5AbL9Ry48THvcg8SbWUA/Hk0MGKm1Gg==", "mode": 420, "size": 335}, "lib/properties/paddingLeft.js": {"checkedAt": 1755069205884, "integrity": "sha512-19bM2vMz27MCpuvYIGSwyMiyWKR3RSahTwVFWOw+XD3F5VcOd8u10paddzs64gIwzPbA5ag2d0NSk52B1tfVqg==", "mode": 420, "size": 331}, "lib/properties/paddingRight.js": {"checkedAt": 1755069205885, "integrity": "sha512-jzqDwSVDF9yBiUz3MfNStkun+z75XP5dcwnykkYPfT05kv+z+GgIf4flca3V+GbxzYfsNwPnh+GBu+73D5zNPQ==", "mode": 420, "size": 333}, "lib/properties/paddingTop.js": {"checkedAt": 1755069205886, "integrity": "sha512-4B9daSN80Wwws8QKJEBEE6Q3LL0ijiTE+t7E3stOTrzxjp0BtzkcCYrIcsSOM4fETjr3uJl1Fd45kGZk+eGc8w==", "mode": 420, "size": 329}, "lib/parsers.js": {"checkedAt": 1755069205887, "integrity": "sha512-bW5ySIK7mqq94XRy949WNfLggeagGGqECXgkeMPtHC0USFne/4QpxvRSdp7zyoHg37seLGaMXom/RfPULd6zfg==", "mode": 420, "size": 19382}, "lib/parsers.test.js": {"checkedAt": 1755069205887, "integrity": "sha512-QWvp7YMW61nJCSODC/S4jCdgv31K3zzHnXLhf6K/LTNnBy/B6DD56nfev7k5vyshWlZAZmR0zsSkxJdcRvI1qg==", "mode": 420, "size": 3292}, "lib/properties.js": {"checkedAt": 1755069205888, "integrity": "sha512-Xc+Z6qvZGEgx2BPagDpkEh8x+SECX6wIohkRD+pqlXu+9RT0IG5hIOZrQG7tO1R/PNFvMkqZBwvKq7dYezGWmA==", "mode": 420, "size": 57699}, "lib/properties/right.js": {"checkedAt": 1755069205889, "integrity": "sha512-QwkzJLrVNzEZj5T1oFn5Dq+5aErnUgXKiD8uZ2sTchE4agTWFMfmX+kYVWbjiHUJ0UVOnfKJPv6Mtv0FThmhaA==", "mode": 420, "size": 301}, "lib/properties/stopColor.js": {"checkedAt": 1755069205890, "integrity": "sha512-I+ViXqeOUvPImnZCdG4Ac4L74P0Gkz+JNxl/GXqgzYhpKFf1we0UWiRZTCI1Qs8Wyr/8T7GZVwB3w0H2QWKg5A==", "mode": 420, "size": 293}, "lib/properties/textLineThroughColor.js": {"checkedAt": 1755069205891, "integrity": "sha512-PdUKSQqAL2/jaSCLMncGvUqP3QULnx7zPuwFvGbcUbgN6Yk3IHRAinMNnCkGdFV7n82OnvEc/4h4xgUc0D7qhQ==", "mode": 420, "size": 319}, "lib/properties/textOverlineColor.js": {"checkedAt": 1755069205892, "integrity": "sha512-Fq8YGqj4kjK7YoNAOTgw3zLaNedQDKWDFoHR7azIkqM6OV4v5wt5usSohPcscBpJxSFA4ZRak/UoLcH3SkKIag==", "mode": 420, "size": 311}, "lib/properties/textUnderlineColor.js": {"checkedAt": 1755069205893, "integrity": "sha512-xm2agJJ+vvYT0RPeVwac1by4pHI1HvxEUE37R6iNT72LmjKclEg/kQvGnntudmWGo+58qwR3Kwufh80YKgmdVQ==", "mode": 420, "size": 313}, "lib/properties/top.js": {"checkedAt": 1755069205893, "integrity": "sha512-Nx/nIfqyz8VgxOFv/vsH74z2WEcymX9yMzSmDG90GukgRyIJ3TjO1dYgu3jwPyiEjHKg+xBV+wUqFBZEeg8nHw==", "mode": 420, "size": 297}, "lib/properties/webkitBorderAfterColor.js": {"checkedAt": 1755069205894, "integrity": "sha512-vMxQHfMSuVQeMyvZbXcTFfN77vu4a/C7Zt2wb5APlyfW8uLLn/dyBqEdmfKsWsqwT/Ht9iHtMGB43i54kQk14A==", "mode": 420, "size": 325}, "lib/properties/webkitBorderBeforeColor.js": {"checkedAt": 1755069205895, "integrity": "sha512-z+XLkEXo90qy1bmnll4NbIB7axneS/Q23kch9QbevTr8LX0IWSfwyMiRhwqhkpz79L8/4oQuzHxOf8WRsNswmQ==", "mode": 420, "size": 327}, "lib/properties/webkitBorderEndColor.js": {"checkedAt": 1755069205896, "integrity": "sha512-qb4ZkP5ghIbqVibwNjyXO2hj56fJSiknvKWfO/pTE8vzE7avYpPkPgv04XRZIKqJ5A+B0kE3zJr+vRxbxHakfg==", "mode": 420, "size": 321}, "lib/properties/webkitBorderStartColor.js": {"checkedAt": 1755069205897, "integrity": "sha512-kWS9qNmR2JN+MEESxpoyECpllcX+XkMRGml2uGLxEgYuaALys+PdOXE9wDIWh7noPONEkcqp9vzX6CcCXVQ/rg==", "mode": 420, "size": 325}, "lib/properties/webkitColumnRuleColor.js": {"checkedAt": 1755069205897, "integrity": "sha512-u0/cRzSwAV/zhVawqwOzazjaSC2vaKFLyoilggs6p+OzZWa2ZIOBpj8JHJqOXrD9FJp7h7blBy0xn0D1c2z7ww==", "mode": 420, "size": 323}, "lib/properties/webkitMatchNearestMailBlockquoteColor.js": {"checkedAt": 1755069205898, "integrity": "sha512-sb978MuhdZKNMDk1GJM7KgmqEGnXhIpPQC2PYvMVGxgm0o3aNxf+C3MNK/9/5av8tvmBSoXZXjf7bgk/5j2Sgw==", "mode": 420, "size": 359}, "lib/properties/webkitTapHighlightColor.js": {"checkedAt": 1755069205898, "integrity": "sha512-UDRzt95SNtxtnC6ciKkeM/FE/07Nd5GChZL9UXlzqkCZ3tgapNeQRAM665NeqL7c3IfPqfxMP6GnbMDPv3eiVg==", "mode": 420, "size": 327}, "lib/properties/webkitTextEmphasisColor.js": {"checkedAt": 1755069205899, "integrity": "sha512-sXTRZZkv0p/zYV1OZU/lff1jEQesnZP4pXO+RzlTnHgo8Xn+8bnQrwJgENMyihyiOp38ZL+WwYgLcO8Ds9LZ7A==", "mode": 420, "size": 327}, "lib/properties/webkitTextFillColor.js": {"checkedAt": 1755069205900, "integrity": "sha512-iJIJMbWAmQOTJczE291BAan7aB0kmJmDQ+UOv5wc2+kEyuRL7Rqq+Py6W8YyC+eOzSCHmgVh/RX3VFs7+z8cRQ==", "mode": 420, "size": 319}, "lib/properties/webkitTextStrokeColor.js": {"checkedAt": 1755069205900, "integrity": "sha512-FIveMrRwc7MZLTGuslfZSZ6lwYS7GSVYQjFpFS7daJj0pxYTbl+t8sU0iCR2qpzSfXfxjJ7XzegJgk37eZS93A==", "mode": 420, "size": 323}, "lib/properties/width.js": {"checkedAt": 1755069205901, "integrity": "sha512-JPQvWBvcw+hbgPmQ2vuQSbVezzzWva7RJLdLKbfZLeLUF8+Hp1Z+6r6XkVXDKfvu8IZlqbpv11fwBZKT4Xy1tw==", "mode": 420, "size": 483}, "lib/named_colors.json": {"checkedAt": 1755069205901, "integrity": "sha512-L4Wp3f9iOXPEOUxTO+8PAjRPP6NJ3i7ldEZP0EiIuonyh9kS1BS0lxv6StujkwZFUpu7RvunPrCyNmO+2pVwmw==", "mode": 420, "size": 2248}, "package.json": {"checkedAt": 1755069205902, "integrity": "sha512-5NVZQ7zHE/C+RUYiM2xYiVwcSZqtdu7KbUpsFBLWtQPLpoq8Gd6W+O6HycHJh8jEvU9zXlVa56U6qCzm+t+gNQ==", "mode": 420, "size": 2009}, "README.md": {"checkedAt": 1755069205903, "integrity": "sha512-lufMxtXlhRxM6+DffKNlzzcAwTh4u02TfCFNs/e4ZM4JF8b9Bee/Vw6L0U5UjCFkBKChkJhznWAIThPcKY3LbA==", "mode": 420, "size": 919}}}