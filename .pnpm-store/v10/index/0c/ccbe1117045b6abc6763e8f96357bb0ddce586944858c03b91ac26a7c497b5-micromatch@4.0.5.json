{"name": "micromatch", "version": "4.0.5", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1755069204221, "integrity": "sha512-cjkrzNiWTIjsiqPYFXRqK2pEZtnHyo9CjX0PPiuxFnTvSUyjNciyVe7lglwIene7RaXWACXzGLeKZOGb7M0jxw==", "mode": 493, "size": 1091}, "index.js": {"checkedAt": 1755069204222, "integrity": "sha512-n+iWYw8GwMojP2vbeuJA+TzdZx0RkrkOnTMZV1rS1H1S6zgzWpc4FHLVXKpcSS+fx7ihu2Tuc+UulJstKOcjtg==", "mode": 420, "size": 13741}, "package.json": {"checkedAt": 1755069204223, "integrity": "sha512-EPr1Yz3BHOdo2tHOKBG+RVLf7ci3zddjOmEOg2gPeuMGtx+r6R2bVKAlrB2zyeRql8VmYVsNDoeStNM0jh/6Aw==", "mode": 420, "size": 2648}, "README.md": {"checkedAt": 1755069204224, "integrity": "sha512-kqV3KfApyppik/eMmDQ8TaR9ARtzSg00P0jCRhP1WPBgTgxI/FFqUk19odlKhg7SQUiL+dTXwC66MUb1nkg0cw==", "mode": 420, "size": 38467}}}