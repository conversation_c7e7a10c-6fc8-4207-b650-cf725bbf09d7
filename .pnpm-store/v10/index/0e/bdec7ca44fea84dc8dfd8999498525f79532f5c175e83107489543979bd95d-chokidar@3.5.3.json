{"name": "chokidar", "version": "3.5.3", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1755069200595, "integrity": "sha512-ggsqGO01HdjtHwG0G5OpQLw3Iuk6P6+OFiv2ljykADNDq9IoNfzrC52xLpHwqhcScm/jlevDg4QzLis28gPs1g==", "mode": 420, "size": 1129}, "lib/constants.js": {"checkedAt": 1755069200596, "integrity": "sha512-LaADlEAUw7Ju8x5Z5Dl8raBWaKYhhzROB21boiBy4caSKT7iwapnLsInhwhn2Epq7R2BlhwpBMWtHUlD07NJ9w==", "mode": 420, "size": 1834}, "lib/fsevents-handler.js": {"checkedAt": 1755069200599, "integrity": "sha512-0CyfqzZ4xgGCwlv9/OQUEGr2AoqcDRcx9lhGDq2gWJuFMnVN0St9ZRvvgL+XE2HETMmXXm4ovjulyCjON40IrQ==", "mode": 420, "size": 16274}, "index.js": {"checkedAt": 1755069200621, "integrity": "sha512-b8h5q2vlWCbA6YAmiAvyGCBYauQmVeS8tJ46l4gbMImqKnJGlBV8DHsJyxcDFcxHxpgyoburipVDxKe5xFmbFw==", "mode": 420, "size": 28368}, "lib/nodefs-handler.js": {"checkedAt": 1755069200626, "integrity": "sha512-Yn34I3CQTMcylWunYpo28W81O6BvoD6sggWJg8B5bke50Mm3FEIkvl/9n5OzNQzdmslo5kSl6CMzaFPYQ/dBRQ==", "mode": 420, "size": 20067}, "package.json": {"checkedAt": 1755069200628, "integrity": "sha512-7RaIcC+x2c4lObJErEdqmNqLQwVk8iv9I0DkmUG8I+jj0uOCEYihRCEI7AyDzR9Kue11yDNQOkesWxzYhqcHsQ==", "mode": 420, "size": 1832}, "README.md": {"checkedAt": 1755069200629, "integrity": "sha512-0IVUdgmykXXS9BsES4/XFJWjo7WcW3gnaT56nenGXCQwQOcII9SYMckqWjlLbAceHzQTKC8f7uW09jD613H8UA==", "mode": 420, "size": 14275}, "types/index.d.ts": {"checkedAt": 1755069200630, "integrity": "sha512-h/Z+tTiprEBD4EWpHDL9LWzXUjl7YVFLPAb9Y1aCngGtYCzHP/8Z3x4e4DHlEYqCQGPKu3yvbwtQAw1UkvtQYw==", "mode": 420, "size": 6303}}}