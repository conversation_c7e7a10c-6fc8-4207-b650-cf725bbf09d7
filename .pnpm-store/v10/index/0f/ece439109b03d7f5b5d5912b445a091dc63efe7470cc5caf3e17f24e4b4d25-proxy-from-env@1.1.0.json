{"name": "proxy-from-env", "version": "1.1.0", "requiresBuild": false, "files": {".eslintrc": {"checkedAt": 1755069192781, "integrity": "sha512-wfJ+CtM9HNZ72fSxF3ymdSiDJ92cdifG+HRtXsn/E2whEaukX052C4cuw4yQHi6iXO1eNODz15GNvtODMi6x7w==", "mode": 416, "size": 743}, "LICENSE": {"checkedAt": 1755069192782, "integrity": "sha512-W7pSFHkQOyvYdIqZT/3kAcdLsPG3FQUWIv/w3Fmz/yq8/s9+B8w8vanNvMaPLqeXIwKARKTkD/Na9UZjef6Bcg==", "mode": 416, "size": 1087}, "index.js": {"checkedAt": 1755069192782, "integrity": "sha512-qeFKyQeKUz+uEvfTFQ8S7orZKqff8F5w3sUyRYkSPXZi6Si5s95/wALeVQ7BjJ2jEfqvDO8gPT95H8esaqD0uQ==", "mode": 416, "size": 3348}, "test.js": {"checkedAt": 1755069192783, "integrity": "sha512-+DD9CdkzgZIaXts+Uod9czwAXUcREaJ2g6o2cJis+Bc8x1ynWh0QQX4lG7r9HiovSJBAcVoJ3rPlWdGPoEjYOg==", "mode": 416, "size": 17720}, "package.json": {"checkedAt": 1755069192784, "integrity": "sha512-O3/uEKWPPBEtD0mf5AvQKNlOBgVAJD72tdBYEorhZj2IIjvGAGok8ZWYs+wEJ0Pt8Rm8uSUf8KIxArn8wDxZ4Q==", "mode": 416, "size": 956}, "README.md": {"checkedAt": 1755069192785, "integrity": "sha512-XXMfQ94SxKxYUOxz81RaBgtAcTiiXhuHo2viZgKKFDv4i3Q+gX7sBNYr6LbFnV06OPCjB0e4vLSFeJZIVpFpOA==", "mode": 416, "size": 5270}, ".travis.yml": {"checkedAt": 1755069192785, "integrity": "sha512-ttjWqsJYaRKx4pDkNWxkmIXXifbcaqCwDKt8vucQTKm/Jj8oYBPgSeoY7lLGRMy0/15FaJvnQUkycj+9iCno/A==", "mode": 416, "size": 328}}}