{"name": "graphemer", "version": "1.4.0", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1755069200307, "integrity": "sha512-gaPd/mTcLB3webRXVCFYaQ/AMKLpLXX01H28kec4goiMjaKFmqekf/Kjyz6tfrtIbILvxdT0LyPOqh9MlWOL5A==", "mode": 420, "size": 1081}, "lib/boundaries.js": {"checkedAt": 1755069200308, "integrity": "sha512-t/eci7zSKKi0IdRyTm8PL6G5O5RpMZcbV2bfpJJIEA0YluJEfhtqABUqjdkCW+AOm3X7XfhBnL1w9/BMX+hXPQ==", "mode": 420, "size": 1953}, "lib/Graphemer.js": {"checkedAt": 1755069200313, "integrity": "sha512-MEGLtGVd7voLvuNRk1mv2WTyiVYKW42uN0X2zGQ/X63+kUxr4iabY/FunukKjbzIn2xHa1WY3u04R6cYHNSfeA==", "mode": 420, "size": 786010}, "lib/GraphemerHelper.js": {"checkedAt": 1755069200315, "integrity": "sha512-Z3m8OnfMOqjv5NZnw2Kj6Ihjgpx1rrMIA7V4AqpkFoStvyXmQEjNaICdtfe+mBPEFLC5+V2PG4ssdl2tA8cgWQ==", "mode": 420, "size": 6688}, "lib/GraphemerIterator.js": {"checkedAt": 1755069200317, "integrity": "sha512-WYYb5LzS6l0pzbUFxTwP78KptvhxSgBJXa0WZasoa1iv/FdmWyY7mw+WPLsQQias3RCXEh/A9MgYvqhSxUzJzA==", "mode": 420, "size": 1071}, "lib/index.js": {"checkedAt": 1755069200318, "integrity": "sha512-XHkwIHJzG5GbQy74bS14s8q11Ooe+J9ZSqxMgJ62jqadHNSOYmSI7uLfi/wrlG1Y1UiZGY3fZ70kqVKABqXZwA==", "mode": 420, "size": 316}, "package.json": {"checkedAt": 1755069200319, "integrity": "sha512-965QBTItmlpeB5E90eimyde8wNcjHeicjqrp2dPhIM7r8d6ft/x6WxTqNKgh1KubDpJ69tKfqeO+CblgkqqlqA==", "mode": 420, "size": 1433}, "lib/boundaries.d.ts.map": {"checkedAt": 1755069200320, "integrity": "sha512-nscR7UA8DDC5WYKk5qFE8ZM3190qVW1KLZU4kd2OmVPD5diimR1eI4nWeYfmNm43xCpkdQZzma1FvUWiwBVPAQ==", "mode": 420, "size": 471}, "lib/Graphemer.d.ts.map": {"checkedAt": 1755069200321, "integrity": "sha512-XyPl2bsaIf/hAvXOIwy/7DjOJEYqGeQbUDXq+cDK6BRvjkM/QTn3DcKlOTO3LpEQN4+4clDzRblq0uF41Y1nGQ==", "mode": 420, "size": 575}, "lib/GraphemerHelper.d.ts.map": {"checkedAt": 1755069200322, "integrity": "sha512-kDw0SNq2ABNC9vUBjndIEZwI9MKXibGm4WhcxDob0vl6Sd5XV5GJtDsKhNx5afYs0VhIpe6Je1dd/zSvUiSJZw==", "mode": 420, "size": 520}, "lib/GraphemerIterator.d.ts.map": {"checkedAt": 1755069200323, "integrity": "sha512-nEQW5gQdTnQbDYEJRPHC+1SpYY0DGuGj/8nAnJyRYclU5rjdatDCC4IF1s8zrzRGjOlBVlz2W9KPmJZkTZY+2A==", "mode": 420, "size": 424}, "lib/index.d.ts.map": {"checkedAt": 1755069200324, "integrity": "sha512-xYSEMzZj2gyQbHPsW329k48e9/XhBkE2Vn2e7YE8VJ7tNo8iB2kGIHfNcXK3ap5t510sjB1vAaWTpzP61ZCqCA==", "mode": 420, "size": 154}, "CHANGELOG.md": {"checkedAt": 1755069200326, "integrity": "sha512-6D4NVQnaw3ZbrQjAxKvjG8wwhmdLGwuWVNG5jJwVx/QNUR6kqbYQ9LZjVZk6BkHAiywHcUUImku4ajj++imd/w==", "mode": 420, "size": 701}, "README.md": {"checkedAt": 1755069200327, "integrity": "sha512-Bu9G9jZ6JEEaOvU74Vs3Mu7B6bMBdHg8KJCyQru+4QpwK6XgkzYTRPDnNK61bf5P/0FdYrpUThYvGyynHDuRLQ==", "mode": 420, "size": 6054}, "lib/boundaries.d.ts": {"checkedAt": 1755069200328, "integrity": "sha512-doWTsLiXYp1ggeGr3r+flBzI/PiAmzpi6tSweUk2YfXVb4LY1GJ5t1haAkjh7eQu/xLvbHAZCS0+Ea/129s22g==", "mode": 420, "size": 1005}, "lib/Graphemer.d.ts": {"checkedAt": 1755069200330, "integrity": "sha512-LzRz0tbibYchFu3l4qE3ZKpaJcx5mYJAuylhr68URVOjPi4AvXPv447mkb7sNSNumWpPHuga1TBjna9uKG1XEg==", "mode": 420, "size": 1390}, "lib/GraphemerHelper.d.ts": {"checkedAt": 1755069200332, "integrity": "sha512-fVQv26NEyv+opVNQgxVgjiC+y18oAYyGpJBdlOEQrkCeKyFPiuCe9RwBFMSJDkwiDa8r9pPZHZoNMnq6wQ8l/Q==", "mode": 420, "size": 1256}, "lib/GraphemerIterator.d.ts": {"checkedAt": 1755069200333, "integrity": "sha512-TIlcWxVWC3C/R5hjFleke78rJ7LL+xE4xSRINIBSBxw2KJ+u6azuwo3NHmnzxYRcxTPDj4tnK/kvbxolTOLehg==", "mode": 420, "size": 610}, "lib/index.d.ts": {"checkedAt": 1755069200334, "integrity": "sha512-VqGSVpq+x6/uiNZkMVWiVCO6n5icYKLOGnhINofytX/V+xd2tdEJx8wngq53LkDvSQuCWKT9XkXAQ4LaChMx7g==", "mode": 420, "size": 98}}}