{"name": "@stdlib/utils-escape-regexp-string", "version": "0.0.9", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1755069186214, "integrity": "sha512-J7jAJS6uUMo84Cq3xWcGZMDIJOA+s9oQifPwoA0j5kipVry59TZFxteWdKh8TMhtEIXcM1kRvgIQ1pEzaxIYVw==", "mode": 420, "size": 10174}, "NOTICE": {"checkedAt": 1755069183397, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "lib/index.js": {"checkedAt": 1755069186215, "integrity": "sha512-lu/mGC1sSt/5Lci9FF1ZYukHWpB4Ec+1att0SMntdIKU91szv+szEj28BhGLOSNiIDAMLL44art5iTTp+mVWbw==", "mode": 420, "size": 976}, "lib/main.js": {"checkedAt": 1755069186216, "integrity": "sha512-UW3S2zZRuKWkSm07XCf5ZPY8040GwVl04fGdI217zGJ+RDIuwMzKj4kmLN4sHQk4928ZTxKqalikWwP2AkSTTg==", "mode": 420, "size": 2053}, "package.json": {"checkedAt": 1755069186217, "integrity": "sha512-Ho06lrfWr32Aan5Y1qQp+MmnDlCKaSZEgL933AdSEuO/zI88kZpLWyB4g7RvtV6DVMrYPruPwV9ohDABCHaesw==", "mode": 420, "size": 1793}, "README.md": {"checkedAt": 1755069186218, "integrity": "sha512-c7oKTgA8gcQpXabKmk6kmHwKmuB5OZRgtg9ejYFoTFy+V3Yh4mqzxn+bFxtVpcdBDKkYObNqkYaSjv37Eqnauw==", "mode": 420, "size": 6864}, "docs/types/index.d.ts": {"checkedAt": 1755069186218, "integrity": "sha512-po/aGpxQ3P5watETC2k6ympHSB0MDSG8MPukz/qThZRIDspIjG7OoGpm2rjFN8fUvRb2UI3x+SeBLFQUdjc9hQ==", "mode": 420, "size": 920}, "docs/types/test.ts": {"checkedAt": 1755069186219, "integrity": "sha512-PgsQTuXMP9pNVRIgkecSMW77hyPGr61QqTFHq93wMIEq6EEywyS/fner47af+KuM9u6cymVDBlywz78rrhDsHw==", "mode": 420, "size": 1190}, "docs/repl.txt": {"checkedAt": 1755069186220, "integrity": "sha512-ePrCppPFATGUV8wMbvWRyY6o2Ko/hOvPRGJmNdZnMnAHmMhxoADel78YdLcJtRv5h55hS6FBDaI/l6lTivhEPw==", "mode": 420, "size": 320}}}