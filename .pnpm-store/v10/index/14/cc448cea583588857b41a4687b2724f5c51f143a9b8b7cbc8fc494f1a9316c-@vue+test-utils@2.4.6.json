{"name": "@vue/test-utils", "version": "2.4.6", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1755069201678, "integrity": "sha512-a7Lh6S4vK1hZfFeKL/QsYH4qRiJorEheDrcgfyHHIJzsNEjyUuA76bpL59yMa9hsfNJEVfSeIYgYyYq0dx43wQ==", "mode": 420, "size": 1079}, "dist/vue-test-utils.browser.js": {"checkedAt": 1755069201679, "integrity": "sha512-<PERSON><PERSON>+lA291jbntNxP7pKOJuocskZHoSoy6T2Mnu+2OUNMyKthaKI995yaiqnLih8Zjj+e6n//kYG9dwHbFnYefg==", "mode": 420, "size": 338169}, "dist/vue-test-utils.cjs.js": {"checkedAt": 1755069201680, "integrity": "sha512-siKo5Z11VI+sfgs69Vm2i6stuTBG9Wv1DG1B8GG8y6uGMKNSip9xCvkwFlKOpXGnFaVCqcOCwYslH+s1pChYkw==", "mode": 420, "size": 307840}, "dist/vue-test-utils.esm-browser.js": {"checkedAt": 1755069201683, "integrity": "sha512-Ju3gurc/2tXK90FnPYj6PVzXjNv1UUtBzp05qJ5kIcOelWu6hXL0SmkNciMouA9T0QYGkLJw2kwXbNe8h9B3xQ==", "mode": 420, "size": 490509}, "package.json": {"checkedAt": 1755069201684, "integrity": "sha512-1y2AAThFfHudUapk5HPHNq7VLIzqRXR7TNmvrkJr4eFdhi6X2cpk1Q0q48dOPtwyjey7lPXa95SdS+46kGGLVA==", "mode": 420, "size": 3361}, "README.md": {"checkedAt": 1755069201685, "integrity": "sha512-zP7bf+jXC2wiBx7+OQJSZkEiSsicKtB41SgQGiO55CVBncuaJ2Fq3JcGpKWdaoiF8INuW+DFYIRyc6to4yhYPA==", "mode": 420, "size": 1927}, "dist/vue-test-utils.esm-bundler.mjs": {"checkedAt": 1755069201687, "integrity": "sha512-CglZONy9fVZZYGkauSmfPUtlT89P9essi4TkxABKMLmS/cUyZ97ThHypinxkxmltvX2sQ01RFVSVRP1UuJB1oQ==", "mode": 420, "size": 307076}, "dist/utils/autoUnmount.d.ts": {"checkedAt": 1755069201688, "integrity": "sha512-U8NJ2krA7E6ErTZOCVUn7v5T5XxZkZHP65VT/VAutCCJxzXnPLFfCNEHpyQh2yZb+1HK8LyaBf0OC+Wx6MnkrQ==", "mode": 420, "size": 326}, "dist/baseWrapper.d.ts": {"checkedAt": 1755069201689, "integrity": "sha512-+EvbXNd77VMB7SUlzLpCQDgVJ6vrcDpi241zbgXQAjXcS/QRaUOlFk8GsaRcsgjiR6xOxNklWfyJK076R0Fopg==", "mode": 420, "size": 5256}, "dist/utils/compileSlots.d.ts": {"checkedAt": 1755069201690, "integrity": "sha512-nXsVmJHLf0/1yjWSvscDu3hHXfYyJ+XXhWgBxui9/OjESI3K1LfEUiUGqknOOrChq0FhqbZRb+oBOZvhu+B2UQ==", "mode": 420, "size": 119}, "dist/utils/componentName.d.ts": {"checkedAt": 1755069201690, "integrity": "sha512-Tta20pqFtMaJx8FCOlZzHLMzhFcWWbV9qS3JC2hBU6gU/vbTzOdNF5wBF5ojb54qoSdXk16CyMxuLmCi8IOGOw==", "mode": 420, "size": 282}, "dist/config.d.ts": {"checkedAt": 1755069201691, "integrity": "sha512-03mC/moxeKufFRTq3wwMYHFTJXuCrr1e5A5la2FRLNCZmf6T4kaSkrZit7KDxMyZEHpe5zl/RPd1WrgzyEZsEA==", "mode": 420, "size": 1194}, "dist/createDomEvent.d.ts": {"checkedAt": 1755069201692, "integrity": "sha512-f+HgoIX8dbvJuLQsLz2zy4YY5G3Iu+CQgyvWUFutIlk4hEnEDrB3aLl2Lt6yb2ibZrpG2FuTfKcVFdjSCQ42nQ==", "mode": 420, "size": 412}, "dist/createInstance.d.ts": {"checkedAt": 1755069201693, "integrity": "sha512-Eb4eyqkzXhsrCL3+P5U9qZ3urwzdraMnrBBud+iMtYnfIiCV0s+4buy3E1ccbnWErFrSbD6SZTPV7Wz6+e61/g==", "mode": 420, "size": 358}, "dist/constants/dom-events.d.ts": {"checkedAt": 1755069201694, "integrity": "sha512-JegEmrOhnP4HwGsQ2yEld0TygTvOoQu16l0fBWiXWWu1MEYaeVWS0MuPRxvh+44Z+237eyZxcjTBqCgMNanueA==", "mode": 420, "size": 30900}, "dist/domWrapper.d.ts": {"checkedAt": 1755069201695, "integrity": "sha512-Nr3UUunbLIzAXQ59uzDKuZchePV3Q3955h73OLgdJpb6ms3ReHVKqCxruzz4/MDCr8LtfIE7G/NsDRnPtdbAsw==", "mode": 420, "size": 1122}, "dist/emit.d.ts": {"checkedAt": 1755069201695, "integrity": "sha512-Fml/PaA58cuTduSCxW6/dK6EaqVDYZnmlBVi7HzKkUaShWnUfSWOUX4e1j7FQW+JbNxUB7UK6gFW3ewrxGxSEQ==", "mode": 420, "size": 448}, "dist/errorWrapper.d.ts": {"checkedAt": 1755069201696, "integrity": "sha512-OeXhTNvUeOvzKzf5SIsNCEB7W9bw4sOeH5nHxQObSrur31mXY5q6dhz6hKJOHKQK4GvE9JLqJdZkGWm9BJ6gqA==", "mode": 420, "size": 107}, "dist/utils/find.d.ts": {"checkedAt": 1755069201697, "integrity": "sha512-ZonTPgSTTWeYApZVTuQvXpNZRUUmngrHgZtiSvDgAd3eEt5tKHho91iimTXA6ShroPh74YlP+wriMOKFonxs4A==", "mode": 420, "size": 450}, "dist/utils/flushPromises.d.ts": {"checkedAt": 1755069201698, "integrity": "sha512-bfYirQUFdaWkwnSBftTiPo+MzKWJ5eK13M6/qmiCGESQzcVHiWpTtcUjwwkfuitOeAd/cKgYUPHZUmpGDvPwpQ==", "mode": 420, "size": 59}, "dist/utils/getRootNodes.d.ts": {"checkedAt": 1755069201699, "integrity": "sha512-CzSmfYe8OKVw4mC/iTC+MIQcy3E/mUzg+F48qGO/gGQKcWF/+Tu0cVmOUYG6G1ekzddzeLSUvr3B2g9y9AnnAA==", "mode": 420, "size": 89}, "dist/index.d.ts": {"checkedAt": 1755069201700, "integrity": "sha512-hG/vXQrVHKd3LGn8GQFia0kNmGbTqNWYP+6sp7c9RbIIfp3S/Y3iU4mNIGCrP7Vqhf38z1uQcrvccQrjWhkjPg==", "mode": 420, "size": 807}, "dist/utils/isDeepRef.d.ts": {"checkedAt": 1755069201702, "integrity": "sha512-k0wh3SzYGHEEV1llVv2gbrneRU9JCzwgDtCKFyQxaRciAfG1yDGv4lV45rPTB+wxJinlxftnnyktDBW2voFbrw==", "mode": 420, "size": 409}, "dist/utils/isElement.d.ts": {"checkedAt": 1755069201703, "integrity": "sha512-FZYtqtaUEE8zWVhST3rEJpdNMa0pUb6LvlUqUy+W6Lpniw0DyHqzhLTvdy6pYOm6OaBIKiAiIERy8bqf27814A==", "mode": 420, "size": 70}, "dist/utils/isElementVisible.d.ts": {"checkedAt": 1755069201704, "integrity": "sha512-g/5f4NflA321X8mu6dsHnmfpBI08T1QoaYTAG16bOulu7F79enVIScHP1jsrZ4DcxLK0C4h5Y66GUaLciQWOCg==", "mode": 420, "size": 205}, "dist/utils/matchName.d.ts": {"checkedAt": 1755069201705, "integrity": "sha512-F/00PyjTd7q4o4ixX6b+s47T+3IZ3BnJCei+owAZ/ya7CsspXfzUMlEgsH1zbIv5KNLgu9v/kmhzf20aPfGIdw==", "mode": 420, "size": 80}, "dist/mount.d.ts": {"checkedAt": 1755069201706, "integrity": "sha512-uIoCG6coJFRqCzKYdjAHyfSAFo9lzFR1aLEFfVHvSpLhTobcwiOJe1czO1dch0KZ2OHpC+zp/3HYys6SeDVGIA==", "mode": 420, "size": 1528}, "dist/renderToString.d.ts": {"checkedAt": 1755069201707, "integrity": "sha512-eCMfkT2u7iCRqWIR3XNaTgWrI9Z7IpQmeq+y1/ROqkZSFWoNo9DVx3KNP4n3zKgKLzIax+HvxGTbghG3FuDA2g==", "mode": 420, "size": 605}, "dist/components/RouterLinkStub.d.ts": {"checkedAt": 1755069201708, "integrity": "sha512-1jhTA3PeVi6UKDPloZK7qbc5TS63u1yb/cv6Y4Oj+DO27jWp+E8HqHjZ9DKEdNuegz4upAKame7IocEwFwaG6Q==", "mode": 420, "size": 647}, "dist/utils/stringifyNode.d.ts": {"checkedAt": 1755069201709, "integrity": "sha512-w2UxAXPBSOTGx86QcX+pSP4FDLNRm4zvNbIjUywTcYF07fyjAvqnKn3USGlnWh1EKPTToZPW5na9ShwbyXeGDA==", "mode": 420, "size": 59}, "dist/vnodeTransformers/stubComponentsTransformer.d.ts": {"checkedAt": 1755069201710, "integrity": "sha512-aKQBBl58TyLPlNI+LtUqiLzmBgClJJ0Uw0sTI77QkzAnwYbcZNh4o0n6qy22P/vPyEU2OiZviPBl8mV/qKhU/w==", "mode": 420, "size": 1363}, "dist/vnodeTransformers/stubDirectivesTransformer.d.ts": {"checkedAt": 1755069201711, "integrity": "sha512-D5W2ZrhRL5xEJ6h/MJGIiD7vp4XzYzFGcr+Hzbgtsjf/HimcfNDSN8WDziQpsrv6joc0Raj6rnWX2fVfTUmVpA==", "mode": 420, "size": 330}, "dist/stubs.d.ts": {"checkedAt": 1755069201711, "integrity": "sha512-SvdCfm6W7+BsT0x806uF+jN41C4URM7bYc4hN5nsN6qHjky6iYNd0RngRMFcpdb/WgU+Q51bN5MqF0gOAifUSQ==", "mode": 420, "size": 238}, "dist/types.d.ts": {"checkedAt": 1755069201712, "integrity": "sha512-Wa9nGzE6KqmFzLQdFLO28Vek/SpD4Z9In5d9Y7M+oPldz4IgFxp43Vu78HlGLcnzW+GHdZD2sfob3r3IW5qzuQ==", "mode": 420, "size": 4962}, "dist/vnodeTransformers/util.d.ts": {"checkedAt": 1755069201713, "integrity": "sha512-/33iyRgeMaXIQx3b4oZF9upv1pDbLTYVR2TeTo8PRoPwnBnFK57lIunRn2U/IdvFLu0goXgW/WQSd64E8fBJSQ==", "mode": 420, "size": 1264}, "dist/utils.d.ts": {"checkedAt": 1755069201713, "integrity": "sha512-4HVAq/8hrHbar78hl2jzYPSCD5lvXJuJmHgQn+COU/DNYC8/E6qpegnw4nkAbkXXpJN7W9vhDPzc98Oy+9Fb2w==", "mode": 420, "size": 1903}, "dist/utils/vueCompatSupport.d.ts": {"checkedAt": 1755069201714, "integrity": "sha512-h+B03kI9wTUOlxEb71DNQP9V5ugYrN8BkEUfspR5M4bZkf0E/9tSY2C4MWbnzIwEhrQcxWvXfHLS5mxNqaJXWw==", "mode": 420, "size": 380}, "dist/utils/vueShared.d.ts": {"checkedAt": 1755069201714, "integrity": "sha512-8FvcdOAFqD57DzpaYgJyZ8v+l/NAVj4k5hDz3+BlQo++6kx0VDzCtTZ0RAmH+ceVNHK6tn7kO0EfkIatIJfrKA==", "mode": 420, "size": 488}, "dist/vueWrapper.d.ts": {"checkedAt": 1755069201715, "integrity": "sha512-lvMOMzMSF1OnOxwde3SjdiSY81tM5Uka3Sdgmb196gP465kte+1g1cfTF3zyINZungxVkjmLHnneK+fzQZwmRQ==", "mode": 420, "size": 1580}, "dist/wrapperFactory.d.ts": {"checkedAt": 1755069201716, "integrity": "sha512-9AarF3RheXu9MHkSQECYtntDes3hpvgv0umb7j3q2aWwGzx10qlDu0GrG03Lr4GQeH/EfG21Wk24tMIFBUYE2A==", "mode": 420, "size": 850}, "dist/interfaces/wrapperLike.d.ts": {"checkedAt": 1755069201716, "integrity": "sha512-qJoHwPL5CY0u8fbkuZGeJWm6A1NlDonk/Knq5nmTrY9waAKtPX0FdwbWQREaStakgOWYmLgeJrHguI2hP2ySzg==", "mode": 420, "size": 3979}}}