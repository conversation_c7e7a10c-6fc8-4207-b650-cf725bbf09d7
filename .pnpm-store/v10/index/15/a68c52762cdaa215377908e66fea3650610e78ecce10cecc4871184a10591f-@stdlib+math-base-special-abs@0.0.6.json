{"name": "@stdlib/math-base-special-abs", "version": "0.0.6", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1755069189236, "integrity": "sha512-giZYqp2QbwsOHNySg9FuTpE8Yyro5HyjpXkP9Ck5aqbhCvG1sKLbnlYjh5mR2+1rIt/vv9FN8ffyrszTfwbkMg==", "mode": 420, "size": 23963}, "NOTICE": {"checkedAt": 1755069189236, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "src/abs.c": {"checkedAt": 1755069189237, "integrity": "sha512-0GUQWkGe/1K6U4uG0q7Mn3McgMNO5PmAmx8N84eCowraxUG0ghmHsQEbMLBM7m/za8UDkqblfdLD8+zMxfzorQ==", "mode": 420, "size": 1164}, "src/addon.c": {"checkedAt": 1755069189238, "integrity": "sha512-WtDTME59EKqTBA50w5KJIgMr7gccDf3QjBiJXLUR8U9NOLNxQBPzwqATWGSl7I9mzWzc19kbD0IaeCFggD5TmQ==", "mode": 420, "size": 754}, "include.gypi": {"checkedAt": 1755069189239, "integrity": "sha512-xJG5KN9YisK0l/oeoDSe2byBN3bQqSvYS0+QeSBEZf+7ap0RMxV7U4bWEZGAUPbBLX6QVqo/dUdofQ/Gxnx4Sw==", "mode": 420, "size": 2193}, "include/stdlib/math/base/special/abs.h": {"checkedAt": 1755069189241, "integrity": "sha512-OTJFE1GzFkY5p8uGpfZfFLT8psdzlRky8ImKNh2Ci8hyC2UTCPBZYPQVNJEkEUecifZ9EWebW6K0gr/7a4iGTw==", "mode": 420, "size": 1138}, "lib/abs.js": {"checkedAt": 1755069189242, "integrity": "sha512-ftr8GxQFNqG0/aQeiud3lnlrPyAMs1+5mILR9jwLCKChkROn9+Z1J5Lb5hCkE9qmOEx6vaAJwBT/HANtQcovKQ==", "mode": 420, "size": 2753}, "lib/high.js": {"checkedAt": 1755069189243, "integrity": "sha512-tP8xSbydHBpVev2PoL3pCAdmZkK678GFQIqnnjpQ2pcDm1AU/KWzVMhcHJ+qq9bKj3jse6A4WffPE5heiUma6w==", "mode": 420, "size": 876}, "lib/index.js": {"checkedAt": 1755069189244, "integrity": "sha512-Y0nrx4DseHTtgPKrEVRq7tMQhrUFjp+ILRsZwSI4khZf6A25UWsm4Mj+ykWwwjRLyxx/ZFgjJDHHiQYvRIV5ZQ==", "mode": 420, "size": 1104}, "lib/main.js": {"checkedAt": 1755069189244, "integrity": "sha512-JjaT/Ye8rhXF+A3N3CZ2xL3GHt3DU2wdhtCiG04XyNYc5AEPyhBRB+C5Y5Zj/W2KLCgQi7YA2ESLc/ulkHzvPA==", "mode": 420, "size": 1194}, "lib/native.js": {"checkedAt": 1755069189245, "integrity": "sha512-p+7TBfMI/lNLNcXePeVpnIDeTgrpyEErG6Jgs2KY+QpXtSDo+24OVr2tfeJKWTq12CCmjgwHcCdy0STm0J1jAw==", "mode": 420, "size": 1219}, "manifest.json": {"checkedAt": 1755069189246, "integrity": "sha512-S7HQAykH24yyhuRMoI3FpiPk/InX/jdIgzorBk5IXVjJbkq+HthwB13dv+W3skOA/rdwxfF3xHGDo/ExYapY+A==", "mode": 420, "size": 1032}, "package.json": {"checkedAt": 1755069189247, "integrity": "sha512-uZXzhCDt8EepITCMGxljdzmoEwX/FI/1Ca2TCI8/WhF+fsHwtQ/YTD3DiR+WXtLbwMENV/bu9nvMZlhJ3DFUxw==", "mode": 420, "size": 2471}, "README.md": {"checkedAt": 1755069189248, "integrity": "sha512-0Fq3wfMZog2gQmAMGBjwbZnevYmUnIgQX1gDtFJlMJWpz7dzbCBep9+Z7aZHOGGnfKvAgqAYXUSinXs244hJrA==", "mode": 420, "size": 7675}, "docs/img/equation_absolute_value.svg": {"checkedAt": 1755069189251, "integrity": "sha512-0zzt4Jfz5g317DE0W5hwQqNcp+oG1FKbUNFi45sPtVghb42kVjy/QNw/lKaD5NFHk+vDzCTgLru+CtOv9NxX7A==", "mode": 420, "size": 6467}, "docs/types/index.d.ts": {"checkedAt": 1755069189252, "integrity": "sha512-0FmwmBeuqKg8c9EspKsUTCon5cu8g4COR+z5ijIgi2xTEHd8dmW6ETd7fJfpWzUqX7vBbXfMAylx4URpsfBWCQ==", "mode": 420, "size": 1118}, "docs/types/test.ts": {"checkedAt": 1755069189252, "integrity": "sha512-8mragZGA1Y2zIbA/sGXLe7hOuLyBNxmtjzyacp4mnkHL//fRUMvzTFv8fUbkqP9+HvXHqQZC71VcB+p/gYKxyA==", "mode": 420, "size": 1184}, "docs/repl.txt": {"checkedAt": 1755069189253, "integrity": "sha512-/OM2VRxlJ0RfrCZWJAvqPNkNvbsXptqGmf3I2rEmHEI6MH9RgCuwxrVWBfMMN+FFMk6G8yvwAjcwOYBqjcH0TA==", "mode": 420, "size": 462}}}