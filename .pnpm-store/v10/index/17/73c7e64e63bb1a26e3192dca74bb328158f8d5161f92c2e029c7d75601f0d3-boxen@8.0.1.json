{"name": "boxen", "version": "8.0.1", "requiresBuild": false, "files": {"license": {"checkedAt": 1755069181580, "integrity": "sha512-0fM2/ycrxrltyaBKfQ748Ck23VlPUUBgNAR47ldf4B1V/HoXTfWBSk+vcshGKwEpmOynu4mOP5o+hyBfuRNa8g==", "mode": 420, "size": 1117}, "index.js": {"checkedAt": 1755069202898, "integrity": "sha512-3dG6tYTeFImvGK+nnXR9Dr6ZhhmIL+7knDgxg2/qOH9muJY3cSNFKeHHyJQesGyDs2fajtD/TUWje4HjPKPGHA==", "mode": 420, "size": 10923}, "package.json": {"checkedAt": 1755069202899, "integrity": "sha512-ZxKRqRb4jnTG62kqXYqdX5Uk1S9OH4rkkffJflRe9lIFLP+CQ0lk6/IPgaFM56TF+ixxw/HWZ0B9ck0pZWzkWA==", "mode": 420, "size": 1227}, "readme.md": {"checkedAt": 1755069202900, "integrity": "sha512-ItsYtSjiKnja5xTnIAACqdHxugA8gLxXbUHJ7RX9JhP2E2GAi5GlvJeqXLhX65+/Ik48FLJMJiXe/YTsuugnUw==", "mode": 420, "size": 5404}, "index.d.ts": {"checkedAt": 1755069202901, "integrity": "sha512-kcBMfo9PrPuR/m9am4yO0+d8cj9BpsoBpg61qXEz7PmvtB5UldsXushqUfF35iR75VsxcFgsBj+46OuS+YDXBQ==", "mode": 420, "size": 4984}}}