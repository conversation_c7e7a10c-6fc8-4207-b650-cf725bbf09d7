{"name": "fast-uri", "version": "3.0.2", "requiresBuild": false, "files": {".gitattributes": {"checkedAt": 1755069203473, "integrity": "sha512-ogGhgciV/rfs+QKsLZeuXKQn8VBoaRzqC5KdUFJCzI4hxMcQ9dYIhOfzt4pGbZ8hSOYeQyOnYAI7dJpKednSYQ==", "mode": 420, "size": 80}, "test/.gitkeep": {"checkedAt": 1755069203477, "integrity": "sha512-z4PhNX7vuL3xVChQ1m2AB9Yg5AULVxXcg/SpIdNs6c5H0NE8XYXysP+DGNKHfuwvY7kxvUdBeoGlODJ6+SfaPg==", "mode": 420, "size": 0}, "LICENSE": {"checkedAt": 1755069203478, "integrity": "sha512-+AuFj0XL//tbZYA2ZWiaMYeu9kEnYG6GvwamIZWYFg8DOUp4s5GenksuX7BwE27jClbgz9WaQPj00FhuufF2wQ==", "mode": 420, "size": 1763}, "test/ajv.test.js": {"checkedAt": 1755069203479, "integrity": "sha512-AM2mHJ7YwLztDn6KHzxhbo1L5Y3NQLKKkbEeD8HdobakdJgSF7v26HRa2MD8WvLqjOAht/GYaM7Oqf2mKfiPkQ==", "mode": 420, "size": 737}, "benchmark.js": {"checkedAt": 1755069203479, "integrity": "sha512-96IRfIROubku6ilYSbCz3pLrVbSgldAGEvw2XsXYLyO9GSqVB3ueVt6Qpk5vsP+b5ckX2icZe6XpH6o2d+RQUg==", "mode": 420, "size": 2677}, "test/compatibility.test.js": {"checkedAt": 1755069203480, "integrity": "sha512-NrV0AsflstbxGr2c/bN3eHlkdhjoyj00wIe/GzjXxYnFwcKhj+ZXyxWQTxqIArhQNhS8FKfFglHiSdEaAy60Nw==", "mode": 420, "size": 4117}, "test/equal.test.js": {"checkedAt": 1755069203481, "integrity": "sha512-V2I1Vq8p76oCLcYd7EUPpWGeaxO5eYwQVNd2zYjWW4l9eXy9nLUptkd00hSQwPBAVfwHsv73EwdzE9P2m18vMg==", "mode": 420, "size": 3300}, "index.js": {"checkedAt": 1755069203483, "integrity": "sha512-HMwrVGztVH94EwO74CphIR5oilHmFj6K89jXGH7qbAIsHQ1M4pUdfGkpuiRhD+EviYEy03tAx9VrO70H+Li6sw==", "mode": 420, "size": 9626}, "test/parse.test.js": {"checkedAt": 1755069203485, "integrity": "sha512-n6c6xK1rSEvk+RyJwG9nkXoDj5TfYUqLuCci9wDX0fG9Z7Rt/TC3dmN5A1/+e49lzWtxya9Uauj8EGBtfka6/Q==", "mode": 420, "size": 13870}, "test/resolve.test.js": {"checkedAt": 1755069203485, "integrity": "sha512-VwBsISzZFvMaWx6mNxFS34bB3L7sPYothfV3eu2xK0knZR7CGzZlx7OHLg5VWr5k94042ZYawJhmBe7fp8GFLw==", "mode": 420, "size": 3803}, "lib/schemes.js": {"checkedAt": 1755069203486, "integrity": "sha512-Hne2Pv8FxrWZHyX+aJcWG3BUPRwHYYmuzP88vpWRd096e5TeaqWTChLzzoDAmrv3LhgD7szTX6p/CDDrfvtf0g==", "mode": 420, "size": 4724}, "lib/scopedChars.js": {"checkedAt": 1755069203488, "integrity": "sha512-hNsZR7FQ5n+0zASIe3n0QLek2olbLmUU+RocoP2jKffJ23/aZG7iqgRdKWk8QHb856fYeurSKPKWoNxVUUGG4Q==", "mode": 420, "size": 245}, "test/serialize.test.js": {"checkedAt": 1755069203494, "integrity": "sha512-0hkQC5taI8nGBJ0W/OvJ8tiPQwbCy+R/HsVcOPGuGMj0DHq/01iN81i+AniqKbcQEMhXrcfxDJN+pUuVZl+4kw==", "mode": 420, "size": 5887}, "test/uri-js.test.js": {"checkedAt": 1755069203497, "integrity": "sha512-sfF3Lr7JHT+9wNTsTLNxnhPDq2MZopHc0Rudhgd9+NWVIR+bOg298JmgiRQGbczmFJTf8u/+rFBJ8j6f0qMUWw==", "mode": 420, "size": 41117}, "test/util.test.js": {"checkedAt": 1755069203501, "integrity": "sha512-HfqB9sIK425zTFkBEKfxVah+YXfv/UuQiZ7Mc6maOc1xaaezKePeR9hQACIU6g0f2gFJmmh2nHVpMQHsoIgZTA==", "mode": 420, "size": 572}, "lib/utils.js": {"checkedAt": 1755069203502, "integrity": "sha512-oVG28zw8W/3CegrSKOGu+fYzZWo6MLIzIzlTedJwWcXbXC5Mul97BIEQWX+kpudzw9S9wUiijA2Z9OKIUeFivw==", "mode": 420, "size": 5863}, "package.json": {"checkedAt": 1755069203503, "integrity": "sha512-UNAo9VTY69ei3kXfAXWN6xXKGb6xus2ko/GKjmz5VpSpnPqg4rUiY0c+yGEwTcuJeHYI2G/l/R9jxvDBt0MvaA==", "mode": 420, "size": 1211}, "README.md": {"checkedAt": 1755069203505, "integrity": "sha512-bnPc0fsQhbnPAMYigNZsISifwwU5e5QcxDZkFQ+asdCCCM3Huu9rJRBn4P/kqzN3Mybq8n1kMjLCpe3qSeieWQ==", "mode": 420, "size": 4292}, "types/index.d.ts": {"checkedAt": 1755069203506, "integrity": "sha512-Bn6bWADWotbRDFaz8SCkhPEabjcMsWn6Cx8kSUIk8Je03LTDI5xUOQiH2E3Qpe8Rlt6qHSPCeiyK9R+hvmoUbw==", "mode": 420, "size": 1458}, "types/index.test-d.ts": {"checkedAt": 1755069203507, "integrity": "sha512-RZcGlCP+U6YW4LDP0C2mNhlwwVVvKBKCaPEMK+jhGvKAyWijW6dVM8jITQM07yJmcNqlFSYiFF1x+E1l74+u+A==", "mode": 420, "size": 490}, ".github/.stale.yml": {"checkedAt": 1755069203508, "integrity": "sha512-bENWs0+KY5YXoptvV0UMfWUK9SGF4Hkud4Z1OfGXotgMM0GQsWUpZvB56whAF7K3t9zi5KO72bwAoI8t1lSE9g==", "mode": 420, "size": 771}, ".github/workflows/ci.yml": {"checkedAt": 1755069203509, "integrity": "sha512-ey0jHVk7jMDVfdudU9UWoqECl1YhVRNJGL28xC+X+ZKA/y/3W+Jofkxsl3Afnyi8rU8gdhlmYSbQ+52DXxFhgw==", "mode": 420, "size": 371}, ".github/dependabot.yml": {"checkedAt": 1755069203510, "integrity": "sha512-tT6mnVg5RhSsQw2wqsfV7LqFsXMu02v/c82HbdorKxaiT5eFDkqbWGj3DiOgT7umgO1d34yfNBaYhapdz5Z1qw==", "mode": 420, "size": 273}, ".github/workflows/package-manager-ci.yml": {"checkedAt": 1755069203511, "integrity": "sha512-eQCA/fA4qiI7fQc702XfpdISXD3xiT1daPgvh6vMh9C5ONt7uGFewoH0Ss2LTXna3XONc0KAZ30UdO0X3SdZ/w==", "mode": 420, "size": 319}, ".github/tests_checker.yml": {"checkedAt": 1755069203512, "integrity": "sha512-Nj1M0nGKcW05zpHLeTacEZiIIftmADd/Lw+Ip95sh3kZFI9Xjk6qQpW6//gbD/3qhnxePByUe8qHsWQ7utq7qQ==", "mode": 420, "size": 223}}}