{"name": "@stdlib/utils-library-manifest", "version": "0.0.8", "requiresBuild": false, "files": {"bin/cli": {"checkedAt": 1755069183741, "integrity": "sha512-btPeWYea8qUWpM8DeIXuPvWwFKTgauqnSOfXgHvHkol1cVuQKVegbGCYSHdgXM17tEinBoUZCqV2xsfEZ2OROQ==", "mode": 493, "size": 2226}, "LICENSE": {"checkedAt": 1755069183742, "integrity": "sha512-giZYqp2QbwsOHNySg9FuTpE8Yyro5HyjpXkP9Ck5aqbhCvG1sKLbnlYjh5mR2+1rIt/vv9FN8ffyrszTfwbkMg==", "mode": 420, "size": 23963}, "NOTICE": {"checkedAt": 1755069183743, "integrity": "sha512-4cuPRRYzgfVMbZIC790aV9oIoAEVC+4xyLwcB73N6IHP34DxsSH2gT61hzrQQsrrPKyZ3lwkqLKxnRVCIg2vCw==", "mode": 420, "size": 44}, "lib/index.js": {"checkedAt": 1755069183743, "integrity": "sha512-CG5xW9vmp9yNVSoh0peCO5m43Aaicgl0ndUQ5MO8M313hlkAEtTRhY51bxjEg/8SHTHmibaQ8HsKSwCZoM7ScQ==", "mode": 420, "size": 957}, "lib/is_object.js": {"checkedAt": 1755069183769, "integrity": "sha512-f4iZJsK6M8ugzt0BHMgusY0B6TsiOsOQZNA2JVzolUUJfCoLY3ZjT6cDkHwQGKlr2ySGtTRSRzmQFmY5/bKsAw==", "mode": 420, "size": 1592}, "lib/manifest.js": {"checkedAt": 1755069183781, "integrity": "sha512-bvV1ZtOc1B/uc8msjDNVII94XtC/aw+UFsnCO3PyKbhBilKw06niHb/w8BgolbUcLoBUDe9Tq+MzT6uyBQWvJg==", "mode": 420, "size": 8088}, "lib/unique.js": {"checkedAt": 1755069183786, "integrity": "sha512-Alx7H1jtwo0LgBgTYAASruzYiouU0vkPIJ4rulF+lCFD+zirj+ptrdep204A/bUtQ23fdqFKfj9AOYm6OcWIsQ==", "mode": 420, "size": 1480}, "lib/validate.js": {"checkedAt": 1755069183787, "integrity": "sha512-N2DYSQksB6a6HeX30h03R4JzuoPUHcr+BSA+cium0H+rA0SHcngfAY9NUP4o6voJEDvS2n8A5t5AAEZLk0bKGw==", "mode": 420, "size": 2443}, "etc/cli_opts.json": {"checkedAt": 1755069183788, "integrity": "sha512-0ZFjipKYMv8YKQbucbjPtzc+fNjTX/d4eLqVQWImfO0h6ibcTZ814S7Oq2BCIFdCdgaGGTc0OVWgMzIbKtn66w==", "mode": 420, "size": 158}, "lib/defaults.json": {"checkedAt": 1755069183790, "integrity": "sha512-3lsAjv+gtgelJ3Mp7KEaCPGOFDiaSgCj7Cto/mylXNaHCj/gq+cpN1UJWMj7rfehzduHBc+s2k881YNmc3ASsw==", "mode": 420, "size": 63}, "package.json": {"checkedAt": 1755069183791, "integrity": "sha512-2vSxn6E05iYwSgrYwcwaFDZXYfFwC2BLcMYsunndv+s7N4g4ORR5KInS/VdduBbT7ZAeRMyStXVwiPaT6pCR8w==", "mode": 420, "size": 2160}, "README.md": {"checkedAt": 1755069183793, "integrity": "sha512-qTydQMvKxo4DuTpx+C6icf5+pw4sv0ZWgWMpy0HpI2V4DVpNBe5pa3Z1rO/4gug1LabNlHWCq3Vkl8x8v8eRog==", "mode": 420, "size": 10857}, "docs/types/index.d.ts": {"checkedAt": 1755069183794, "integrity": "sha512-Vu76il+KCELaD0yLYKiq3ic/dEaGqddHyTq3lNWQgY71HEsKHnA5gIv9lHYylQ+eEEUX//n1SODSGxx5gt5JNg==", "mode": 420, "size": 1320}, "docs/types/test.ts": {"checkedAt": 1755069183796, "integrity": "sha512-yRjG+lcuTEVlGpareRUJh/tnGK8XoUQBPoXlrX0jCI3R2ojk1zVQj/ea1UQcxldQ9AhpHdJLKkOrQNOg4sO+tg==", "mode": 420, "size": 2938}, "docs/usage.txt": {"checkedAt": 1755069183797, "integrity": "sha512-RyLUdzm3rJoA+X1AwLHKB8JjKCbdM9rT0ByACxUrcJAiDmmY1EmIXXaHIOboOiCAqVYV2AbGvPn3ugmn9puPsw==", "mode": 420, "size": 297}}}