# AI微服务数据访问解决方案

版本: 1.0  
日期: 2025-08-18  
目标: 为AI微服务提供安全、高效的Chatwoot用户数据访问方案

## 目录
- [方案概述](#方案概述)
- [数据访问策略](#数据访问策略)
- [具体数据需求分析](#具体数据需求分析)
- [安全和权限控制](#安全和权限控制)
- [性能优化策略](#性能优化策略)
- [技术实施方案](#技术实施方案)

---

## 方案概述

### 🎯 设计原则

基于微服务架构的最佳实践，我们采用**混合数据访问策略**，结合API接口、只读副本、缓存机制和消息队列，为AI微服务提供安全、高效的数据访问能力。

```mermaid
graph TB
    subgraph "Chatwoot主应用"
        A[Rails主应用] --> B[PostgreSQL主库]
        A --> C[Redis主缓存]
        A --> D[消息队列]
    end
    
    subgraph "数据访问层"
        E[数据API服务] --> F[权限验证]
        G[只读数据库副本] --> H[连接池管理]
        I[AI专用缓存] --> J[多级缓存]
        K[实时数据同步] --> L[CDC监控]
    end
    
    subgraph "AI微服务"
        M[数据访问客户端] --> N[本地缓存]
        M --> O[批量查询优化]
        M --> P[异步数据加载]
    end
    
    A --> E
    B --> G
    C --> I
    D --> K
    
    E --> M
    G --> M
    I --> M
    K --> M
    
    style E fill:#e1f5fe
    style G fill:#f3e5f5
    style I fill:#e8f5e8
    style K fill:#fff3e0
```

### 📋 核心架构特点

1. **多层数据访问**：API接口 + 只读副本 + 缓存 + 消息队列
2. **安全隔离**：专用数据访问层，细粒度权限控制
3. **性能优化**：多级缓存，读写分离，批量处理
4. **实时同步**：基于事件驱动的数据同步机制
5. **故障隔离**：AI数据访问不影响主应用性能

---

## 数据访问策略

### 🔄 分层访问模型

根据数据的访问频率、实时性要求和安全级别，采用分层的数据访问策略：

#### 1. 实时数据层（消息队列）

```yaml
适用数据:
  - 新消息通知
  - 对话状态变更
  - 用户在线状态
  - 紧急事件通知

访问方式: RabbitMQ消息队列
实时性: < 100ms
安全级别: 高（加密传输）
```

#### 2. 高频数据层（API接口 + 缓存）

```yaml
适用数据:
  - 用户基本信息
  - 对话上下文
  - AI配置参数
  - 实时统计数据

访问方式: RESTful API + Redis缓存
实时性: < 500ms
安全级别: 高（JWT认证 + 权限控制）
```

#### 3. 分析数据层（只读副本）

```yaml
适用数据:
  - 历史对话记录
  - 用户行为分析
  - 统计报表数据
  - 模型训练数据

访问方式: PostgreSQL只读副本
实时性: < 5s
安全级别: 中（只读权限 + 数据脱敏）
```

#### 4. 配置数据层（配置同步）

```yaml
适用数据:
  - AI策略配置
  - 系统参数设置
  - 模型版本信息
  - 业务规则定义

访问方式: 配置文件 + 版本控制
实时性: < 30s
安全级别: 中（版本控制 + 审计）
```

### 🔀 数据流向设计

```mermaid
sequenceDiagram
    participant CW as Chatwoot主应用
    participant API as 数据API服务
    participant Cache as Redis缓存
    participant DB as 只读副本
    participant MQ as 消息队列
    participant AI as AI微服务
    
    Note over CW,AI: 实时数据同步
    CW->>MQ: 发布数据变更事件
    MQ->>AI: 推送实时更新
    
    Note over CW,AI: 高频数据查询
    AI->>API: 请求用户数据
    API->>Cache: 检查缓存
    
    alt 缓存命中
        Cache->>API: 返回缓存数据
    else 缓存未命中
        API->>DB: 查询数据库
        DB->>API: 返回查询结果
        API->>Cache: 更新缓存
    end
    
    API->>AI: 返回数据
    
    Note over CW,AI: 批量历史数据
    AI->>DB: 直接查询历史数据
    DB->>AI: 返回批量结果
```

---

## 具体数据需求分析

### 📊 数据分类和访问模式

#### 1. 用户画像数据

```yaml
数据内容:
  基本信息:
    - 用户ID、姓名、邮箱、电话
    - 公司信息、职位、地区
    - 注册时间、最后活跃时间
  
  自定义属性:
    - VIP等级、客户分类
    - 产品偏好、购买历史
    - 标签、备注信息
  
  行为特征:
    - 互动频率、响应时间
    - 满意度评分、投诉记录
    - 渠道偏好、设备信息

访问模式:
  频率: 中等（每次对话开始时）
  实时性: 中等（5分钟内）
  数据量: 小（< 10KB per user）
  
访问策略: API接口 + 缓存（1小时TTL）
```

#### 2. 对话历史数据

```yaml
数据内容:
  消息记录:
    - 消息内容、时间戳、发送者
    - 消息类型、附件信息
    - 阅读状态、回复状态
  
  对话元数据:
    - 对话状态、优先级、标签
    - 分配的客服、部门信息
    - 创建时间、更新时间
  
  AI处理记录:
    - 意图识别结果、置信度
    - 执行的动作、处理时间
    - 错误日志、性能指标

访问模式:
  频率: 高（每次AI处理）
  实时性: 高（< 1秒）
  数据量: 中等（10-100KB per conversation）
  
访问策略: 
  - 最近消息：API接口 + 缓存（30分钟TTL）
  - 历史消息：只读副本直接查询
```

#### 3. 配置和策略数据

```yaml
数据内容:
  AI策略配置:
    - 策略名称、描述、版本
    - 触发条件、执行动作
    - 节点配置、工作流定义
  
  系统配置:
    - AI模型参数、API配置
    - 缓存策略、性能阈值
    - 功能开关、权限设置
  
  业务规则:
    - 自动回复模板、关键词库
    - 分类规则、路由策略
    - SLA设置、升级规则

访问模式:
  频率: 低（配置变更时）
  实时性: 中等（30秒内）
  数据量: 小（< 1MB total）
  
访问策略: 配置同步 + 本地缓存（24小时TTL）
```

#### 4. 统计分析数据

```yaml
数据内容:
  实时统计:
    - 在线用户数、活跃对话数
    - 响应时间、处理速度
    - 成功率、错误率
  
  历史统计:
    - 日/周/月统计报表
    - 用户行为分析数据
    - 性能趋势数据
  
  模型训练数据:
    - 标注数据、反馈数据
    - 模型评估指标
    - A/B测试结果

访问模式:
  频率: 低（定时批量处理）
  实时性: 低（小时级别）
  数据量: 大（GB级别）
  
访问策略: 只读副本 + 数据仓库
```

### 📈 数据访问量预估

```yaml
预估指标:
  用户画像查询: 1000 QPS
  对话历史查询: 5000 QPS
  实时消息同步: 2000 msg/s
  配置数据同步: 10 updates/min
  
性能目标:
  API响应时间: P95 < 200ms
  缓存命中率: > 90%
  数据库CPU使用率: < 70%
  消息队列延迟: < 50ms
```

---

## 安全和权限控制

### 🔐 多层安全架构

#### 1. 网络安全层

```yaml
网络隔离:
  - VPC私有网络，服务间内网通信
  - 防火墙规则，限制端口访问
  - SSL/TLS加密，保护数据传输
  - VPN接入，管理员远程访问

访问控制:
  - IP白名单，限制访问来源
  - 端口限制，只开放必要端口
  - 流量监控，检测异常访问
  - DDoS防护，防止攻击
```

#### 2. 应用安全层

```yaml
身份认证:
  - JWT Token认证，支持过期和刷新
  - API Key管理，支持密钥轮换
  - 多因素认证，提高安全级别
  - 单点登录，统一身份管理

权限控制:
  - RBAC角色权限模型
  - 细粒度权限控制（账户、功能、字段级别）
  - 动态权限验证，实时权限检查
  - 权限审计，记录所有权限操作
```

#### 3. 数据安全层

```yaml
数据加密:
  - 传输加密：TLS 1.3
  - 存储加密：AES-256
  - 字段级加密：敏感数据单独加密
  - 密钥管理：使用专用密钥管理服务

数据脱敏:
  - PII数据脱敏：姓名、电话、邮箱
  - 动态脱敏：根据权限级别动态脱敏
  - 格式保持：保持数据格式用于AI处理
  - 审计日志：记录所有脱敏操作
```

### 🛡️ 权限模型设计

#### 权限层次结构

```mermaid
graph TB
    subgraph "权限层次"
        A[系统级权限] --> B[账户级权限]
        B --> C[功能级权限]
        C --> D[数据级权限]
        D --> E[字段级权限]
    end
    
    subgraph "权限类型"
        F[读取权限] --> F1[基础数据读取]
        F --> F2[敏感数据读取]
        F --> F3[历史数据读取]
        
        G[写入权限] --> G1[数据更新]
        G --> G2[配置修改]
        G --> G3[策略执行]
    end
    
    subgraph "权限验证"
        H[JWT Token验证] --> I[权限缓存查询]
        I --> J[动态权限计算]
        J --> K[审计日志记录]
    end
    
    A --> F
    B --> G
    C --> H
```

#### 具体权限实现

```ruby
# 权限验证服务示例
class AiDataAccessPermission
  PERMISSION_LEVELS = {
    basic: 1,      # 基础数据访问
    standard: 2,   # 标准数据访问
    advanced: 3,   # 高级数据访问
    admin: 4       # 管理员权限
  }.freeze

  def initialize(account_id, user_role, ai_service_type)
    @account_id = account_id
    @user_role = user_role
    @ai_service_type = ai_service_type
  end

  def can_access_user_profile?(user_id, fields = [])
    return false unless basic_permission?
    return false unless same_account?(user_id)
    
    sensitive_fields = %w[email phone credit_card ssn]
    requested_sensitive = fields & sensitive_fields
    
    if requested_sensitive.any?
      return advanced_permission? && audit_log_access(user_id, requested_sensitive)
    end
    
    true
  end

  def can_access_conversation_history?(conversation_id, time_range = nil)
    return false unless standard_permission?
    return false unless authorized_conversation?(conversation_id)
    
    # 限制历史数据访问时间范围
    if time_range && time_range.begin < 1.year.ago
      return advanced_permission?
    end
    
    audit_log_access(conversation_id, 'conversation_history')
    true
  end

  def can_execute_strategy?(strategy_id)
    return false unless advanced_permission?
    return false unless authorized_strategy?(strategy_id)
    
    audit_log_access(strategy_id, 'strategy_execution')
    true
  end

  private

  def basic_permission?
    permission_level >= PERMISSION_LEVELS[:basic]
  end

  def standard_permission?
    permission_level >= PERMISSION_LEVELS[:standard]
  end

  def advanced_permission?
    permission_level >= PERMISSION_LEVELS[:advanced]
  end

  def permission_level
    case @user_role
    when 'ai_basic' then PERMISSION_LEVELS[:basic]
    when 'ai_standard' then PERMISSION_LEVELS[:standard]
    when 'ai_advanced' then PERMISSION_LEVELS[:advanced]
    when 'admin' then PERMISSION_LEVELS[:admin]
    else 0
    end
  end

  def same_account?(user_id)
    User.find(user_id).account_id == @account_id
  end

  def authorized_conversation?(conversation_id)
    Conversation.find(conversation_id).account_id == @account_id
  end

  def authorized_strategy?(strategy_id)
    AiStrategy.find(strategy_id).account_id == @account_id
  end

  def audit_log_access(resource_id, action)
    AuditLog.create!(
      account_id: @account_id,
      service_type: @ai_service_type,
      action: action,
      resource_id: resource_id,
      timestamp: Time.current,
      ip_address: request_ip,
      user_agent: request_user_agent
    )
    true
  end
end
```

### 🔍 数据脱敏实现

```ruby
# 数据脱敏服务
class DataMaskingService
  MASKING_RULES = {
    email: ->(email) { email.gsub(/(.{2}).*(@.*)/, '\1***\2') },
    phone: ->(phone) { phone.gsub(/(\d{3})\d{4}(\d{4})/, '\1****\2') },
    name: ->(name) { name.gsub(/(.{1}).*(.{1})/, '\1***\2') },
    credit_card: ->(cc) { cc.gsub(/\d{4}\s?\d{4}\s?\d{4}\s?(\d{4})/, '**** **** **** \1') }
  }.freeze

  def self.mask_user_data(user_data, permission_level)
    return user_data if permission_level >= 3 # 高级权限不脱敏

    masked_data = user_data.dup
    
    MASKING_RULES.each do |field, masking_func|
      if masked_data[field].present?
        masked_data[field] = masking_func.call(masked_data[field])
      end
    end
    
    # 移除高敏感字段
    if permission_level < 2
      masked_data.delete('ssn')
      masked_data.delete('credit_card')
    end
    
    masked_data
  end

  def self.mask_conversation_data(conversation_data, permission_level)
    return conversation_data if permission_level >= 3

    masked_data = conversation_data.dup
    
    # 脱敏消息内容中的敏感信息
    if masked_data['messages']
      masked_data['messages'].each do |message|
        message['content'] = mask_sensitive_content(message['content'])
      end
    end
    
    masked_data
  end

  private

  def self.mask_sensitive_content(content)
    # 使用正则表达式识别和脱敏敏感信息
    content.gsub(/\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/, '**** **** **** ****') # 信用卡号
           .gsub(/\b\d{3}-\d{2}-\d{4}\b/, '***-**-****') # SSN
           .gsub(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, '***@***.***') # 邮箱
  end
end
```

---

## 性能优化策略

### ⚡ 多级缓存架构

#### 1. 缓存层次设计

```mermaid
graph TB
    subgraph "AI微服务缓存层"
        A[L1: 本地内存缓存] --> A1[热点数据]
        A --> A2[计算结果]
        A --> A3[配置信息]
    end
    
    subgraph "共享缓存层"
        B[L2: Redis集群] --> B1[用户画像]
        B --> B2[对话上下文]
        B --> B3[API响应]
    end
    
    subgraph "数据库缓存层"
        C[L3: 查询结果缓存] --> C1[复杂查询]
        C --> C2[聚合统计]
        C --> C3[历史数据]
    end
    
    A --> B
    B --> C
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
```

#### 2. 缓存策略配置

```yaml
缓存配置:
  L1_本地缓存:
    类型: LRU内存缓存
    大小: 512MB
    TTL: 5-30分钟
    适用数据: 热点用户数据、配置信息、计算结果
    
  L2_Redis集群:
    类型: Redis Cluster
    大小: 16GB (4节点 × 4GB)
    TTL: 30分钟-24小时
    适用数据: 用户画像、对话上下文、API缓存
    
  L3_查询缓存:
    类型: PostgreSQL查询缓存
    大小: 8GB
    TTL: 1-6小时
    适用数据: 复杂查询结果、统计数据

缓存更新策略:
  实时更新: 关键数据变更时立即失效缓存
  定时更新: 非关键数据定时刷新缓存
  懒加载: 缓存失效时按需加载
  预热: 系统启动时预加载热点数据
```

### 🔄 读写分离架构

```yaml
数据库架构:
  主库 (Master):
    用途: 写入操作、实时读取
    配置: 高性能SSD、充足内存
    连接数: 限制AI服务连接
    
  只读副本 (Slave):
    用途: AI服务专用查询
    配置: 读优化配置、大内存
    连接数: AI服务专用连接池
    延迟: < 100ms同步延迟
    
  连接池配置:
    AI服务连接池: 20-50连接
    连接超时: 30秒
    查询超时: 60秒
    重试机制: 3次重试，指数退避
```

### 📊 批量处理优化

```typescript
// AI微服务批量数据访问客户端
class BatchDataClient {
  private batchQueue: Map<string, BatchRequest> = new Map();
  private batchTimer: NodeJS.Timeout | null = null;
  private readonly BATCH_SIZE = 100;
  private readonly BATCH_TIMEOUT = 50; // ms

  async getUserProfiles(userIds: string[]): Promise<UserProfile[]> {
    if (userIds.length === 1) {
      return this.getSingleUserProfile(userIds[0]);
    }
    
    return this.batchRequest('user_profiles', userIds);
  }

  async getConversationHistory(conversationIds: string[]): Promise<ConversationHistory[]> {
    return this.batchRequest('conversation_history', conversationIds);
  }

  private async batchRequest<T>(type: string, ids: string[]): Promise<T[]> {
    const batchKey = `${type}_${Date.now()}`;
    const request = new BatchRequest<T>(type, ids);
    
    this.batchQueue.set(batchKey, request);
    
    if (!this.batchTimer) {
      this.batchTimer = setTimeout(() => this.processBatch(), this.BATCH_TIMEOUT);
    }
    
    return request.promise;
  }

  private async processBatch(): Promise<void> {
    const requests = Array.from(this.batchQueue.values());
    this.batchQueue.clear();
    this.batchTimer = null;

    // 按类型分组批量请求
    const groupedRequests = this.groupRequestsByType(requests);
    
    for (const [type, typeRequests] of groupedRequests) {
      await this.executeBatchRequest(type, typeRequests);
    }
  }

  private async executeBatchRequest(type: string, requests: BatchRequest[]): Promise<void> {
    const allIds = requests.flatMap(req => req.ids);
    const chunks = this.chunkArray(allIds, this.BATCH_SIZE);
    
    try {
      const results = await Promise.all(
        chunks.map(chunk => this.callBatchAPI(type, chunk))
      );
      
      const flatResults = results.flat();
      this.distributeResults(requests, flatResults);
    } catch (error) {
      requests.forEach(req => req.reject(error));
    }
  }

  private async callBatchAPI(type: string, ids: string[]): Promise<any[]> {
    const response = await fetch(`${this.apiBaseUrl}/batch/${type}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.authToken}`
      },
      body: JSON.stringify({ ids })
    });

    if (!response.ok) {
      throw new Error(`Batch API error: ${response.status}`);
    }

    return response.json();
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
}

class BatchRequest<T> {
  public promise: Promise<T[]>;
  public resolve!: (value: T[]) => void;
  public reject!: (error: any) => void;

  constructor(public type: string, public ids: string[]) {
    this.promise = new Promise<T[]>((resolve, reject) => {
      this.resolve = resolve;
      this.reject = reject;
    });
  }
}
```

### 🚀 异步处理机制

```typescript
// 异步数据预加载服务
class DataPreloadService {
  private preloadCache = new Map<string, Promise<any>>();
  
  async preloadUserContext(userId: string): Promise<void> {
    const cacheKey = `user_context_${userId}`;
    
    if (this.preloadCache.has(cacheKey)) {
      return this.preloadCache.get(cacheKey);
    }
    
    const preloadPromise = this.loadUserContextData(userId);
    this.preloadCache.set(cacheKey, preloadPromise);
    
    // 设置缓存过期
    setTimeout(() => {
      this.preloadCache.delete(cacheKey);
    }, 300000); // 5分钟过期
    
    return preloadPromise;
  }

  private async loadUserContextData(userId: string): Promise<UserContext> {
    const [profile, recentConversations, preferences] = await Promise.all([
      this.dataClient.getUserProfile(userId),
      this.dataClient.getRecentConversations(userId, 10),
      this.dataClient.getUserPreferences(userId)
    ]);

    const context = {
      profile,
      recentConversations,
      preferences,
      loadedAt: new Date()
    };

    // 存储到缓存
    await this.cacheService.set(`user_context_${userId}`, context, 1800); // 30分钟
    
    return context;
  }

  async predictivePreload(conversationId: string): Promise<void> {
    // 基于对话历史预测可能需要的数据
    const conversation = await this.dataClient.getConversation(conversationId);
    const relatedUsers = this.extractRelatedUsers(conversation);
    
    // 异步预加载相关用户数据
    relatedUsers.forEach(userId => {
      this.preloadUserContext(userId).catch(error => {
        console.warn(`Failed to preload user context for ${userId}:`, error);
      });
    });
  }
}

---

## 总结

本解决方案提供了一个完整的AI微服务数据访问架构，具有以下核心特点：

### 🎯 核心优势

1. **安全性**：多层安全架构，细粒度权限控制，敏感数据脱敏
2. **性能**：多级缓存，读写分离，批量处理，异步加载
3. **可靠性**：故障隔离，重试机制，降级策略，健康检查
4. **可扩展性**：水平扩展，连接池管理，负载均衡

### 🚀 实施建议

1. **分阶段实施**：先实现基础的API接口，再逐步添加缓存和优化
2. **监控先行**：建立完善的监控体系，及时发现性能瓶颈
3. **安全优先**：严格的权限控制和数据脱敏，保护用户隐私
4. **性能测试**：充分的压力测试，确保系统稳定性

这个方案既保证了AI微服务能够高效访问所需数据，又确保了Chatwoot主应用的性能和安全性不受影响。
```
