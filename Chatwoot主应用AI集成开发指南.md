# Chatwoot主应用AI集成开发指南

版本: 1.0  
日期: 2025-08-18  
目标: 指导Chatwoot团队为集成AI微服务做好准备工作，确保两个团队能够并行开发

## 目录
- [项目概述](#项目概述)
- [Chatwoot端功能模块开发](#chatwoot端功能模块开发)
- [API集成接口开发](#api集成接口开发)
- [前端界面改造](#前端界面改造)
- [数据流和状态管理](#数据流和状态管理)
- [配置和部署准备](#配置和部署准备)
- [测试策略](#测试策略)

---

## 项目概述

### 🎯 集成目标

基于微服务架构，Chatwoot主应用需要为集成AI微服务做好准备工作，主要包括：

1. **保持核心功能稳定**：确保现有客服功能不受影响
2. **建立通信桥梁**：开发与AI微服务的通信接口
3. **增强用户界面**：集成策略工作台和AI副驾面板
4. **优化数据流**：确保数据在两个系统间正确同步

### 📋 开发原则

```mermaid
mindmap
  root((Chatwoot集成开发原则))
    稳定性优先
      保持现有功能不变
      渐进式集成
      完善的回滚机制
    接口标准化
      RESTful API设计
      统一错误处理
      版本管理策略
    用户体验
      无缝集成界面
      实时状态反馈
      降级策略保证
    数据一致性
      状态同步机制
      缓存策略
      事务处理
```

### 🔄 集成架构概览

```mermaid
graph TB
    subgraph "Chatwoot主应用"
        A[Rails Web应用] --> B[AI集成控制器]
        B --> C[AI客户端服务]
        C --> D[Webhook处理器]
        
        E[Vue.js前端] --> F[AI界面组件]
        F --> G[WebSocket客户端]
        
        H[数据模型层] --> I[AI相关模型]
        I --> J[状态同步服务]
    end
    
    subgraph "AI微服务"
        K[AI服务API]
        L[WebSocket服务]
        M[Webhook回调]
    end
    
    C -->|HTTP API| K
    G -->|WebSocket| L
    D -->|接收回调| M
    
    style B fill:#e1f5fe
    style F fill:#f3e5f5
    style I fill:#e8f5e8
```

---

## Chatwoot端功能模块开发

### 🏗️ 需要新增的功能模块

#### 1. Controller层新增

```ruby
# app/controllers/api/v1/accounts/ai_controller.rb
class Api::V1::Accounts::AiController < Api::V1::Accounts::BaseController
  before_action :check_ai_feature_enabled
  before_action :fetch_conversation, only: [:process_message, :get_suggestions, :get_status]

  # 处理消息的AI分析请求
  def process_message
    result = ai_service.process_message(
      conversation: @conversation,
      message: message_params,
      context: build_context
    )
    
    render json: result
  rescue AiService::ServiceUnavailableError => e
    render json: { error: 'AI service temporarily unavailable', fallback: true }, status: 503
  end

  # 获取AI建议
  def get_suggestions
    suggestions = ai_service.get_suggestions(
      conversation: @conversation,
      context: build_context
    )
    
    render json: { suggestions: suggestions }
  end

  # 获取AI状态
  def get_status
    status = ai_service.get_conversation_status(@conversation.id)
    render json: status
  end

  private

  def ai_service
    @ai_service ||= AiIntegration::ClientService.new(account: Current.account)
  end

  def message_params
    params.require(:message).permit(:content, :message_type, attachments: [])
  end

  def build_context
    {
      account_id: Current.account.id,
      inbox_id: @conversation.inbox_id,
      contact: @conversation.contact.as_json(only: [:id, :name, :email]),
      agent: Current.user&.as_json(only: [:id, :name]),
      conversation_history: @conversation.messages.recent.limit(10),
      labels: @conversation.labels.pluck(:title)
    }
  end

  def check_ai_feature_enabled
    render json: { error: 'AI features not enabled' }, status: 403 unless Current.account.ai_enabled?
  end
end
```

```ruby
# app/controllers/api/v1/accounts/ai_strategies_controller.rb
class Api::V1::Accounts::AiStrategiesController < Api::V1::Accounts::BaseController
  before_action :fetch_strategy, only: [:show, :update, :destroy, :execute]

  def index
    @strategies = Current.account.ai_strategies.includes(:nodes)
    render json: @strategies, include: :nodes
  end

  def create
    @strategy = Current.account.ai_strategies.build(strategy_params)
    
    if @strategy.save
      render json: @strategy, status: :created
    else
      render json: { errors: @strategy.errors }, status: :unprocessable_entity
    end
  end

  def update
    if @strategy.update(strategy_params)
      # 同步到AI服务
      ai_service.sync_strategy(@strategy)
      render json: @strategy
    else
      render json: { errors: @strategy.errors }, status: :unprocessable_entity
    end
  end

  def execute
    result = ai_service.execute_strategy(
      strategy_id: @strategy.id,
      trigger_data: params[:trigger_data],
      context: params[:context]
    )
    
    render json: result
  end

  private

  def fetch_strategy
    @strategy = Current.account.ai_strategies.find(params[:id])
  end

  def strategy_params
    params.require(:ai_strategy).permit(
      :name, :description, :is_active,
      nodes_attributes: [:id, :name, :position, :triggers, :goals, :tools, :_destroy],
      dimensions_attributes: [:id, :name, :type, :config, :_destroy]
    )
  end

  def ai_service
    @ai_service ||= AiIntegration::StrategyService.new(account: Current.account)
  end
end
```

```ruby
# app/controllers/webhooks/ai_controller.rb
class Webhooks::AiController < ApplicationController
  skip_before_action :authenticate_user!
  skip_before_action :verify_authenticity_token
  before_action :verify_webhook_signature

  def receive
    case params[:event_type]
    when 'ai.processing.completed'
      handle_processing_completed
    when 'ai.suggestion.ready'
      handle_suggestion_ready
    when 'ai.strategy.triggered'
      handle_strategy_triggered
    when 'ai.error.occurred'
      handle_error_occurred
    else
      render json: { error: 'Unknown event type' }, status: :bad_request
    end
  end

  private

  def handle_processing_completed
    conversation = Conversation.find(params[:conversation_id])
    result = params[:result]
    
    # 执行AI建议的操作
    AiIntegration::ActionExecutorService.new(conversation, result).execute
    
    # 广播状态更新
    broadcast_ai_status_update(conversation, 'completed', result)
    
    render json: { status: 'processed' }
  end

  def handle_suggestion_ready
    conversation = Conversation.find(params[:conversation_id])
    suggestions = params[:suggestions]
    
    # 通过WebSocket推送建议到前端
    ActionCable.server.broadcast(
      "conversation_#{conversation.id}",
      {
        type: 'ai_suggestions',
        data: suggestions
      }
    )
    
    render json: { status: 'broadcasted' }
  end

  def verify_webhook_signature
    signature = request.headers['X-AI-Signature']
    payload = request.raw_post
    
    unless AiIntegration::WebhookVerifier.verify(payload, signature)
      render json: { error: 'Invalid signature' }, status: :unauthorized
    end
  end

  def broadcast_ai_status_update(conversation, status, data = {})
    ActionCable.server.broadcast(
      "conversation_#{conversation.id}",
      {
        type: 'ai_status_update',
        data: {
          status: status,
          timestamp: Time.current,
          **data
        }
      }
    )
  end
end
```

#### 2. Service层新增

```ruby
# app/services/ai_integration/client_service.rb
class AiIntegration::ClientService
  include HTTParty
  
  base_uri ENV['AI_SERVICE_URL']
  
  def initialize(account:)
    @account = account
    @headers = {
      'Content-Type' => 'application/json',
      'Authorization' => "Bearer #{generate_jwt_token}",
      'X-Account-ID' => account.id.to_s
    }
  end

  def process_message(conversation:, message:, context:)
    response = self.class.post(
      "/api/v1/conversations/#{conversation.id}/process",
      body: {
        message: message,
        context: context
      }.to_json,
      headers: @headers,
      timeout: 30
    )
    
    handle_response(response)
  end

  def get_suggestions(conversation:, context:)
    response = self.class.get(
      "/api/v1/conversations/#{conversation.id}/suggestions",
      query: { context: context.to_json },
      headers: @headers,
      timeout: 10
    )
    
    handle_response(response)
  end

  def get_conversation_status(conversation_id)
    response = self.class.get(
      "/api/v1/conversations/#{conversation_id}/status",
      headers: @headers,
      timeout: 5
    )
    
    handle_response(response)
  end

  def sync_strategy(strategy)
    response = self.class.post(
      "/api/v1/strategies/sync",
      body: strategy.to_ai_format.to_json,
      headers: @headers,
      timeout: 15
    )
    
    handle_response(response)
  end

  private

  def generate_jwt_token
    payload = {
      account_id: @account.id,
      iat: Time.current.to_i,
      exp: 1.hour.from_now.to_i
    }
    
    JWT.encode(payload, ENV['AI_SERVICE_JWT_SECRET'], 'HS256')
  end

  def handle_response(response)
    case response.code
    when 200..299
      JSON.parse(response.body)
    when 503
      raise AiService::ServiceUnavailableError, 'AI service is temporarily unavailable'
    when 429
      raise AiService::RateLimitError, 'Rate limit exceeded'
    else
      raise AiService::APIError, "AI service error: #{response.code} - #{response.body}"
    end
  rescue JSON::ParserError
    raise AiService::InvalidResponseError, 'Invalid JSON response from AI service'
  end
end
```

```ruby
# app/services/ai_integration/action_executor_service.rb
class AiIntegration::ActionExecutorService
  def initialize(conversation, ai_result)
    @conversation = conversation
    @ai_result = ai_result
  end

  def execute
    return unless @ai_result['success'] && @ai_result['actions']

    @ai_result['actions'].each do |action|
      execute_action(action)
    end
  end

  private

  def execute_action(action)
    case action['type']
    when 'send_reply'
      send_reply(action['data'])
    when 'add_labels'
      add_labels(action['data']['labels'])
    when 'transfer_conversation'
      transfer_conversation(action['data']['agent_id'])
    when 'create_note'
      create_note(action['data']['content'])
    when 'update_contact'
      update_contact(action['data'])
    else
      Rails.logger.warn "Unknown AI action type: #{action['type']}"
    end
  rescue => e
    Rails.logger.error "Failed to execute AI action #{action['type']}: #{e.message}"
  end

  def send_reply(data)
    message = @conversation.messages.create!(
      content: data['content'],
      message_type: :outgoing,
      sender: ai_bot_user,
      content_attributes: {
        ai_generated: true,
        confidence: data['confidence']
      }
    )
    
    # 触发消息发送到渠道
    message.save!
  end

  def add_labels(labels)
    labels.each do |label_name|
      label = @conversation.account.labels.find_or_create_by(title: label_name)
      @conversation.labels << label unless @conversation.labels.include?(label)
    end
  end

  def transfer_conversation(agent_id)
    agent = @conversation.account.users.find(agent_id)
    @conversation.update!(assignee: agent)
  end

  def create_note(content)
    @conversation.messages.create!(
      content: content,
      message_type: :activity,
      sender: ai_bot_user,
      private: true
    )
  end

  def update_contact(data)
    @conversation.contact.update!(data.slice('name', 'email', 'phone_number'))
    
    # 更新自定义属性
    if data['custom_attributes']
      @conversation.contact.custom_attributes.merge!(data['custom_attributes'])
      @conversation.contact.save!
    end
  end

  def ai_bot_user
    @ai_bot_user ||= @conversation.account.users.find_by(email: '<EMAIL>') ||
                     create_ai_bot_user
  end

  def create_ai_bot_user
    @conversation.account.users.create!(
      name: 'AI Assistant',
      email: '<EMAIL>',
      password: SecureRandom.hex(20),
      role: :agent,
      account_users_attributes: [{ role: :agent }]
    )
  end
end
```

#### 3. Model层新增

```ruby
# app/models/ai_strategy.rb
class AiStrategy < ApplicationRecord
  belongs_to :account
  has_many :ai_strategy_nodes, dependent: :destroy
  has_many :ai_strategy_dimensions, dependent: :destroy
  has_many :ai_execution_logs, dependent: :destroy

  accepts_nested_attributes_for :ai_strategy_nodes, allow_destroy: true
  accepts_nested_attributes_for :ai_strategy_dimensions, allow_destroy: true

  validates :name, presence: true, length: { maximum: 255 }
  validates :account, presence: true

  scope :active, -> { where(is_active: true) }
  scope :recent, -> { order(updated_at: :desc) }

  def to_ai_format
    {
      id: id,
      name: name,
      description: description,
      is_active: is_active,
      nodes: ai_strategy_nodes.map(&:to_ai_format),
      dimensions: ai_strategy_dimensions.map(&:to_ai_format),
      created_at: created_at,
      updated_at: updated_at
    }
  end

  def execution_stats
    {
      total_executions: ai_execution_logs.count,
      success_rate: ai_execution_logs.successful.count.to_f / ai_execution_logs.count,
      avg_execution_time: ai_execution_logs.average(:execution_time),
      last_executed: ai_execution_logs.maximum(:created_at)
    }
  end
end
```

```ruby
# app/models/ai_strategy_node.rb
class AiStrategyNode < ApplicationRecord
  belongs_to :ai_strategy

  validates :name, presence: true
  validates :position, presence: true, uniqueness: { scope: :ai_strategy_id }

  serialize :triggers, JSON
  serialize :goals, JSON
  serialize :tools, JSON
  serialize :data_columns, JSON

  scope :ordered, -> { order(:position) }

  def to_ai_format
    {
      id: id,
      name: name,
      position: position,
      triggers: triggers || [],
      goals: goals || [],
      tools: tools || [],
      data_columns: data_columns || []
    }
  end

  def current_users_count
    # 实现获取当前停留在此节点的用户数量的逻辑
    # 这里需要根据实际的用户旅程跟踪机制来实现
    0
  end

  def conversion_rate
    # 实现转化率计算逻辑
    # 这里需要根据实际的转化跟踪机制来实现
    0.0
  end
end
```

```ruby
# app/models/ai_execution_log.rb
class AiExecutionLog < ApplicationRecord
  belongs_to :ai_strategy
  belongs_to :conversation
  belongs_to :triggered_by, class_name: 'User', optional: true

  validates :action_type, presence: true
  validates :status, presence: true

  serialize :input_data, JSON
  serialize :output_data, JSON

  enum status: { pending: 0, completed: 1, failed: 2 }

  scope :successful, -> { where(status: :completed) }
  scope :recent, -> { order(created_at: :desc) }

  def execution_time_ms
    (execution_time * 1000).round(2) if execution_time
  end
end
```

#### 4. 数据库迁移

```ruby
# db/migrate/20250818000001_create_ai_strategies.rb
class CreateAiStrategies < ActiveRecord::Migration[7.0]
  def change
    create_table :ai_strategies do |t|
      t.references :account, null: false, foreign_key: true
      t.string :name, null: false
      t.text :description
      t.boolean :is_active, default: true
      t.integer :version, default: 1
      t.json :metadata, default: {}

      t.timestamps
    end

    add_index :ai_strategies, [:account_id, :name], unique: true
    add_index :ai_strategies, :is_active
  end
end
```

```ruby
# db/migrate/20250818000002_create_ai_strategy_nodes.rb
class CreateAiStrategyNodes < ActiveRecord::Migration[7.0]
  def change
    create_table :ai_strategy_nodes do |t|
      t.references :ai_strategy, null: false, foreign_key: true
      t.string :name, null: false
      t.integer :position, null: false
      t.json :triggers, default: []
      t.json :goals, default: []
      t.json :tools, default: []
      t.json :data_columns, default: []

      t.timestamps
    end

    add_index :ai_strategy_nodes, [:ai_strategy_id, :position], unique: true
  end
end
```

```ruby
# db/migrate/20250818000003_create_ai_execution_logs.rb
class CreateAiExecutionLogs < ActiveRecord::Migration[7.0]
  def change
    create_table :ai_execution_logs do |t|
      t.references :ai_strategy, null: false, foreign_key: true
      t.references :conversation, null: false, foreign_key: true
      t.references :triggered_by, null: true, foreign_key: { to_table: :users }
      t.string :action_type, null: false
      t.json :input_data, default: {}
      t.json :output_data, default: {}
      t.integer :status, default: 0
      t.decimal :execution_time, precision: 8, scale: 3
      t.text :error_message

      t.timestamps
    end

    add_index :ai_execution_logs, [:ai_strategy_id, :created_at]
    add_index :ai_execution_logs, [:conversation_id, :created_at]
    add_index :ai_execution_logs, :status
  end
end
```

```ruby
# db/migrate/20250818000004_add_ai_features_to_accounts.rb
class AddAiFeaturesToAccounts < ActiveRecord::Migration[7.0]
  def change
    add_column :accounts, :ai_enabled, :boolean, default: false
    add_column :accounts, :ai_settings, :json, default: {}
    
    add_index :accounts, :ai_enabled
  end
end
```

#### 5. 需要修改的现有代码

```ruby
# app/models/account.rb - 添加AI相关关联
class Account < ApplicationRecord
  # 现有代码...
  
  # 新增AI相关关联
  has_many :ai_strategies, dependent: :destroy
  has_many :ai_execution_logs, dependent: :destroy
  
  # 新增AI功能检查方法
  def ai_enabled?
    ai_enabled && ai_service_configured?
  end
  
  def ai_service_configured?
    ENV['AI_SERVICE_URL'].present? && ENV['AI_SERVICE_JWT_SECRET'].present?
  end
  
  # 新增AI设置管理
  def ai_setting(key)
    ai_settings&.dig(key.to_s)
  end
  
  def update_ai_setting(key, value)
    self.ai_settings = (ai_settings || {}).merge(key.to_s => value)
    save!
  end
end
```

```ruby
# app/models/conversation.rb - 添加AI状态跟踪
class Conversation < ApplicationRecord
  # 现有代码...
  
  # 新增AI相关关联
  has_many :ai_execution_logs, dependent: :destroy
  has_one :current_ai_session, -> { where(status: 'active') }, 
          class_name: 'AiExecutionLog'
  
  # 新增AI状态方法
  def ai_processing?
    current_ai_session&.status == 'pending'
  end
  
  def last_ai_interaction
    ai_execution_logs.completed.order(:created_at).last
  end
  
  def ai_insights
    return {} unless last_ai_interaction
    
    last_ai_interaction.output_data&.dig('ai_insights') || {}
  end
end
```

```ruby
# app/models/message.rb - 添加AI处理触发
class Message < ApplicationRecord
  # 现有代码...
  
  after_create_commit :trigger_ai_processing, if: :should_trigger_ai?
  
  private
  
  def should_trigger_ai?
    incoming? && 
    conversation.account.ai_enabled? && 
    !conversation.ai_processing? &&
    content.present?
  end
  
  def trigger_ai_processing
    AiIntegration::MessageProcessorJob.perform_later(self)
  end
end

---

## API集成接口开发

### 🔌 AI服务客户端集成

#### 1. HTTP客户端配置

```ruby
# config/initializers/ai_service.rb
Rails.application.configure do
  config.ai_service = ActiveSupport::OrderedOptions.new
  config.ai_service.url = ENV.fetch('AI_SERVICE_URL', 'http://localhost:4000')
  config.ai_service.timeout = ENV.fetch('AI_SERVICE_TIMEOUT', '30').to_i
  config.ai_service.jwt_secret = ENV.fetch('AI_SERVICE_JWT_SECRET')
  config.ai_service.webhook_secret = ENV.fetch('AI_WEBHOOK_SECRET')
  config.ai_service.retry_attempts = ENV.fetch('AI_SERVICE_RETRY_ATTEMPTS', '3').to_i
  config.ai_service.retry_delay = ENV.fetch('AI_SERVICE_RETRY_DELAY', '1').to_i
end

# 健康检查配置
Rails.application.config.after_initialize do
  if Rails.env.production?
    AiIntegration::HealthChecker.start_monitoring
  end
end
```

#### 2. 错误处理和降级策略

```ruby
# app/services/ai_integration/error_handler.rb
class AiIntegration::ErrorHandler
  class << self
    def handle_error(error, context = {})
      case error
      when AiService::ServiceUnavailableError
        handle_service_unavailable(context)
      when AiService::RateLimitError
        handle_rate_limit(context)
      when AiService::TimeoutError
        handle_timeout(context)
      else
        handle_generic_error(error, context)
      end
    end

    private

    def handle_service_unavailable(context)
      Rails.logger.warn "AI service unavailable for conversation #{context[:conversation_id]}"

      # 启用降级模式
      fallback_response = {
        success: false,
        fallback: true,
        message: 'AI服务暂时不可用，已切换到基础模式',
        actions: generate_fallback_actions(context)
      }

      # 记录降级事件
      track_fallback_event('service_unavailable', context)

      fallback_response
    end

    def handle_rate_limit(context)
      Rails.logger.warn "AI service rate limit exceeded for account #{context[:account_id]}"

      # 延迟处理
      AiIntegration::MessageProcessorJob.set(wait: 60.seconds).perform_later(
        context[:message_id]
      )

      {
        success: false,
        retry_after: 60,
        message: '请求过于频繁，请稍后再试'
      }
    end

    def handle_timeout(context)
      Rails.logger.error "AI service timeout for conversation #{context[:conversation_id]}"

      # 异步重试
      AiIntegration::MessageProcessorJob.set(wait: 30.seconds).perform_later(
        context[:message_id]
      )

      {
        success: false,
        message: 'AI处理超时，正在后台重试'
      }
    end

    def generate_fallback_actions(context)
      conversation = Conversation.find(context[:conversation_id])

      # 基于规则的简单回复
      if conversation.messages.count == 1
        [{
          type: 'send_reply',
          data: {
            content: '您好！感谢您的咨询，我们的客服人员会尽快为您处理。',
            confidence: 0.5
          }
        }]
      else
        [{
          type: 'transfer_to_human',
          data: {
            reason: 'AI服务不可用，转接人工客服'
          }
        }]
      end
    end

    def track_fallback_event(reason, context)
      Rails.logger.info "AI fallback triggered: #{reason}", context

      # 可以集成到监控系统
      if defined?(Prometheus)
        Prometheus.ai_fallback_counter.increment(
          labels: { reason: reason, account_id: context[:account_id] }
        )
      end
    end
  end
end
```

#### 3. 重试机制和熔断器

```ruby
# app/services/ai_integration/circuit_breaker.rb
class AiIntegration::CircuitBreaker
  include Singleton

  FAILURE_THRESHOLD = 5
  RECOVERY_TIMEOUT = 60.seconds
  MONITORING_PERIOD = 10.seconds

  def initialize
    @failure_count = 0
    @last_failure_time = nil
    @state = :closed # :closed, :open, :half_open
    @mutex = Mutex.new
  end

  def call(&block)
    @mutex.synchronize do
      case @state
      when :closed
        execute_with_monitoring(&block)
      when :open
        if should_attempt_reset?
          @state = :half_open
          execute_with_monitoring(&block)
        else
          raise AiService::CircuitBreakerOpenError, 'Circuit breaker is open'
        end
      when :half_open
        execute_with_monitoring(&block)
      end
    end
  end

  def failure_count
    @failure_count
  end

  def state
    @state
  end

  private

  def execute_with_monitoring(&block)
    result = block.call
    on_success
    result
  rescue => error
    on_failure
    raise error
  end

  def on_success
    @failure_count = 0
    @state = :closed
  end

  def on_failure
    @failure_count += 1
    @last_failure_time = Time.current

    if @failure_count >= FAILURE_THRESHOLD
      @state = :open
      Rails.logger.error "AI service circuit breaker opened after #{@failure_count} failures"
    end
  end

  def should_attempt_reset?
    Time.current - @last_failure_time > RECOVERY_TIMEOUT
  end
end
```

#### 4. WebSocket集成

```ruby
# app/channels/ai_channel.rb
class AiChannel < ApplicationCable::Channel
  def subscribed
    conversation = find_conversation
    return reject unless conversation && authorized_for_conversation?(conversation)

    stream_from "ai_updates_#{conversation.id}"

    # 发送当前AI状态
    transmit({
      type: 'ai_status',
      data: get_current_ai_status(conversation)
    })
  end

  def unsubscribed
    # 清理工作
  end

  def request_suggestions(data)
    conversation = find_conversation
    return unless conversation && authorized_for_conversation?(conversation)

    # 异步获取AI建议
    AiIntegration::SuggestionGeneratorJob.perform_later(
      conversation.id,
      data['context'],
      current_user.id
    )
  end

  def update_preferences(data)
    return unless current_user

    # 更新用户的AI偏好设置
    current_user.update_ai_preferences(data['preferences'])

    transmit({
      type: 'preferences_updated',
      data: { success: true }
    })
  end

  private

  def find_conversation
    Conversation.find_by(id: params[:conversation_id])
  end

  def authorized_for_conversation?(conversation)
    conversation.account == current_user.account &&
    current_user.accessible_inboxes.include?(conversation.inbox)
  end

  def get_current_ai_status(conversation)
    {
      processing: conversation.ai_processing?,
      last_interaction: conversation.last_ai_interaction&.created_at,
      insights: conversation.ai_insights
    }
  end
end
```

#### 5. 后台任务处理

```ruby
# app/jobs/ai_integration/message_processor_job.rb
class AiIntegration::MessageProcessorJob < ApplicationJob
  queue_as :ai_processing

  retry_on AiService::ServiceUnavailableError, wait: :exponentially_longer, attempts: 3
  retry_on AiService::TimeoutError, wait: 30.seconds, attempts: 2

  discard_on AiService::InvalidRequestError

  def perform(message_id)
    message = Message.find(message_id)
    conversation = message.conversation

    return unless should_process?(message, conversation)

    # 使用熔断器保护AI服务调用
    result = AiIntegration::CircuitBreaker.instance.call do
      ai_service = AiIntegration::ClientService.new(account: conversation.account)
      ai_service.process_message(
        conversation: conversation,
        message: message.as_json,
        context: build_context(conversation, message)
      )
    end

    # 处理AI响应
    if result['success']
      AiIntegration::ActionExecutorService.new(conversation, result).execute
      broadcast_ai_completion(conversation, result)
    else
      handle_ai_failure(conversation, result)
    end

  rescue AiService::CircuitBreakerOpenError
    Rails.logger.warn "AI circuit breaker open for conversation #{conversation.id}"
    handle_circuit_breaker_open(conversation)
  rescue => error
    Rails.logger.error "AI processing failed for message #{message_id}: #{error.message}"
    handle_processing_error(conversation, error)
  end

  private

  def should_process?(message, conversation)
    message.incoming? &&
    conversation.account.ai_enabled? &&
    !conversation.ai_processing?
  end

  def build_context(conversation, message)
    {
      account_id: conversation.account_id,
      inbox_id: conversation.inbox_id,
      contact: conversation.contact.as_json(only: [:id, :name, :email]),
      conversation_history: conversation.messages.recent.limit(10).as_json,
      labels: conversation.labels.pluck(:title),
      current_message: message.as_json
    }
  end

  def broadcast_ai_completion(conversation, result)
    ActionCable.server.broadcast(
      "ai_updates_#{conversation.id}",
      {
        type: 'ai_processing_completed',
        data: {
          conversation_id: conversation.id,
          result: result,
          timestamp: Time.current
        }
      }
    )
  end

  def handle_circuit_breaker_open(conversation)
    # 转接到人工客服
    conversation.update!(
      status: :open,
      assignee: find_available_agent(conversation.inbox)
    )

    # 通知前端
    ActionCable.server.broadcast(
      "ai_updates_#{conversation.id}",
      {
        type: 'ai_service_unavailable',
        data: {
          message: 'AI服务暂时不可用，已转接人工客服',
          fallback_mode: true
        }
      }
    )
  end

  def find_available_agent(inbox)
    inbox.members.online.order('RANDOM()').first
  end
end
```

---

## 前端界面改造

### 🎨 Vue.js组件开发

#### 1. AI副驾面板主组件

```vue
<!-- app/javascript/dashboard/components/AICopilot/AICopilotPanel.vue -->
<template>
  <div class="ai-copilot-panel">
    <!-- 状态栏 -->
    <div class="ai-status-bar">
      <div class="status-indicator" :class="aiStatus">
        <i :class="statusIcon"></i>
        <span>{{ statusText }}</span>
      </div>
      <div class="confidence-score" v-if="currentConfidence">
        置信度: {{ Math.round(currentConfidence * 100) }}%
      </div>
    </div>

    <!-- 动态旅程罗盘 -->
    <AiJourneyCompass
      v-if="showJourneyCompass"
      :journey-nodes="journeyNodes"
      :current-node="currentNode"
      :predicted-path="predictedPath"
      @node-click="handleNodeClick"
    />

    <!-- 智能回复建议 -->
    <AiReplySuggestions
      v-if="showSuggestions"
      :suggestions="suggestions"
      :loading="suggestionsLoading"
      @apply-suggestion="applySuggestion"
      @optimize-suggestion="optimizeSuggestion"
      @request-suggestions="requestSuggestions"
    />

    <!-- 实时意图透镜 -->
    <AiIntentLens
      v-if="showIntentLens"
      :current-intent="currentIntent"
      :intent-history="intentHistory"
      :confidence="intentConfidence"
    />

    <!-- 任务执行清单 -->
    <AiTaskList
      v-if="showTaskList"
      :tasks="executionTasks"
      @execute-task="executeTask"
      @pause-task="pauseTask"
      @handover-task="handoverTask"
    />

    <!-- 个性化设置 -->
    <AiPersonalizationSettings
      v-if="showSettings"
      :preferences="userPreferences"
      @update-preferences="updatePreferences"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import AiJourneyCompass from './components/AiJourneyCompass.vue'
import AiReplySuggestions from './components/AiReplySuggestions.vue'
import AiIntentLens from './components/AiIntentLens.vue'
import AiTaskList from './components/AiTaskList.vue'
import AiPersonalizationSettings from './components/AiPersonalizationSettings.vue'
import { useAiWebSocket } from '../composables/useAiWebSocket'

export default {
  name: 'AICopilotPanel',
  components: {
    AiJourneyCompass,
    AiReplySuggestions,
    AiIntentLens,
    AiTaskList,
    AiPersonalizationSettings
  },
  props: {
    conversationId: {
      type: Number,
      required: true
    }
  },
  setup(props) {
    const {
      aiStatus,
      currentConfidence,
      suggestions,
      currentIntent,
      executionTasks,
      connectToAi,
      requestSuggestions,
      executeTask
    } = useAiWebSocket(props.conversationId)

    return {
      aiStatus,
      currentConfidence,
      suggestions,
      currentIntent,
      executionTasks,
      connectToAi,
      requestSuggestions,
      executeTask
    }
  },
  data() {
    return {
      suggestionsLoading: false,
      journeyNodes: [],
      currentNode: null,
      predictedPath: [],
      intentHistory: [],
      intentConfidence: 0,
      userPreferences: {}
    }
  },
  computed: {
    ...mapGetters({
      currentUser: 'getCurrentUser',
      currentAccount: 'getCurrentAccount'
    }),
    statusIcon() {
      const icons = {
        idle: 'ion-pause',
        thinking: 'ion-refresh',
        processing: 'ion-gear-a',
        error: 'ion-alert-circled'
      }
      return icons[this.aiStatus] || 'ion-help'
    },
    statusText() {
      const texts = {
        idle: '待机中',
        thinking: '思考中',
        processing: '处理中',
        error: '异常'
      }
      return texts[this.aiStatus] || '未知状态'
    },
    showJourneyCompass() {
      return this.userPreferences.showJourneyCompass !== false
    },
    showSuggestions() {
      return this.userPreferences.showSuggestions !== false
    },
    showIntentLens() {
      return this.userPreferences.showIntentLens !== false
    },
    showTaskList() {
      return this.userPreferences.showTaskList !== false
    },
    showSettings() {
      return this.userPreferences.showSettings === true
    }
  },
  mounted() {
    this.initializeAiCopilot()
    this.loadUserPreferences()
  },
  methods: {
    async initializeAiCopilot() {
      try {
        // 连接AI WebSocket
        await this.connectToAi()

        // 加载初始数据
        await this.loadJourneyData()
        await this.loadIntentHistory()
      } catch (error) {
        console.error('Failed to initialize AI copilot:', error)
        this.$toast.error('AI助手初始化失败')
      }
    },

    async loadUserPreferences() {
      try {
        const response = await this.$http.get('/api/v1/profile/ai_preferences')
        this.userPreferences = response.data.preferences || {}
      } catch (error) {
        console.error('Failed to load AI preferences:', error)
      }
    },

    async loadJourneyData() {
      try {
        const response = await this.$http.get(
          `/api/v1/accounts/${this.currentAccount.id}/conversations/${this.conversationId}/journey`
        )
        this.journeyNodes = response.data.nodes || []
        this.currentNode = response.data.current_node
        this.predictedPath = response.data.predicted_path || []
      } catch (error) {
        console.error('Failed to load journey data:', error)
      }
    },

    async loadIntentHistory() {
      try {
        const response = await this.$http.get(
          `/api/v1/accounts/${this.currentAccount.id}/conversations/${this.conversationId}/intent_history`
        )
        this.intentHistory = response.data.history || []
      } catch (error) {
        console.error('Failed to load intent history:', error)
      }
    },

    async applySuggestion(suggestion) {
      try {
        // 将建议应用到消息输入框
        this.$emit('apply-suggestion', suggestion.content)

        // 记录使用统计
        await this.trackSuggestionUsage(suggestion, 'applied')
      } catch (error) {
        console.error('Failed to apply suggestion:', error)
        this.$toast.error('应用建议失败')
      }
    },

    async optimizeSuggestion(suggestion) {
      try {
        this.suggestionsLoading = true

        const response = await this.$http.post(
          `/api/v1/accounts/${this.currentAccount.id}/ai/optimize_suggestion`,
          {
            suggestion: suggestion,
            context: this.buildOptimizationContext()
          }
        )

        const optimizedSuggestion = response.data.optimized_suggestion
        this.$emit('apply-suggestion', optimizedSuggestion.content)

        // 显示优化提示
        if (optimizedSuggestion.optimization_tip) {
          this.$toast.info(optimizedSuggestion.optimization_tip)
        }

        await this.trackSuggestionUsage(suggestion, 'optimized')
      } catch (error) {
        console.error('Failed to optimize suggestion:', error)
        this.$toast.error('优化建议失败')
      } finally {
        this.suggestionsLoading = false
      }
    },

    async updatePreferences(newPreferences) {
      try {
        await this.$http.put('/api/v1/profile/ai_preferences', {
          preferences: newPreferences
        })

        this.userPreferences = { ...this.userPreferences, ...newPreferences }
        this.$toast.success('偏好设置已更新')
      } catch (error) {
        console.error('Failed to update preferences:', error)
        this.$toast.error('更新偏好设置失败')
      }
    },

    handleNodeClick(node) {
      // 显示节点详细信息
      this.$emit('show-node-details', node)
    },

    async pauseTask(task) {
      try {
        await this.$http.post(
          `/api/v1/accounts/${this.currentAccount.id}/ai/tasks/${task.id}/pause`
        )
        this.$toast.success('任务已暂停')
      } catch (error) {
        console.error('Failed to pause task:', error)
        this.$toast.error('暂停任务失败')
      }
    },

    async handoverTask(task) {
      try {
        await this.$http.post(
          `/api/v1/accounts/${this.currentAccount.id}/ai/tasks/${task.id}/handover`,
          { agent_id: this.currentUser.id }
        )
        this.$toast.success('任务已转为人工处理')
      } catch (error) {
        console.error('Failed to handover task:', error)
        this.$toast.error('任务转接失败')
      }
    },

    buildOptimizationContext() {
      return {
        conversation_id: this.conversationId,
        current_intent: this.currentIntent,
        user_preferences: this.userPreferences,
        conversation_context: this.getConversationContext()
      }
    },

    getConversationContext() {
      // 从store或props获取对话上下文
      return {
        contact_info: this.$store.getters.getCurrentContact,
        recent_messages: this.$store.getters.getRecentMessages,
        labels: this.$store.getters.getConversationLabels
      }
    },

    async trackSuggestionUsage(suggestion, action) {
      try {
        await this.$http.post('/api/v1/analytics/ai_suggestion_usage', {
          suggestion_id: suggestion.id,
          action: action,
          conversation_id: this.conversationId,
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        // 静默失败，不影响用户体验
        console.warn('Failed to track suggestion usage:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-copilot-panel {
  width: 320px;
  height: 100%;
  background: #ffffff;
  border-left: 1px solid #e0e6ed;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .ai-status-bar {
    padding: 12px 16px;
    background: #f8fafc;
    border-bottom: 1px solid #e0e6ed;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 500;

      &.idle {
        color: #64748b;
      }

      &.thinking {
        color: #3b82f6;

        i {
          animation: spin 1s linear infinite;
        }
      }

      &.processing {
        color: #10b981;

        i {
          animation: spin 1s linear infinite;
        }
      }

      &.error {
        color: #ef4444;
      }
    }

    .confidence-score {
      font-size: 12px;
      color: #64748b;
      background: #e2e8f0;
      padding: 2px 8px;
      border-radius: 12px;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
```

#### 2. 策略工作台组件

```vue
<!-- app/javascript/dashboard/components/StrategyWorkbench/StrategyWorkbench.vue -->
<template>
  <div class="strategy-workbench">
    <!-- 工作台头部 -->
    <div class="workbench-header">
      <div class="header-left">
        <h2>{{ workbench.name || '策略工作台' }}</h2>
        <div class="workbench-stats">
          <span class="stat-item">
            <i class="ion-person"></i>
            {{ totalUsers }} 用户
          </span>
          <span class="stat-item">
            <i class="ion-stats-bars"></i>
            {{ averageConversionRate }}% 转化率
          </span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="saveWorkbench" class="btn btn-primary" :disabled="saving">
          <i class="ion-checkmark" v-if="!saving"></i>
          <i class="ion-load-c spinning" v-else></i>
          {{ saving ? '保存中...' : '保存策略' }}
        </button>
        <button @click="testWorkbench" class="btn btn-secondary">
          <i class="ion-play"></i>
          测试运行
        </button>
        <button @click="showTemplates" class="btn btn-outline">
          <i class="ion-folder"></i>
          模板中心
        </button>
        <button @click="showSettings" class="btn btn-outline">
          <i class="ion-gear-a"></i>
          设置
        </button>
      </div>
    </div>

    <!-- 多维表格 -->
    <div class="strategy-table-container" ref="tableContainer">
      <table class="strategy-table" :style="tableStyle">
        <thead>
          <tr>
            <th class="node-column sticky-column">
              <div class="column-header">
                <span>运营节点</span>
                <button @click="addNode" class="add-btn">
                  <i class="ion-plus"></i>
                </button>
              </div>
            </th>
            <th v-for="dimension in dimensions"
                :key="dimension.id"
                class="dimension-column"
                :style="{ minWidth: dimension.width + 'px' }">
              <div class="column-header">
                <span>{{ dimension.name }}</span>
                <div class="column-actions">
                  <button @click="editDimension(dimension)" class="edit-btn">
                    <i class="ion-edit"></i>
                  </button>
                  <button @click="deleteDimension(dimension)" class="delete-btn">
                    <i class="ion-trash-a"></i>
                  </button>
                </div>
              </div>
            </th>
            <th class="actions-column">
              <button @click="addDimension" class="add-btn">
                <i class="ion-plus"></i>
                添加列
              </button>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(node, index) in nodes"
              :key="node.id"
              class="node-row"
              :class="{ 'active': node.id === activeNodeId }">
            <td class="node-cell sticky-column">
              <div class="node-info">
                <div class="node-header">
                  <input
                    v-model="node.name"
                    class="node-name-input"
                    @blur="updateNode(node)"
                    placeholder="节点名称"
                  />
                  <div class="node-actions">
                    <button @click="moveNodeUp(index)" :disabled="index === 0" class="move-btn">
                      <i class="ion-chevron-up"></i>
                    </button>
                    <button @click="moveNodeDown(index)" :disabled="index === nodes.length - 1" class="move-btn">
                      <i class="ion-chevron-down"></i>
                    </button>
                    <button @click="deleteNode(node)" class="delete-btn">
                      <i class="ion-trash-a"></i>
                    </button>
                  </div>
                </div>
                <div class="node-stats">
                  <span class="user-count">
                    <i class="ion-person"></i>
                    {{ node.currentUsers || 0 }}人
                  </span>
                  <span class="conversion-rate" :class="getConversionRateClass(node.conversionRate)">
                    <i class="ion-stats-bars"></i>
                    {{ (node.conversionRate || 0).toFixed(1) }}%
                  </span>
                </div>
              </div>
            </td>
            <td v-for="dimension in dimensions"
                :key="`${node.id}-${dimension.id}`"
                class="strategy-cell">
              <StrategyCellEditor
                :node="node"
                :dimension="dimension"
                :value="getCellValue(node, dimension)"
                @update="updateCell"
                @focus="setActiveNode(node.id)"
              />
            </td>
            <td class="actions-cell">
              <div class="row-actions">
                <button @click="duplicateNode(node)" class="duplicate-btn" title="复制节点">
                  <i class="ion-ios-copy"></i>
                </button>
                <button @click="exportNode(node)" class="export-btn" title="导出节点">
                  <i class="ion-ios-download"></i>
                </button>
              </div>
            </td>
          </tr>
          <tr class="add-node-row">
            <td colspan="100%">
              <button @click="addNode" class="add-node-btn">
                <i class="ion-plus"></i>
                添加运营节点
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 工具箱面板 -->
    <ToolboxPanel
      v-if="showToolbox"
      :available-tools="availableTools"
      :selected-tools="selectedTools"
      @select-tool="selectTool"
      @close="showToolbox = false"
    />

    <!-- 触发器配置面板 -->
    <TriggerConfigPanel
      v-if="showTriggerConfig"
      :triggers="currentTriggers"
      :node="currentNode"
      @update="updateTriggers"
      @close="showTriggerConfig = false"
    />

    <!-- 模板中心弹窗 -->
    <TemplateCenter
      v-if="showTemplateCenter"
      @import-template="importTemplate"
      @close="showTemplateCenter = false"
    />

    <!-- 测试运行面板 -->
    <TestRunPanel
      v-if="showTestPanel"
      :workbench="workbench"
      @close="showTestPanel = false"
    />
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useStrategyWorkbench } from '../composables/useStrategyWorkbench'
import StrategyCellEditor from './components/StrategyCellEditor.vue'
import ToolboxPanel from './components/ToolboxPanel.vue'
import TriggerConfigPanel from './components/TriggerConfigPanel.vue'
import TemplateCenter from './components/TemplateCenter.vue'
import TestRunPanel from './components/TestRunPanel.vue'

export default {
  name: 'StrategyWorkbench',
  components: {
    StrategyCellEditor,
    ToolboxPanel,
    TriggerConfigPanel,
    TemplateCenter,
    TestRunPanel
  },
  props: {
    workbenchId: {
      type: Number,
      default: null
    }
  },
  setup(props) {
    const {
      workbench,
      nodes,
      dimensions,
      availableTools,
      selectedTools,
      loading,
      saving,
      saveWorkbench,
      addNode,
      deleteNode,
      updateNode,
      addDimension,
      deleteDimension,
      updateCell,
      loadWorkbench
    } = useStrategyWorkbench(props.workbenchId)

    const showToolbox = ref(false)
    const showTriggerConfig = ref(false)
    const showTemplateCenter = ref(false)
    const showTestPanel = ref(false)
    const activeNodeId = ref(null)
    const currentTriggers = ref([])
    const currentNode = ref(null)

    const totalUsers = computed(() => {
      return nodes.value.reduce((sum, node) => sum + (node.currentUsers || 0), 0)
    })

    const averageConversionRate = computed(() => {
      if (nodes.value.length === 0) return 0
      const total = nodes.value.reduce((sum, node) => sum + (node.conversionRate || 0), 0)
      return (total / nodes.value.length).toFixed(1)
    })

    const tableStyle = computed(() => {
      const minWidth = 200 + dimensions.value.reduce((sum, dim) => sum + (dim.width || 200), 0) + 100
      return {
        minWidth: `${minWidth}px`
      }
    })

    onMounted(() => {
      if (props.workbenchId) {
        loadWorkbench()
      }
    })

    return {
      workbench,
      nodes,
      dimensions,
      availableTools,
      selectedTools,
      loading,
      saving,
      showToolbox,
      showTriggerConfig,
      showTemplateCenter,
      showTestPanel,
      activeNodeId,
      currentTriggers,
      currentNode,
      totalUsers,
      averageConversionRate,
      tableStyle,
      saveWorkbench,
      addNode,
      deleteNode,
      updateNode,
      addDimension,
      deleteDimension,
      updateCell,
      loadWorkbench
    }
  },
  methods: {
    getCellValue(node, dimension) {
      return node.cells?.[dimension.id] || dimension.defaultValue || ''
    },

    setActiveNode(nodeId) {
      this.activeNodeId = nodeId
    },

    moveNodeUp(index) {
      if (index > 0) {
        const temp = this.nodes[index]
        this.nodes[index] = this.nodes[index - 1]
        this.nodes[index - 1] = temp
        this.updateNodePositions()
      }
    },

    moveNodeDown(index) {
      if (index < this.nodes.length - 1) {
        const temp = this.nodes[index]
        this.nodes[index] = this.nodes[index + 1]
        this.nodes[index + 1] = temp
        this.updateNodePositions()
      }
    },

    updateNodePositions() {
      this.nodes.forEach((node, index) => {
        node.position = index
      })
    },

    duplicateNode(node) {
      const duplicatedNode = {
        ...node,
        id: null,
        name: `${node.name} (副本)`,
        position: node.position + 1
      }

      this.nodes.splice(node.position + 1, 0, duplicatedNode)
      this.updateNodePositions()
    },

    async exportNode(node) {
      try {
        const exportData = {
          node: node,
          dimensions: this.dimensions.filter(dim =>
            node.cells && node.cells[dim.id]
          )
        }

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
          type: 'application/json'
        })

        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${node.name || 'node'}.json`
        a.click()

        URL.revokeObjectURL(url)
      } catch (error) {
        console.error('Failed to export node:', error)
        this.$toast.error('导出节点失败')
      }
    },

    getConversionRateClass(rate) {
      if (rate >= 80) return 'excellent'
      if (rate >= 60) return 'good'
      if (rate >= 40) return 'average'
      return 'poor'
    },

    editDimension(dimension) {
      // 打开维度编辑弹窗
      this.$emit('edit-dimension', dimension)
    },

    selectTool(tool) {
      this.selectedTools.push(tool)
      this.showToolbox = false
    },

    updateTriggers(triggers) {
      if (this.currentNode) {
        this.currentNode.triggers = triggers
        this.updateNode(this.currentNode)
      }
      this.showTriggerConfig = false
    },

    testWorkbench() {
      this.showTestPanel = true
    },

    showTemplates() {
      this.showTemplateCenter = true
    },

    showSettings() {
      this.$emit('show-settings')
    },

    async importTemplate(template) {
      try {
        // 导入模板数据
        this.nodes = template.nodes || []
        this.dimensions = template.dimensions || []

        this.showTemplateCenter = false
        this.$toast.success('模板导入成功')
      } catch (error) {
        console.error('Failed to import template:', error)
        this.$toast.error('导入模板失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.strategy-workbench {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #ffffff;

  .workbench-header {
    padding: 16px 24px;
    background: #ffffff;
    border-bottom: 1px solid #e0e6ed;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-left {
      h2 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
      }

      .workbench-stats {
        display: flex;
        gap: 16px;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 14px;
          color: #6b7280;

          i {
            font-size: 16px;
          }
        }
      }
    }

    .header-actions {
      display: flex;
      gap: 8px;

      .btn {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        border: 1px solid transparent;
        cursor: pointer;
        transition: all 0.2s;

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        &.btn-primary {
          background: #3b82f6;
          color: white;

          &:hover:not(:disabled) {
            background: #2563eb;
          }
        }

        &.btn-secondary {
          background: #10b981;
          color: white;

          &:hover {
            background: #059669;
          }
        }

        &.btn-outline {
          background: transparent;
          color: #6b7280;
          border-color: #d1d5db;

          &:hover {
            background: #f9fafb;
            border-color: #9ca3af;
          }
        }
      }
    }
  }

  .strategy-table-container {
    flex: 1;
    overflow: auto;
    background: #f8fafc;

    .strategy-table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;

      th, td {
        border: 1px solid #e5e7eb;
        border-top: 0;
        border-left: 0;
        vertical-align: top;

        &:first-child {
          border-left: 1px solid #e5e7eb;
        }
      }

      th {
        background: #ffffff;
        position: sticky;
        top: 0;
        z-index: 10;
        height: 60px;

        .column-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          height: 100%;

          span {
            font-weight: 600;
            color: #374151;
          }

          .column-actions {
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s;
          }

          &:hover .column-actions {
            opacity: 1;
          }
        }
      }

      .sticky-column {
        position: sticky;
        left: 0;
        z-index: 5;
        background: #ffffff;
        min-width: 200px;
        max-width: 200px;
      }

      .node-cell {
        padding: 16px;

        .node-info {
          .node-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .node-name-input {
              flex: 1;
              border: none;
              background: transparent;
              font-size: 16px;
              font-weight: 600;
              color: #1f2937;
              padding: 4px 0;

              &:focus {
                outline: none;
                background: #f9fafb;
                border-radius: 4px;
                padding: 4px 8px;
              }
            }

            .node-actions {
              display: flex;
              gap: 2px;
              opacity: 0;
              transition: opacity 0.2s;
            }

            &:hover .node-actions {
              opacity: 1;
            }
          }

          .node-stats {
            display: flex;
            gap: 12px;

            .user-count, .conversion-rate {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              padding: 2px 6px;
              border-radius: 12px;
              background: #f1f5f9;
              color: #64748b;
            }

            .conversion-rate {
              &.excellent { background: #dcfce7; color: #166534; }
              &.good { background: #dbeafe; color: #1d4ed8; }
              &.average { background: #fef3c7; color: #92400e; }
              &.poor { background: #fee2e2; color: #dc2626; }
            }
          }
        }
      }

      .strategy-cell {
        padding: 8px;
        min-width: 200px;
        max-width: 300px;
      }

      .node-row {
        &.active {
          background: #eff6ff;
        }

        &:hover {
          background: #f8fafc;
        }
      }

      .add-node-row {
        td {
          padding: 16px;
          text-align: center;
          border-bottom: 1px solid #e5e7eb;
        }

        .add-node-btn {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 12px 24px;
          background: transparent;
          border: 2px dashed #d1d5db;
          border-radius: 8px;
          color: #6b7280;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.2s;
          margin: 0 auto;

          &:hover {
            border-color: #3b82f6;
            color: #3b82f6;
            background: #eff6ff;
          }
        }
      }
    }
  }
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
```

#### 3. WebSocket集成Composable

```javascript
// app/javascript/dashboard/composables/useAiWebSocket.js
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import ActionCableConnector from '../helper/ActionCableConnector'

export function useAiWebSocket(conversationId) {
  const store = useStore()

  // 响应式状态
  const aiStatus = ref('idle')
  const currentConfidence = ref(0)
  const suggestions = ref([])
  const currentIntent = ref(null)
  const executionTasks = ref([])
  const isConnected = ref(false)
  const connectionError = ref(null)

  // WebSocket连接
  let aiChannel = null

  const connectToAi = async () => {
    try {
      aiChannel = ActionCableConnector.consumer.subscriptions.create(
        {
          channel: 'AiChannel',
          conversation_id: conversationId
        },
        {
          connected() {
            console.log('AI WebSocket connected')
            isConnected.value = true
            connectionError.value = null
          },

          disconnected() {
            console.log('AI WebSocket disconnected')
            isConnected.value = false
          },

          received(data) {
            handleAiMessage(data)
          },

          rejected() {
            console.error('AI WebSocket connection rejected')
            connectionError.value = 'Connection rejected'
          }
        }
      )
    } catch (error) {
      console.error('Failed to connect to AI WebSocket:', error)
      connectionError.value = error.message
    }
  }

  const disconnectFromAi = () => {
    if (aiChannel) {
      aiChannel.unsubscribe()
      aiChannel = null
      isConnected.value = false
    }
  }

  const handleAiMessage = (data) => {
    switch (data.type) {
      case 'ai_status':
        updateAiStatus(data.data)
        break
      case 'ai_suggestions':
        updateSuggestions(data.data)
        break
      case 'ai_processing_completed':
        handleProcessingCompleted(data.data)
        break
      case 'ai_intent_update':
        updateIntent(data.data)
        break
      case 'ai_task_update':
        updateTasks(data.data)
        break
      case 'ai_service_unavailable':
        handleServiceUnavailable(data.data)
        break
      default:
        console.warn('Unknown AI message type:', data.type)
    }
  }

  const updateAiStatus = (statusData) => {
    aiStatus.value = statusData.processing ? 'processing' : 'idle'
    if (statusData.insights) {
      currentIntent.value = statusData.insights.intent
      currentConfidence.value = statusData.insights.confidence
    }
  }

  const updateSuggestions = (suggestionsData) => {
    suggestions.value = suggestionsData.map(suggestion => ({
      id: suggestion.id || Math.random().toString(36),
      type: suggestion.type,
      content: suggestion.content,
      confidence: suggestion.confidence || 0,
      reasoning: suggestion.reasoning,
      optimization_tip: suggestion.optimization_tip
    }))
  }

  const handleProcessingCompleted = (completionData) => {
    aiStatus.value = 'idle'

    if (completionData.result && completionData.result.ai_insights) {
      const insights = completionData.result.ai_insights
      currentIntent.value = insights.intent
      currentConfidence.value = insights.confidence

      if (insights.suggested_responses) {
        updateSuggestions(insights.suggested_responses)
      }
    }

    // 通知用户处理完成
    store.dispatch('notifications/addNotification', {
      type: 'success',
      message: 'AI处理完成',
      duration: 3000
    })
  }

  const updateIntent = (intentData) => {
    currentIntent.value = {
      primary: intentData.primary,
      secondary: intentData.secondary,
      confidence: intentData.confidence,
      keywords: intentData.keywords || []
    }
  }

  const updateTasks = (tasksData) => {
    executionTasks.value = tasksData.tasks || []
  }

  const handleServiceUnavailable = (errorData) => {
    aiStatus.value = 'error'
    connectionError.value = errorData.message

    store.dispatch('notifications/addNotification', {
      type: 'warning',
      message: errorData.message || 'AI服务暂时不可用',
      duration: 5000
    })
  }

  // 主动请求功能
  const requestSuggestions = async (context = {}) => {
    if (!aiChannel || !isConnected.value) {
      throw new Error('AI WebSocket not connected')
    }

    aiChannel.perform('request_suggestions', {
      context: {
        conversation_id: conversationId,
        ...context
      }
    })
  }

  const executeTask = async (taskId, params = {}) => {
    if (!aiChannel || !isConnected.value) {
      throw new Error('AI WebSocket not connected')
    }

    aiChannel.perform('execute_task', {
      task_id: taskId,
      params: params
    })
  }

  const updatePreferences = async (preferences) => {
    if (!aiChannel || !isConnected.value) {
      throw new Error('AI WebSocket not connected')
    }

    aiChannel.perform('update_preferences', {
      preferences: preferences
    })
  }

  // 生命周期管理
  onMounted(() => {
    connectToAi()
  })

  onUnmounted(() => {
    disconnectFromAi()
  })

  return {
    // 状态
    aiStatus,
    currentConfidence,
    suggestions,
    currentIntent,
    executionTasks,
    isConnected,
    connectionError,

    // 方法
    connectToAi,
    disconnectFromAi,
    requestSuggestions,
    executeTask,
    updatePreferences
  }
}
```

---

## 数据流和状态管理

### 🔄 对话状态同步机制

#### 1. Vuex Store模块

```javascript
// app/javascript/dashboard/store/modules/aiIntegration.js
const state = {
  // AI服务状态
  serviceStatus: 'idle', // idle, connecting, connected, error
  connectionError: null,

  // 对话AI状态
  conversationAiStates: {}, // { conversationId: { status, confidence, intent, etc. } }

  // 策略工作台
  currentWorkbench: null,
  workbenches: [],

  // AI建议
  suggestions: {},

  // 用户偏好
  userPreferences: {
    aiEnabled: true,
    showSuggestions: true,
    showJourneyCompass: true,
    showIntentLens: true,
    suggestionConfidenceThreshold: 0.7,
    autoApplySuggestions: false
  },

  // 执行历史
  executionHistory: []
}

const getters = {
  getConversationAiState: (state) => (conversationId) => {
    return state.conversationAiStates[conversationId] || {
      status: 'idle',
      confidence: 0,
      intent: null,
      suggestions: [],
      tasks: []
    }
  },

  getActiveSuggestions: (state) => (conversationId) => {
    const aiState = state.conversationAiStates[conversationId]
    if (!aiState || !aiState.suggestions) return []

    return aiState.suggestions.filter(suggestion =>
      suggestion.confidence >= state.userPreferences.suggestionConfidenceThreshold
    )
  },

  isAiProcessing: (state) => (conversationId) => {
    const aiState = state.conversationAiStates[conversationId]
    return aiState && ['thinking', 'processing'].includes(aiState.status)
  },

  getCurrentWorkbench: (state) => {
    return state.currentWorkbench
  },

  getUserPreferences: (state) => {
    return state.userPreferences
  }
}

const mutations = {
  SET_SERVICE_STATUS(state, status) {
    state.serviceStatus = status
  },

  SET_CONNECTION_ERROR(state, error) {
    state.connectionError = error
  },

  UPDATE_CONVERSATION_AI_STATE(state, { conversationId, updates }) {
    if (!state.conversationAiStates[conversationId]) {
      state.conversationAiStates[conversationId] = {
        status: 'idle',
        confidence: 0,
        intent: null,
        suggestions: [],
        tasks: []
      }
    }

    Object.assign(state.conversationAiStates[conversationId], updates)
  },

  SET_SUGGESTIONS(state, { conversationId, suggestions }) {
    if (!state.conversationAiStates[conversationId]) {
      state.conversationAiStates[conversationId] = {}
    }
    state.conversationAiStates[conversationId].suggestions = suggestions
  },

  SET_CURRENT_WORKBENCH(state, workbench) {
    state.currentWorkbench = workbench
  },

  ADD_WORKBENCH(state, workbench) {
    state.workbenches.push(workbench)
  },

  UPDATE_WORKBENCH(state, { id, updates }) {
    const index = state.workbenches.findIndex(w => w.id === id)
    if (index !== -1) {
      Object.assign(state.workbenches[index], updates)
    }
  },

  UPDATE_USER_PREFERENCES(state, preferences) {
    Object.assign(state.userPreferences, preferences)
  },

  ADD_EXECUTION_HISTORY(state, execution) {
    state.executionHistory.unshift(execution)
    // 保持最近100条记录
    if (state.executionHistory.length > 100) {
      state.executionHistory = state.executionHistory.slice(0, 100)
    }
  }
}

const actions = {
  async initializeAiIntegration({ commit, dispatch }) {
    try {
      commit('SET_SERVICE_STATUS', 'connecting')

      // 加载用户偏好
      await dispatch('loadUserPreferences')

      // 检查AI服务状态
      const response = await this.$http.get('/api/v1/ai/status')

      if (response.data.available) {
        commit('SET_SERVICE_STATUS', 'connected')
      } else {
        commit('SET_SERVICE_STATUS', 'error')
        commit('SET_CONNECTION_ERROR', 'AI service not available')
      }
    } catch (error) {
      commit('SET_SERVICE_STATUS', 'error')
      commit('SET_CONNECTION_ERROR', error.message)
    }
  },

  async loadUserPreferences({ commit }) {
    try {
      const response = await this.$http.get('/api/v1/profile/ai_preferences')
      commit('UPDATE_USER_PREFERENCES', response.data.preferences || {})
    } catch (error) {
      console.error('Failed to load AI preferences:', error)
    }
  },

  async updateUserPreferences({ commit }, preferences) {
    try {
      await this.$http.put('/api/v1/profile/ai_preferences', {
        preferences: preferences
      })
      commit('UPDATE_USER_PREFERENCES', preferences)
    } catch (error) {
      console.error('Failed to update AI preferences:', error)
      throw error
    }
  },

  updateConversationAiState({ commit }, { conversationId, updates }) {
    commit('UPDATE_CONVERSATION_AI_STATE', { conversationId, updates })
  },

  async requestAiSuggestions({ commit }, { conversationId, context }) {
    try {
      commit('UPDATE_CONVERSATION_AI_STATE', {
        conversationId,
        updates: { status: 'thinking' }
      })

      const response = await this.$http.post(
        `/api/v1/accounts/${context.accountId}/ai/suggestions`,
        {
          conversation_id: conversationId,
          context: context
        }
      )

      commit('SET_SUGGESTIONS', {
        conversationId,
        suggestions: response.data.suggestions
      })

      commit('UPDATE_CONVERSATION_AI_STATE', {
        conversationId,
        updates: { status: 'idle' }
      })

      return response.data.suggestions
    } catch (error) {
      commit('UPDATE_CONVERSATION_AI_STATE', {
        conversationId,
        updates: { status: 'error' }
      })
      throw error
    }
  },

  async loadWorkbench({ commit }, workbenchId) {
    try {
      const response = await this.$http.get(
        `/api/v1/accounts/${this.getters.getCurrentAccount.id}/ai_strategies/${workbenchId}`
      )
      commit('SET_CURRENT_WORKBENCH', response.data)
      return response.data
    } catch (error) {
      console.error('Failed to load workbench:', error)
      throw error
    }
  },

  async saveWorkbench({ commit, getters }, workbenchData) {
    try {
      const accountId = getters.getCurrentAccount.id
      let response

      if (workbenchData.id) {
        response = await this.$http.put(
          `/api/v1/accounts/${accountId}/ai_strategies/${workbenchData.id}`,
          { ai_strategy: workbenchData }
        )
        commit('UPDATE_WORKBENCH', { id: workbenchData.id, updates: response.data })
      } else {
        response = await this.$http.post(
          `/api/v1/accounts/${accountId}/ai_strategies`,
          { ai_strategy: workbenchData }
        )
        commit('ADD_WORKBENCH', response.data)
      }

      commit('SET_CURRENT_WORKBENCH', response.data)
      return response.data
    } catch (error) {
      console.error('Failed to save workbench:', error)
      throw error
    }
  },

  recordExecution({ commit }, executionData) {
    commit('ADD_EXECUTION_HISTORY', {
      ...executionData,
      timestamp: new Date().toISOString()
    })
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
```

#### 2. 缓存策略实现

```ruby
# app/services/ai_integration/cache_service.rb
class AiIntegration::CacheService
  CACHE_PREFIXES = {
    suggestions: 'ai:suggestions',
    intent: 'ai:intent',
    context: 'ai:context',
    strategy: 'ai:strategy'
  }.freeze

  CACHE_EXPIRY = {
    suggestions: 5.minutes,
    intent: 10.minutes,
    context: 30.minutes,
    strategy: 1.hour
  }.freeze

  class << self
    def cache_suggestions(conversation_id, suggestions)
      key = cache_key(:suggestions, conversation_id)
      Rails.cache.write(key, suggestions, expires_in: CACHE_EXPIRY[:suggestions])
    end

    def get_cached_suggestions(conversation_id)
      key = cache_key(:suggestions, conversation_id)
      Rails.cache.read(key)
    end

    def cache_intent(conversation_id, intent_data)
      key = cache_key(:intent, conversation_id)
      Rails.cache.write(key, intent_data, expires_in: CACHE_EXPIRY[:intent])
    end

    def get_cached_intent(conversation_id)
      key = cache_key(:intent, conversation_id)
      Rails.cache.read(key)
    end

    def cache_conversation_context(conversation_id, context)
      key = cache_key(:context, conversation_id)
      Rails.cache.write(key, context, expires_in: CACHE_EXPIRY[:context])
    end

    def get_cached_context(conversation_id)
      key = cache_key(:context, conversation_id)
      Rails.cache.read(key)
    end

    def cache_strategy_result(strategy_id, input_hash, result)
      key = cache_key(:strategy, "#{strategy_id}:#{input_hash}")
      Rails.cache.write(key, result, expires_in: CACHE_EXPIRY[:strategy])
    end

    def get_cached_strategy_result(strategy_id, input_hash)
      key = cache_key(:strategy, "#{strategy_id}:#{input_hash}")
      Rails.cache.read(key)
    end

    def invalidate_conversation_cache(conversation_id)
      patterns = [
        cache_key(:suggestions, conversation_id),
        cache_key(:intent, conversation_id),
        cache_key(:context, conversation_id)
      ]

      patterns.each { |pattern| Rails.cache.delete(pattern) }
    end

    def warm_cache_for_conversation(conversation)
      # 预热常用数据的缓存
      context = build_conversation_context(conversation)
      cache_conversation_context(conversation.id, context)

      # 如果有最近的意图数据，也缓存起来
      if conversation.last_ai_interaction
        intent_data = conversation.last_ai_interaction.output_data&.dig('ai_insights', 'intent')
        cache_intent(conversation.id, intent_data) if intent_data
      end
    end

    private

    def cache_key(type, identifier)
      "#{CACHE_PREFIXES[type]}:#{identifier}"
    end

    def build_conversation_context(conversation)
      {
        account_id: conversation.account_id,
        inbox_id: conversation.inbox_id,
        contact: conversation.contact.as_json(only: [:id, :name, :email]),
        labels: conversation.labels.pluck(:title),
        recent_messages: conversation.messages.recent.limit(10).as_json,
        created_at: conversation.created_at,
        updated_at: conversation.updated_at
      }
    end
  end
end
```

---

## 配置和部署准备

### ⚙️ 环境变量配置

```bash
# config/application.yml (使用figaro gem)
# AI服务集成配置
AI_SERVICE_URL: 'http://localhost:4000'
AI_SERVICE_JWT_SECRET: 'your-jwt-secret-key'
AI_SERVICE_TIMEOUT: '30'
AI_SERVICE_RETRY_ATTEMPTS: '3'
AI_SERVICE_RETRY_DELAY: '1'

# Webhook配置
AI_WEBHOOK_SECRET: 'your-webhook-secret-key'
AI_WEBHOOK_ENDPOINT: '/webhooks/ai'

# 功能开关
AI_FEATURES_ENABLED: 'true'
AI_SUGGESTIONS_ENABLED: 'true'
AI_STRATEGY_WORKBENCH_ENABLED: 'true'
AI_COPILOT_PANEL_ENABLED: 'true'

# 性能配置
AI_CACHE_TTL: '300'
AI_MAX_CONCURRENT_REQUESTS: '10'
AI_REQUEST_QUEUE_SIZE: '100'

# 监控配置
AI_METRICS_ENABLED: 'true'
AI_LOGGING_LEVEL: 'info'
```

#### Docker配置调整

```dockerfile
# Dockerfile - 添加AI集成相关配置
FROM ruby:3.1-slim

# ... 现有配置 ...

# 添加AI服务健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 添加AI服务相关的环境变量
ENV AI_SERVICE_URL=http://ai-service:4000
ENV AI_FEATURES_ENABLED=true

# ... 其余配置 ...
```

```yaml
# docker-compose.yml - 添加AI服务依赖
version: '3.8'

services:
  chatwoot:
    build: .
    ports:
      - "3000:3000"
    environment:
      - AI_SERVICE_URL=http://ai-service:4000
      - AI_FEATURES_ENABLED=true
    depends_on:
      - postgres
      - redis
      - ai-service
    networks:
      - chatwoot-network

  ai-service:
    image: chatwoot-ai-service:latest
    ports:
      - "4000:4000"
    environment:
      - CHATWOOT_API_URL=http://chatwoot:3000
      - DATABASE_URL=********************************************/chatwoot
    depends_on:
      - postgres
      - redis
    networks:
      - chatwoot-network

  postgres:
    image: postgres:15
    # ... 现有配置 ...

  redis:
    image: redis:7-alpine
    # ... 现有配置 ...

networks:
  chatwoot-network:
    driver: bridge
```

---

## 测试策略

### 🧪 单元测试

```ruby
# spec/services/ai_integration/client_service_spec.rb
require 'rails_helper'

RSpec.describe AiIntegration::ClientService do
  let(:account) { create(:account, ai_enabled: true) }
  let(:conversation) { create(:conversation, account: account) }
  let(:service) { described_class.new(account: account) }

  before do
    stub_const('ENV', ENV.to_hash.merge(
      'AI_SERVICE_URL' => 'http://localhost:4000',
      'AI_SERVICE_JWT_SECRET' => 'test-secret'
    ))
  end

  describe '#process_message' do
    let(:message) { { content: 'Hello', message_type: 'incoming' } }
    let(:context) { { account_id: account.id } }

    context 'when AI service responds successfully' do
      before do
        stub_request(:post, 'http://localhost:4000/api/v1/conversations/1/process')
          .to_return(
            status: 200,
            body: {
              success: true,
              actions: [
                {
                  type: 'send_reply',
                  data: { content: 'Hi there!' }
                }
              ]
            }.to_json
          )
      end

      it 'returns the AI response' do
        result = service.process_message(
          conversation: conversation,
          message: message,
          context: context
        )

        expect(result['success']).to be true
        expect(result['actions']).to be_present
      end
    end

    context 'when AI service is unavailable' do
      before do
        stub_request(:post, 'http://localhost:4000/api/v1/conversations/1/process')
          .to_return(status: 503)
      end

      it 'raises ServiceUnavailableError' do
        expect {
          service.process_message(
            conversation: conversation,
            message: message,
            context: context
          )
        }.to raise_error(AiService::ServiceUnavailableError)
      end
    end

    context 'when request times out' do
      before do
        stub_request(:post, 'http://localhost:4000/api/v1/conversations/1/process')
          .to_timeout
      end

      it 'raises TimeoutError' do
        expect {
          service.process_message(
            conversation: conversation,
            message: message,
            context: context
          )
        }.to raise_error(AiService::TimeoutError)
      end
    end
  end
end
```

```ruby
# spec/models/ai_strategy_spec.rb
require 'rails_helper'

RSpec.describe AiStrategy do
  let(:account) { create(:account) }

  describe 'validations' do
    it 'requires a name' do
      strategy = build(:ai_strategy, name: nil)
      expect(strategy).not_to be_valid
      expect(strategy.errors[:name]).to include("can't be blank")
    end

    it 'requires an account' do
      strategy = build(:ai_strategy, account: nil)
      expect(strategy).not_to be_valid
      expect(strategy.errors[:account]).to include("can't be blank")
    end
  end

  describe '#to_ai_format' do
    let(:strategy) { create(:ai_strategy, account: account) }
    let!(:node) { create(:ai_strategy_node, ai_strategy: strategy) }

    it 'returns the strategy in AI service format' do
      result = strategy.to_ai_format

      expect(result).to include(
        id: strategy.id,
        name: strategy.name,
        description: strategy.description,
        is_active: strategy.is_active,
        nodes: array_including(hash_including(id: node.id))
      )
    end
  end

  describe '#execution_stats' do
    let(:strategy) { create(:ai_strategy, account: account) }
    let(:conversation) { create(:conversation, account: account) }

    before do
      create_list(:ai_execution_log, 3,
        ai_strategy: strategy,
        conversation: conversation,
        status: :completed,
        execution_time: 1.5
      )
      create(:ai_execution_log,
        ai_strategy: strategy,
        conversation: conversation,
        status: :failed
      )
    end

    it 'calculates execution statistics' do
      stats = strategy.execution_stats

      expect(stats[:total_executions]).to eq(4)
      expect(stats[:success_rate]).to eq(0.75)
      expect(stats[:avg_execution_time]).to eq(1.5)
    end
  end
end
```

### 🔧 Mock AI服务

```ruby
# spec/support/ai_service_mock.rb
class AiServiceMock
  include Singleton

  def initialize
    @responses = {}
    @delays = {}
    @failures = {}
  end

  def mock_response(endpoint, response, delay: 0, failure: nil)
    @responses[endpoint] = response
    @delays[endpoint] = delay
    @failures[endpoint] = failure
  end

  def clear_mocks
    @responses.clear
    @delays.clear
    @failures.clear
  end

  def handle_request(endpoint, params = {})
    sleep(@delays[endpoint]) if @delays[endpoint]

    if @failures[endpoint]
      raise @failures[endpoint]
    end

    response = @responses[endpoint]

    if response.is_a?(Proc)
      response.call(params)
    else
      response || default_response
    end
  end

  private

  def default_response
    {
      success: true,
      actions: [],
      ai_insights: {
        intent: { primary: 'unknown', confidence: 0.5 },
        sentiment: { polarity: 'neutral', score: 0 }
      }
    }
  end
end

# 在测试中使用
RSpec.configure do |config|
  config.before(:each) do
    AiServiceMock.instance.clear_mocks

    # 默认mock所有AI服务请求
    allow_any_instance_of(AiIntegration::ClientService)
      .to receive(:process_message) do |_, params|
        AiServiceMock.instance.handle_request('process_message', params)
      end
  end
end
```

### 🎯 集成测试

```ruby
# spec/requests/api/v1/accounts/ai_controller_spec.rb
require 'rails_helper'

RSpec.describe 'AI Integration API', type: :request do
  let(:account) { create(:account, ai_enabled: true) }
  let(:user) { create(:user, account: account) }
  let(:conversation) { create(:conversation, account: account) }

  before do
    sign_in user
  end

  describe 'POST /api/v1/accounts/:account_id/ai/process_message' do
    let(:message_params) do
      {
        message: {
          content: 'Hello, I need help',
          message_type: 'incoming'
        }
      }
    end

    context 'when AI service is available' do
      before do
        AiServiceMock.instance.mock_response('process_message', {
          success: true,
          actions: [
            {
              type: 'send_reply',
              data: { content: 'How can I help you?' }
            }
          ],
          ai_insights: {
            intent: { primary: 'support_request', confidence: 0.85 }
          }
        })
      end

      it 'processes the message successfully' do
        post "/api/v1/accounts/#{account.id}/ai/process_message",
             params: message_params.merge(conversation_id: conversation.id)

        expect(response).to have_http_status(:ok)

        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['actions']).to be_present
        expect(json_response['ai_insights']['intent']['primary']).to eq('support_request')
      end
    end

    context 'when AI service is unavailable' do
      before do
        AiServiceMock.instance.mock_response('process_message', nil,
          failure: AiService::ServiceUnavailableError.new('Service unavailable')
        )
      end

      it 'returns service unavailable error' do
        post "/api/v1/accounts/#{account.id}/ai/process_message",
             params: message_params.merge(conversation_id: conversation.id)

        expect(response).to have_http_status(:service_unavailable)

        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('AI service temporarily unavailable')
        expect(json_response['fallback']).to be true
      end
    end
  end

  describe 'GET /api/v1/accounts/:account_id/ai/suggestions' do
    context 'when suggestions are available' do
      before do
        AiServiceMock.instance.mock_response('get_suggestions', {
          suggestions: [
            {
              type: 'efficient',
              content: 'Thank you for contacting us. How can I help?',
              confidence: 0.9
            },
            {
              type: 'empathetic',
              content: 'I understand you need assistance. I\'m here to help.',
              confidence: 0.85
            }
          ]
        })
      end

      it 'returns AI suggestions' do
        get "/api/v1/accounts/#{account.id}/ai/suggestions",
            params: { conversation_id: conversation.id }

        expect(response).to have_http_status(:ok)

        json_response = JSON.parse(response.body)
        expect(json_response['suggestions']).to have(2).items
        expect(json_response['suggestions'].first['type']).to eq('efficient')
      end
    end
  end
end
```

### 📱 端到端测试

```ruby
# spec/system/ai_integration_spec.rb
require 'rails_helper'

RSpec.describe 'AI Integration', type: :system, js: true do
  let(:account) { create(:account, ai_enabled: true) }
  let(:user) { create(:user, account: account) }
  let(:inbox) { create(:inbox, account: account) }
  let(:conversation) { create(:conversation, account: account, inbox: inbox) }

  before do
    sign_in user

    # Mock AI service responses
    AiServiceMock.instance.mock_response('process_message', {
      success: true,
      actions: [
        {
          type: 'send_reply',
          data: {
            content: 'Thank you for your message. How can I help you today?',
            confidence: 0.9
          }
        }
      ],
      ai_insights: {
        intent: { primary: 'greeting', confidence: 0.85 },
        suggested_responses: [
          {
            type: 'efficient',
            content: 'Hi! How can I help?',
            confidence: 0.9
          },
          {
            type: 'empathetic',
            content: 'Hello! I\'m here to assist you with anything you need.',
            confidence: 0.85
          }
        ]
      }
    })
  end

  scenario 'Agent receives AI suggestions when customer sends message' do
    visit "/app/accounts/#{account.id}/conversations/#{conversation.id}"

    # 等待页面加载
    expect(page).to have_content(conversation.display_id)

    # 模拟收到客户消息
    create(:message,
      conversation: conversation,
      content: 'Hello, I need help with my order',
      message_type: 'incoming'
    )

    # 刷新页面以触发AI处理
    page.refresh

    # 等待AI副驾面板出现
    expect(page).to have_css('.ai-copilot-panel', wait: 10)

    # 检查AI建议是否显示
    within('.ai-copilot-panel') do
      expect(page).to have_content('智能建议')
      expect(page).to have_content('Hi! How can I help?')
      expect(page).to have_content('Hello! I\'m here to assist you')
    end

    # 点击应用建议
    within('.ai-reply-suggestions') do
      first('.apply-suggestion-btn').click
    end

    # 检查建议是否应用到消息输入框
    expect(find('.message-input')).to have_content('Hi! How can I help?')
  end

  scenario 'Agent can configure AI preferences' do
    visit "/app/accounts/#{account.id}/settings/profile"

    # 点击AI设置标签
    click_on 'AI设置'

    # 修改AI偏好设置
    within('.ai-preferences-form') do
      uncheck 'showSuggestions'
      fill_in 'suggestionConfidenceThreshold', with: '0.8'
      click_button '保存设置'
    end

    # 检查设置是否保存成功
    expect(page).to have_content('AI偏好设置已更新')

    # 返回对话页面验证设置生效
    visit "/app/accounts/#{account.id}/conversations/#{conversation.id}"

    # AI建议面板应该被隐藏
    expect(page).not_to have_css('.ai-reply-suggestions')
  end

  scenario 'Strategy workbench allows creating and editing strategies' do
    visit "/app/accounts/#{account.id}/ai/strategies"

    # 创建新策略
    click_button '创建策略'

    within('.strategy-form') do
      fill_in 'name', with: '客户欢迎策略'
      fill_in 'description', with: '为新客户提供个性化欢迎体验'
      click_button '创建'
    end

    # 进入策略工作台
    expect(page).to have_content('策略工作台')

    # 添加运营节点
    click_button '添加运营节点'

    within('.node-row:last-child') do
      fill_in 'node-name', with: '初次接触'

      # 配置触发器
      click_button '配置触发器'
    end

    within('.trigger-config-panel') do
      select '关键词触发', from: 'trigger-type'
      fill_in 'keywords', with: '你好,hello,hi'
      click_button '保存触发器'
    end

    # 保存策略
    click_button '保存策略'

    expect(page).to have_content('策略保存成功')
  end
end
```

---

## 总结

本开发指南为Chatwoot团队提供了完整的AI微服务集成准备工作指导，涵盖了从后端API开发到前端界面改造的所有关键环节。通过遵循本指南，Chatwoot团队可以：

### 🎯 核心收益

1. **并行开发能力**：与AI微服务团队实现真正的并行开发
2. **标准化集成**：建立清晰的API接口规范和数据流
3. **用户体验提升**：无缝集成AI功能到现有界面
4. **系统稳定性**：完善的错误处理和降级策略
5. **可测试性**：全面的测试策略确保质量

### 🚀 下一步行动

1. **立即开始**：按照文档开始Controller和Service层的开发
2. **团队协调**：与AI微服务团队确认接口规范
3. **渐进实施**：先实现基础功能，再逐步完善高级特性
4. **持续测试**：在开发过程中持续进行集成测试

通过本指南的实施，Chatwoot将成功实现与AI微服务的深度集成，为用户提供智能化的客服体验。
```
