# Claude-Code集成Chatwoot AI服务开发指南

版本: 1.0  
日期: 2025-08-18  
目标: 指导独立AI服务团队基于Claude-Code开发与Chatwoot集成的智能服务

## 目录
- [项目概述](#项目概述)
- [Claude-Code项目分析](#claude-code项目分析)
- [Chatwoot集成接口规范](#chatwoot集成接口规范)
- [三阶段开发路线](#三阶段开发路线)
- [技术架构设计](#技术架构设计)
- [开发环境配置](#开发环境配置)
- [部署和运维](#部署和运维)

---

## 项目概述

### 🎯 项目目标

基于Claude-Code项目的AI代理能力，开发独立的AI服务，与Chatwoot客服系统集成，最终实现PRD文档中规划的智能私域运营平台功能。

### 📋 开发原则

1. **独立服务**：AI功能作为独立微服务开发，与Chatwoot松耦合
2. **渐进集成**：分三阶段实施，确保每个阶段都有可交付价值
3. **接口标准**：定义清晰的API接口规范，便于集成和维护
4. **可扩展性**：架构设计支持未来功能扩展和性能优化

### 🔄 核心理念

```mermaid
mindmap
  root((AI服务开发理念))
    Claude-Code能力复用
      AI代理系统
      工具调用框架
      Hook扩展机制
      自然语言处理
    Chatwoot业务集成
      客服对话处理
      用户状态管理
      多渠道消息路由
      权限和安全控制
    PRD功能实现
      策略工作台
      AI副驾面板
      目标导向代理
      人机协同优化
```

---

## Claude-Code项目分析

### 🔍 核心能力评估

#### 可直接复用的功能模块

```mermaid
graph TB
    subgraph "AI代理核心"
        A[Claude Agent Engine] --> A1[自然语言理解]
        A --> A2[任务规划和分解]
        A --> A3[上下文状态管理]
        A --> A4[多轮对话处理]
    end
    
    subgraph "工具调用系统"
        B[Tool Registry] --> B1[工具发现和注册]
        B --> B2[参数验证和转换]
        B --> B3[异步执行管理]
        B --> B4[结果处理和格式化]
    end
    
    subgraph "Hook扩展机制"
        C[Hook System] --> C1[PreToolUse钩子]
        C --> C2[PostToolUse钩子]
        C --> C3[SessionStart钩子]
        C --> C4[自定义验证逻辑]
    end
    
    subgraph "MCP协议支持"
        D[MCP Integration] --> D1[外部服务连接]
        D --> D2[多种传输协议]
        D --> D3[认证和授权]
        D --> D4[服务发现机制]
    end
```

#### 技术架构优势

| 组件 | 优势特性 | 集成价值 |
|------|---------|---------|
| **AI代理引擎** | 成熟的Claude API集成，支持复杂推理 | 为Chatwoot提供智能对话能力 |
| **工具调用框架** | 插件化架构，易于扩展新工具 | 支持Chatwoot业务操作自动化 |
| **Hook系统** | 灵活的扩展点，支持自定义逻辑 | 实现业务规则和权限控制 |
| **MCP协议** | 标准化的服务集成协议 | 便于与Chatwoot API集成 |

### 📦 需要适配的关键组件

#### 1. 消息处理适配器
- **功能**：将Chatwoot消息格式转换为Claude-Code可处理的格式
- **职责**：消息标准化、上下文构建、响应格式化

#### 2. 工具集成层
- **功能**：注册Chatwoot特定的业务工具
- **职责**：API调用封装、权限验证、错误处理

#### 3. 状态同步机制
- **功能**：保持AI服务与Chatwoot的数据一致性
- **职责**：会话状态管理、用户画像同步、配置更新

---

## Chatwoot集成接口规范

### 🔌 API接口设计

#### 核心集成接口

```mermaid
graph LR
    subgraph "Chatwoot系统"
        A[消息接收] --> B[Webhook触发]
        B --> C[AI服务调用]
        C --> D[响应处理]
        D --> E[消息发送]
    end
    
    subgraph "AI服务接口"
        F[POST /api/v1/process_message] --> F1[消息处理]
        G[POST /api/v1/execute_strategy] --> G1[策略执行]
        H[GET /api/v1/agent_status] --> H1[状态查询]
        I[POST /api/v1/update_context] --> I1[上下文更新]
    end
    
    C --> F
    C --> G
    C --> H
    C --> I
```

#### 1. 消息处理接口

**接口路径**: `POST /api/v1/process_message`

**请求格式**:
```json
{
  "conversation_id": "string",
  "message": {
    "id": "number",
    "content": "string",
    "message_type": "incoming|outgoing",
    "sender": {
      "type": "contact|user",
      "id": "number",
      "name": "string"
    },
    "created_at": "datetime"
  },
  "context": {
    "account_id": "number",
    "inbox_id": "number",
    "contact_info": "object",
    "conversation_history": "array",
    "agent_preferences": "object"
  }
}
```

**响应格式**:
```json
{
  "success": "boolean",
  "actions": [
    {
      "type": "send_reply|add_tag|transfer|create_note",
      "data": "object",
      "priority": "number"
    }
  ],
  "ai_insights": {
    "intent": "string",
    "confidence": "number",
    "suggested_responses": "array",
    "next_actions": "array"
  },
  "context_updates": "object"
}
```

#### 2. 策略执行接口

**接口路径**: `POST /api/v1/execute_strategy`

**功能**: 执行策略工作台配置的自动化策略

**请求格式**:
```json
{
  "strategy_id": "string",
  "trigger_event": {
    "type": "message|status_change|behavior",
    "data": "object"
  },
  "context": {
    "conversation_id": "string",
    "user_profile": "object",
    "current_node": "string"
  }
}
```

#### 3. 实时状态接口

**接口路径**: `GET /api/v1/agent_status/{conversation_id}`

**功能**: 获取AI代理当前状态，用于副驾面板显示

**响应格式**:
```json
{
  "agent_status": "active|thinking|idle",
  "current_task": "string",
  "confidence_level": "number",
  "processing_time": "number",
  "available_actions": "array"
}
```

### 🔐 认证和安全

#### API认证机制
- **方式**: JWT Token + API Key双重认证
- **权限**: 基于Chatwoot账户和用户角色的细粒度权限控制
- **安全**: 请求签名验证，防止重放攻击

#### 数据安全要求
- **传输加密**: 所有API调用必须使用HTTPS
- **数据脱敏**: 敏感信息在日志中自动脱敏
- **访问审计**: 记录所有API调用的详细审计日志

---

## 三阶段开发路线

### 📅 阶段一：Claude-Code完全集成（8周）

#### 目标
完整集成Claude-Code项目，建立与Chatwoot的基础通信能力

#### 关键任务

```mermaid
gantt
    title 阶段一开发计划
    dateFormat  YYYY-MM-DD
    section 环境搭建
    开发环境配置        :a1, 2025-08-18, 1w
    Claude-Code部署     :a2, after a1, 1w
    
    section 适配器开发
    消息处理适配器      :b1, after a2, 2w
    API接口开发        :b2, after b1, 2w
    
    section 集成测试
    基础功能测试       :c1, after b2, 1w
    Chatwoot集成测试   :c2, after c1, 1w
```

#### 交付成果
1. **可运行的AI服务**：基于Claude-Code的独立AI服务
2. **基础API接口**：消息处理、状态查询等核心接口
3. **集成验证**：与Chatwoot的基础通信验证通过
4. **部署文档**：完整的部署和配置文档

#### 验收标准
- AI服务能够接收和处理Chatwoot消息
- 基础的智能回复功能正常工作
- API响应时间 < 3秒，可用性 > 99%
- 完整的错误处理和日志记录

### 📅 阶段二：功能精简和优化（6周）

#### 目标
移除Claude-Code中与客服场景无关的功能，优化系统性能

#### 关键任务

```mermaid
gantt
    title 阶段二开发计划
    dateFormat  YYYY-MM-DD
    section 功能分析
    功能模块评估       :a1, 2025-10-13, 1w
    冗余功能识别       :a2, after a1, 1w
    
    section 系统优化
    代码重构          :b1, after a2, 2w
    性能优化          :b2, after b1, 1w
    
    section 稳定性测试
    系统测试          :c1, after b2, 1w
```

#### 优化重点
1. **移除开发工具功能**：Git操作、代码分析等与客服无关的功能
2. **简化工具注册**：只保留客服业务相关的工具类型
3. **优化AI调用**：针对客服对话场景优化提示词和参数
4. **提升响应速度**：缓存优化、批处理、异步处理

#### 交付成果
- **精简版AI服务**：移除冗余功能后的稳定版本
- **性能报告**：响应时间、资源使用等性能指标
- **稳定性验证**：长时间运行的稳定性测试报告

### 📅 阶段三：新功能开发规划（12周）

#### 目标
基于PRD文档规划，开发策略工作台和AI副驾面板功能

#### 功能模块规划

```mermaid
graph TB
    subgraph "策略工作台开发"
        A[多维表格引擎] --> A1[表格配置管理]
        A --> A2[单元格编辑器]
        A --> A3[数据可视化]
        
        B[触发器系统] --> B1[条件评估引擎]
        B --> B2[规则配置界面]
        B --> B3[触发历史记录]
        
        C[工具箱系统] --> C1[工具分类管理]
        C --> C2[参数配置界面]
        C --> C3[执行结果跟踪]
    end
    
    subgraph "AI副驾面板开发"
        D[旅程罗盘] --> D1[路径可视化]
        D --> D2[状态跟踪]
        D --> D3[预测算法]
        
        E[智能建议] --> E1[多策略生成]
        E --> E2[个性化适配]
        E --> E3[效果评估]
        
        F[意图分析] --> F1[实时识别]
        F --> F2[置信度计算]
        F --> F3[历史趋势]
    end
```

#### 开发优先级
1. **高优先级**：策略工作台核心功能、智能建议系统
2. **中优先级**：旅程罗盘、意图分析引擎
3. **低优先级**：高级可视化、复杂分析功能

---

## 技术架构设计

### 🏗️ 整体架构

```mermaid
graph TB
    subgraph "AI服务架构"
        A[API网关层] --> B[业务逻辑层]
        B --> C[AI引擎层]
        C --> D[数据访问层]
        
        A1[认证中间件] --> A
        A2[限流中间件] --> A
        A3[日志中间件] --> A
        
        B1[消息处理器] --> B
        B2[策略执行器] --> B
        B3[状态管理器] --> B
        
        C1[Claude-Code引擎] --> C
        C2[工具调用框架] --> C
        C3[Hook系统] --> C
        
        D1[数据库连接池] --> D
        D2[缓存管理] --> D
        D3[外部API客户端] --> D
    end
    
    subgraph "外部系统"
        E[Chatwoot API]
        F[Anthropic API]
        G[PostgreSQL]
        H[Redis]
    end
    
    D --> E
    C --> F
    D --> G
    D --> H
```

### 🔧 核心组件设计

#### 1. 消息处理器
- **职责**：处理来自Chatwoot的消息，协调AI推理和响应生成
- **特性**：异步处理、错误重试、状态跟踪

#### 2. 策略执行器
- **职责**：执行策略工作台配置的自动化策略
- **特性**：规则引擎、条件评估、动作执行

#### 3. 状态管理器
- **职责**：管理对话状态、用户画像、AI代理状态
- **特性**：实时同步、持久化存储、状态恢复

#### 4. 工具适配层
- **职责**：将Claude-Code的通用工具适配为Chatwoot特定操作
- **特性**：插件化架构、动态加载、权限控制

### 📊 数据模型设计

#### 核心数据实体

```mermaid
erDiagram
    CONVERSATION {
        string id PK
        number chatwoot_conversation_id
        string current_strategy_id
        json context_data
        string status
        datetime created_at
        datetime updated_at
    }
    
    AI_SESSION {
        string id PK
        string conversation_id FK
        string agent_state
        json processing_context
        number confidence_level
        datetime last_activity
    }
    
    STRATEGY_CONFIG {
        string id PK
        string name
        json trigger_rules
        json action_definitions
        json data_columns
        boolean is_active
    }
    
    EXECUTION_LOG {
        string id PK
        string conversation_id FK
        string action_type
        json input_data
        json output_data
        string status
        datetime executed_at
    }
    
    CONVERSATION ||--|| AI_SESSION : has
    CONVERSATION ||--o{ EXECUTION_LOG : generates
    STRATEGY_CONFIG ||--o{ EXECUTION_LOG : triggers
```

---

## 开发环境配置

### 🛠️ 技术栈和依赖

#### 核心技术栈

```yaml
运行环境:
  Node.js: 18.17.0+
  TypeScript: 5.0+
  包管理器: npm 9+ 或 yarn 3+

Web框架:
  主框架: Express.js 4.18+ 或 Fastify 4.0+
  中间件: cors, helmet, compression, rate-limiter
  验证: joi 或 zod (数据验证)
  认证: jsonwebtoken, passport

数据存储:
  主数据库: PostgreSQL 15+ (与Chatwoot共享)
  缓存: Redis 7+ (会话状态、AI推理缓存)
  消息队列: RabbitMQ 3.11+ 或 Redis Streams
  搜索引擎: Elasticsearch 8+ (可选，用于日志分析)

AI和机器学习:
  Claude API: @anthropic-ai/sdk 0.20+
  向量处理: @tensorflow/tfjs 4.0+ 或 faiss-node
  自然语言处理: natural 6.0+, compromise 14.0+
  数据科学: pandas-js, ml-matrix
```

#### 项目结构规范

```
ai-service/
├── src/
│   ├── controllers/          # API控制器
│   │   ├── conversation.controller.ts
│   │   ├── strategy.controller.ts
│   │   └── webhook.controller.ts
│   ├── services/            # 业务逻辑服务
│   │   ├── ai/             # AI相关服务
│   │   │   ├── claude-adapter.service.ts
│   │   │   ├── intent-analyzer.service.ts
│   │   │   └── response-generator.service.ts
│   │   ├── integration/    # 集成服务
│   │   │   ├── chatwoot-client.service.ts
│   │   │   └── webhook-handler.service.ts
│   │   └── strategy/       # 策略执行服务
│   │       ├── strategy-engine.service.ts
│   │       └── rule-evaluator.service.ts
│   ├── models/             # 数据模型
│   │   ├── conversation.model.ts
│   │   ├── strategy.model.ts
│   │   └── ai-session.model.ts
│   ├── middleware/         # 中间件
│   │   ├── auth.middleware.ts
│   │   ├── validation.middleware.ts
│   │   └── rate-limit.middleware.ts
│   ├── utils/              # 工具函数
│   │   ├── logger.ts
│   │   ├── cache.ts
│   │   └── metrics.ts
│   ├── config/             # 配置文件
│   │   ├── database.ts
│   │   ├── redis.ts
│   │   └── ai-models.ts
│   └── app.ts              # 应用入口
├── tests/                  # 测试文件
│   ├── unit/
│   ├── integration/
│   └── e2e/
├── docs/                   # 文档
│   ├── api/               # API文档
│   └── deployment/        # 部署文档
├── scripts/               # 脚本文件
│   ├── setup.sh
│   └── migrate.ts
├── docker/                # Docker配置
│   ├── Dockerfile
│   ├── docker-compose.yml
│   └── docker-compose.dev.yml
├── .github/               # GitHub Actions
│   └── workflows/
├── package.json
├── tsconfig.json
├── .eslintrc.js
├── .prettierrc
└── README.md
```

### 🏗️ 本地开发环境搭建

#### 1. 环境准备

```bash
# 1. 克隆项目
git clone https://github.com/your-org/chatwoot-ai-service.git
cd chatwoot-ai-service

# 2. 安装依赖
npm install

# 3. 复制环境变量配置
cp .env.example .env.local

# 4. 启动依赖服务
docker-compose -f docker-compose.dev.yml up -d postgres redis rabbitmq

# 5. 运行数据库迁移
npm run migrate

# 6. 启动开发服务器
npm run dev
```

#### 2. 环境变量配置

```bash
# .env.local - 本地开发环境配置

# 基础配置
NODE_ENV=development
PORT=4000
LOG_LEVEL=debug

# Anthropic API配置
ANTHROPIC_API_KEY=your_anthropic_api_key_here
CLAUDE_MODEL=claude-3-sonnet-20240229
ANTHROPIC_API_BASE_URL=https://api.anthropic.com

# 数据库配置
DATABASE_URL=postgresql://ai_user:ai_password@localhost:5432/chatwoot_ai_dev
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10
DATABASE_TIMEOUT=30000

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_CACHE_DB=1
REDIS_SESSION_DB=2
REDIS_QUEUE_DB=3

# RabbitMQ配置
RABBITMQ_URL=amqp://guest:guest@localhost:5672
RABBITMQ_EXCHANGE=ai_service_exchange
RABBITMQ_QUEUE_PREFIX=ai_service

# Chatwoot集成配置
CHATWOOT_API_URL=http://localhost:3000
CHATWOOT_API_TOKEN=your_chatwoot_api_token
CHATWOOT_WEBHOOK_SECRET=your_webhook_secret
CHATWOOT_TIMEOUT=30000

# AI服务配置
AI_CACHE_TTL=3600
AI_MAX_CONTEXT_LENGTH=8000
AI_DEFAULT_TEMPERATURE=0.7
AI_MAX_TOKENS=2000
AI_REQUEST_TIMEOUT=30000
AI_MAX_CONCURRENT_REQUESTS=50

# 安全配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=24h
API_RATE_LIMIT=1000
API_RATE_WINDOW=3600

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_HEALTH_CHECK=true
HEALTH_CHECK_INTERVAL=30

# 开发工具配置
ENABLE_API_DOCS=true
ENABLE_DEBUG_LOGS=true
ENABLE_MOCK_AI=false
```

#### 3. 数据库初始化

```sql
-- 创建AI服务专用数据库和用户
CREATE DATABASE chatwoot_ai_dev;
CREATE USER ai_user WITH PASSWORD 'ai_password';
GRANT ALL PRIVILEGES ON DATABASE chatwoot_ai_dev TO ai_user;

-- 创建必要的扩展
\c chatwoot_ai_dev;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建AI服务数据表
CREATE TABLE ai_conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    chatwoot_conversation_id BIGINT NOT NULL UNIQUE,
    current_strategy_id VARCHAR(255),
    context_data JSONB DEFAULT '{}',
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE ai_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID REFERENCES ai_conversations(id),
    agent_state VARCHAR(50) DEFAULT 'idle',
    processing_context JSONB DEFAULT '{}',
    confidence_level DECIMAL(3,2) DEFAULT 0.0,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE strategy_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    trigger_rules JSONB DEFAULT '[]',
    action_definitions JSONB DEFAULT '[]',
    data_columns JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE execution_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID REFERENCES ai_conversations(id),
    strategy_id UUID REFERENCES strategy_configs(id),
    action_type VARCHAR(100) NOT NULL,
    input_data JSONB DEFAULT '{}',
    output_data JSONB DEFAULT '{}',
    status VARCHAR(50) DEFAULT 'pending',
    execution_time DECIMAL(8,3),
    error_message TEXT,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_ai_conversations_chatwoot_id ON ai_conversations(chatwoot_conversation_id);
CREATE INDEX idx_ai_sessions_conversation_id ON ai_sessions(conversation_id);
CREATE INDEX idx_execution_logs_conversation_id ON execution_logs(conversation_id);
CREATE INDEX idx_execution_logs_executed_at ON execution_logs(executed_at);
```

### 🔗 与Chatwoot开发环境联调

#### 1. Chatwoot Webhook配置

```ruby
# 在Chatwoot开发环境中配置Webhook
# config/environments/development.rb

Rails.application.configure do
  # AI服务配置
  config.ai_service_url = ENV.fetch('AI_SERVICE_URL', 'http://localhost:4000')
  config.ai_service_token = ENV.fetch('AI_SERVICE_TOKEN', 'dev_token_123')
  config.ai_service_timeout = 30

  # Webhook配置
  config.ai_webhook_url = "#{config.ai_service_url}/webhooks/chatwoot"
  config.ai_webhook_secret = ENV.fetch('AI_WEBHOOK_SECRET', 'dev_secret_456')
end
```

#### 2. 本地网络配置

```yaml
# docker-compose.dev.yml - 开发环境服务编排
version: '3.8'

networks:
  chatwoot-ai-network:
    driver: bridge

services:
  # AI服务
  ai-service:
    build:
      context: .
      dockerfile: docker/Dockerfile.dev
    ports:
      - "4000:4000"
      - "9090:9090"  # Metrics端口
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**********************************************/chatwoot_ai_dev
      - REDIS_URL=redis://redis:6379
      - CHATWOOT_API_URL=http://chatwoot:3000
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - chatwoot-ai-network
    depends_on:
      - postgres
      - redis
      - rabbitmq

  # 数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=chatwoot_ai_dev
      - POSTGRES_USER=ai_user
      - POSTGRES_PASSWORD=ai_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - chatwoot-ai-network

  # Redis
  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - chatwoot-ai-network

  # RabbitMQ
  rabbitmq:
    image: rabbitmq:3.11-management-alpine
    ports:
      - "5673:5672"
      - "15673:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - chatwoot-ai-network

  # Chatwoot (用于联调)
  chatwoot:
    image: chatwoot/chatwoot:latest
    ports:
      - "3001:3000"
    environment:
      - DATABASE_URL=*****************************************************/chatwoot
      - REDIS_URL=redis://chatwoot-redis:6379
      - AI_SERVICE_URL=http://ai-service:4000
    networks:
      - chatwoot-ai-network
    depends_on:
      - chatwoot-postgres
      - chatwoot-redis

  chatwoot-postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=chatwoot
      - POSTGRES_USER=chatwoot
      - POSTGRES_PASSWORD=password
    volumes:
      - chatwoot_postgres_data:/var/lib/postgresql/data
    networks:
      - chatwoot-ai-network

  chatwoot-redis:
    image: redis:7-alpine
    volumes:
      - chatwoot_redis_data:/data
    networks:
      - chatwoot-ai-network

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
  chatwoot_postgres_data:
  chatwoot_redis_data:
```

### 🧪 测试数据和模拟环境

#### 1. 测试数据生成

```typescript
// scripts/generate-test-data.ts
import { faker } from '@faker-js/faker';
import { DatabaseService } from '../src/services/database.service';

interface TestDataConfig {
  conversations: number;
  messagesPerConversation: number;
  strategies: number;
}

class TestDataGenerator {
  constructor(private db: DatabaseService) {}

  async generateTestData(config: TestDataConfig) {
    console.log('开始生成测试数据...');

    // 生成策略配置
    const strategies = await this.generateStrategies(config.strategies);
    console.log(`生成了 ${strategies.length} 个策略配置`);

    // 生成对话数据
    const conversations = await this.generateConversations(config.conversations);
    console.log(`生成了 ${conversations.length} 个对话`);

    // 生成消息数据
    for (const conversation of conversations) {
      await this.generateMessages(conversation.id, config.messagesPerConversation);
    }
    console.log(`为每个对话生成了 ${config.messagesPerConversation} 条消息`);

    console.log('测试数据生成完成！');
  }

  private async generateStrategies(count: number) {
    const strategies = [];

    for (let i = 0; i < count; i++) {
      const strategy = {
        name: faker.company.buzzPhrase(),
        description: faker.lorem.paragraph(),
        trigger_rules: [
          {
            type: 'keyword',
            conditions: [faker.lorem.word(), faker.lorem.word()],
            operator: 'OR'
          },
          {
            type: 'sentiment',
            threshold: faker.number.float({ min: 0.1, max: 0.9 }),
            operator: 'GT'
          }
        ],
        action_definitions: [
          {
            type: 'send_reply',
            template: faker.lorem.sentences(2),
            priority: faker.number.int({ min: 1, max: 10 })
          },
          {
            type: 'add_tag',
            tags: [faker.lorem.word(), faker.lorem.word()],
            priority: faker.number.int({ min: 1, max: 10 })
          }
        ],
        is_active: faker.datatype.boolean()
      };

      const result = await this.db.query(
        'INSERT INTO strategy_configs (name, description, trigger_rules, action_definitions, is_active) VALUES ($1, $2, $3, $4, $5) RETURNING *',
        [strategy.name, strategy.description, JSON.stringify(strategy.trigger_rules), JSON.stringify(strategy.action_definitions), strategy.is_active]
      );

      strategies.push(result.rows[0]);
    }

    return strategies;
  }

  private async generateConversations(count: number) {
    const conversations = [];

    for (let i = 0; i < count; i++) {
      const conversation = {
        chatwoot_conversation_id: faker.number.int({ min: 1000, max: 9999 }),
        current_strategy_id: faker.string.uuid(),
        context_data: {
          customer_tier: faker.helpers.arrayElement(['basic', 'premium', 'enterprise']),
          interaction_count: faker.number.int({ min: 1, max: 50 }),
          last_sentiment: faker.number.float({ min: -1, max: 1 }),
          topics: faker.helpers.arrayElements(['pricing', 'support', 'features', 'billing'], { min: 1, max: 3 })
        },
        status: faker.helpers.arrayElement(['active', 'paused', 'completed'])
      };

      const result = await this.db.query(
        'INSERT INTO ai_conversations (chatwoot_conversation_id, current_strategy_id, context_data, status) VALUES ($1, $2, $3, $4) RETURNING *',
        [conversation.chatwoot_conversation_id, conversation.current_strategy_id, JSON.stringify(conversation.context_data), conversation.status]
      );

      conversations.push(result.rows[0]);
    }

    return conversations;
  }

  private async generateMessages(conversationId: string, count: number) {
    for (let i = 0; i < count; i++) {
      const executionLog = {
        conversation_id: conversationId,
        action_type: faker.helpers.arrayElement(['send_reply', 'add_tag', 'transfer_conversation', 'create_note']),
        input_data: {
          message_content: faker.lorem.sentences(faker.number.int({ min: 1, max: 3 })),
          sender_type: faker.helpers.arrayElement(['contact', 'user']),
          timestamp: faker.date.recent().toISOString()
        },
        output_data: {
          success: faker.datatype.boolean(),
          response_time: faker.number.float({ min: 0.1, max: 5.0 }),
          confidence: faker.number.float({ min: 0.5, max: 1.0 })
        },
        status: faker.helpers.arrayElement(['completed', 'failed', 'pending']),
        execution_time: faker.number.float({ min: 0.1, max: 10.0 })
      };

      await this.db.query(
        'INSERT INTO execution_logs (conversation_id, action_type, input_data, output_data, status, execution_time) VALUES ($1, $2, $3, $4, $5, $6)',
        [executionLog.conversation_id, executionLog.action_type, JSON.stringify(executionLog.input_data), JSON.stringify(executionLog.output_data), executionLog.status, executionLog.execution_time]
      );
    }
  }
}

// 运行脚本
async function main() {
  const db = new DatabaseService();
  const generator = new TestDataGenerator(db);

  await generator.generateTestData({
    conversations: 100,
    messagesPerConversation: 20,
    strategies: 10
  });

  await db.close();
}

if (require.main === module) {
  main().catch(console.error);
}
```

#### 2. Mock AI服务

```typescript
// src/services/mock/mock-ai.service.ts
export class MockAIService {
  private responses = [
    "感谢您的咨询，我来为您详细解答。",
    "根据您的需求，我推荐以下解决方案...",
    "这是一个很好的问题，让我为您分析一下。",
    "我理解您的关切，这里有几个选择供您参考。"
  ];

  private intents = [
    { name: 'product_inquiry', confidence: 0.85 },
    { name: 'pricing_question', confidence: 0.92 },
    { name: 'technical_support', confidence: 0.78 },
    { name: 'billing_issue', confidence: 0.88 }
  ];

  async processMessage(message: string): Promise<any> {
    // 模拟AI处理延迟
    await this.delay(faker.number.int({ min: 500, max: 2000 }));

    const intent = faker.helpers.arrayElement(this.intents);
    const response = faker.helpers.arrayElement(this.responses);

    return {
      success: true,
      actions: [
        {
          type: 'send_reply',
          priority: 1,
          data: {
            content: response,
            confidence: intent.confidence
          }
        }
      ],
      ai_insights: {
        intent: intent,
        sentiment: {
          polarity: faker.helpers.arrayElement(['positive', 'neutral', 'negative']),
          score: faker.number.float({ min: -1, max: 1 })
        },
        suggested_responses: [
          {
            type: 'efficient',
            content: `简洁回复：${response}`,
            estimated_satisfaction: faker.number.float({ min: 0.7, max: 0.95 })
          },
          {
            type: 'empathetic',
            content: `同理心回复：我完全理解您的感受。${response}`,
            estimated_satisfaction: faker.number.float({ min: 0.8, max: 0.98 })
          }
        ]
      }
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

#### 3. 开发工具配置

```json
{
  "scripts": {
    "dev": "nodemon --exec ts-node src/app.ts",
    "build": "tsc",
    "start": "node dist/app.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/**/*.ts",
    "lint:fix": "eslint src/**/*.ts --fix",
    "format": "prettier --write src/**/*.ts",
    "migrate": "ts-node scripts/migrate.ts",
    "seed": "ts-node scripts/generate-test-data.ts",
    "docs": "swagger-jsdoc -d swaggerDef.js src/controllers/*.ts -o docs/api/swagger.json",
    "docker:dev": "docker-compose -f docker-compose.dev.yml up -d",
    "docker:down": "docker-compose -f docker-compose.dev.yml down"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "@types/express": "^4.17.17",
    "@types/jest": "^29.5.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "eslint": "^8.45.0",
    "eslint-config-prettier": "^8.8.0",
    "eslint-plugin-prettier": "^5.0.0",
    "jest": "^29.6.0",
    "nodemon": "^3.0.0",
    "prettier": "^3.0.0",
    "supertest": "^6.3.0",
    "swagger-jsdoc": "^6.2.8",
    "swagger-ui-express": "^5.0.0",
    "ts-jest": "^29.1.0",
    "ts-node": "^10.9.0",
    "typescript": "^5.1.0"
  }
}
```

---

## 部署和运维

### 🚀 生产环境部署架构

#### 1. 容器化部署配置

```yaml
# docker-compose.prod.yml - 生产环境配置
version: '3.8'

networks:
  ai-service-network:
    driver: bridge
  monitoring-network:
    driver: bridge

services:
  # AI服务 - 多实例部署
  ai-service-1:
    image: your-registry/chatwoot-ai-service:latest
    container_name: ai-service-1
    ports:
      - "4001:4000"
    environment:
      - NODE_ENV=production
      - INSTANCE_ID=ai-service-1
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config:ro
    networks:
      - ai-service-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  ai-service-2:
    image: your-registry/chatwoot-ai-service:latest
    container_name: ai-service-2
    ports:
      - "4002:4000"
    environment:
      - NODE_ENV=production
      - INSTANCE_ID=ai-service-2
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config:ro
    networks:
      - ai-service-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # Nginx负载均衡器
  nginx:
    image: nginx:alpine
    container_name: ai-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - ai-service-network
    depends_on:
      - ai-service-1
      - ai-service-2
    restart: unless-stopped

  # Redis集群
  redis-master:
    image: redis:7-alpine
    container_name: redis-master
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --replica-read-only no
    volumes:
      - redis_master_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - ai-service-network
    restart: unless-stopped

  redis-replica:
    image: redis:7-alpine
    container_name: redis-replica
    ports:
      - "6380:6379"
    command: redis-server --replicaof redis-master 6379 --appendonly yes
    volumes:
      - redis_replica_data:/data
    networks:
      - ai-service-network
    depends_on:
      - redis-master
    restart: unless-stopped

  # RabbitMQ集群
  rabbitmq:
    image: rabbitmq:3.11-management-alpine
    container_name: rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASS}
      - RABBITMQ_ERLANG_COOKIE=${RABBITMQ_COOKIE}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
    networks:
      - ai-service-network
    restart: unless-stopped

volumes:
  redis_master_data:
  redis_replica_data:
  rabbitmq_data:
```

#### 2. Nginx负载均衡配置

```nginx
# nginx/nginx.conf
upstream ai_service_backend {
    least_conn;
    server ai-service-1:4000 max_fails=3 fail_timeout=30s;
    server ai-service-2:4000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

server {
    listen 80;
    listen 443 ssl http2;
    server_name ai-service.yourdomain.com;

    # SSL配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # 日志配置
    access_log /var/log/nginx/ai-service.access.log;
    error_log /var/log/nginx/ai-service.error.log;

    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
    limit_req zone=api burst=20 nodelay;

    # API路由
    location /api/ {
        proxy_pass http://ai_service_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓存配置
        proxy_cache api_cache;
        proxy_cache_valid 200 5m;
        proxy_cache_key $scheme$proxy_host$request_uri;
    }

    # WebSocket路由
    location /ws/ {
        proxy_pass http://ai_service_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket特定配置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }

    # 健康检查
    location /health {
        proxy_pass http://ai_service_backend;
        access_log off;
    }

    # 静态文件
    location /docs/ {
        alias /var/www/docs/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
}

# 缓存配置
proxy_cache_path /var/cache/nginx/api levels=1:2 keys_zone=api_cache:10m max_size=1g inactive=60m use_temp_path=off;
```

### 🔍 服务发现和注册

#### 1. Consul服务发现

```yaml
# consul/docker-compose.yml
version: '3.8'

services:
  consul:
    image: consul:1.15
    container_name: consul
    ports:
      - "8500:8500"
      - "8600:8600/udp"
    environment:
      - CONSUL_BIND_INTERFACE=eth0
    command: >
      consul agent -server -bootstrap-expect=1 -ui -client=0.0.0.0
      -data-dir=/consul/data -config-dir=/consul/config
    volumes:
      - consul_data:/consul/data
      - ./consul/config:/consul/config:ro
    networks:
      - ai-service-network
    restart: unless-stopped

volumes:
  consul_data:
```

#### 2. 服务注册配置

```typescript
// src/services/service-discovery.service.ts
import Consul from 'consul';

export class ServiceDiscoveryService {
  private consul: Consul.Consul;
  private serviceId: string;

  constructor() {
    this.consul = new Consul({
      host: process.env.CONSUL_HOST || 'localhost',
      port: process.env.CONSUL_PORT || '8500'
    });
    this.serviceId = `ai-service-${process.env.INSTANCE_ID || 'default'}`;
  }

  async registerService(): Promise<void> {
    const serviceConfig = {
      id: this.serviceId,
      name: 'ai-service',
      tags: ['ai', 'chatwoot', 'claude-code'],
      address: process.env.SERVICE_HOST || 'localhost',
      port: parseInt(process.env.PORT || '4000'),
      check: {
        http: `http://${process.env.SERVICE_HOST || 'localhost'}:${process.env.PORT || '4000'}/health`,
        interval: '30s',
        timeout: '10s',
        deregistercriticalserviceafter: '90s'
      },
      meta: {
        version: process.env.SERVICE_VERSION || '1.0.0',
        environment: process.env.NODE_ENV || 'development'
      }
    };

    try {
      await this.consul.agent.service.register(serviceConfig);
      console.log(`服务 ${this.serviceId} 注册成功`);
    } catch (error) {
      console.error('服务注册失败:', error);
      throw error;
    }
  }

  async deregisterService(): Promise<void> {
    try {
      await this.consul.agent.service.deregister(this.serviceId);
      console.log(`服务 ${this.serviceId} 注销成功`);
    } catch (error) {
      console.error('服务注销失败:', error);
    }
  }

  async discoverServices(serviceName: string): Promise<any[]> {
    try {
      const result = await this.consul.health.service({
        service: serviceName,
        passing: true
      });
      return result;
    } catch (error) {
      console.error('服务发现失败:', error);
      return [];
    }
  }
}
```

### 📊 监控、日志和告警系统

#### 1. Prometheus监控配置

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'ai-service'
    static_configs:
      - targets: ['ai-service-1:9090', 'ai-service-2:9090']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-master:9121', 'redis-replica:9121']

  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15692']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### 2. 告警规则配置

```yaml
# monitoring/alert_rules.yml
groups:
  - name: ai-service-alerts
    rules:
      # API响应时间告警
      - alert: HighAPIResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "AI服务API响应时间过高"
          description: "95%的请求响应时间超过5秒，当前值: {{ $value }}秒"

      # 错误率告警
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "AI服务错误率过高"
          description: "错误率超过5%，当前值: {{ $value | humanizePercentage }}"

      # AI推理失败率告警
      - alert: HighAIFailureRate
        expr: rate(ai_inference_failures_total[5m]) / rate(ai_inference_requests_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "AI推理失败率过高"
          description: "AI推理失败率超过10%，当前值: {{ $value | humanizePercentage }}"

      # 系统资源告警
      - alert: HighCPUUsage
        expr: cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "CPU使用率超过80%，当前值: {{ $value }}%"

      - alert: HighMemoryUsage
        expr: memory_usage_percent > 85
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "内存使用率过高"
          description: "内存使用率超过85%，当前值: {{ $value }}%"

      # 服务可用性告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务不可用"
          description: "服务 {{ $labels.instance }} 已停止响应"

  - name: business-alerts
    rules:
      # 业务指标告警
      - alert: LowUserSatisfaction
        expr: avg_over_time(user_satisfaction_score[1h]) < 3.5
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "用户满意度下降"
          description: "过去1小时平均用户满意度低于3.5分，当前值: {{ $value }}"

      # 成本控制告警
      - alert: HighAICost
        expr: increase(ai_api_cost_total[1h]) > 100
        for: 0m
        labels:
          severity: warning
        annotations:
          summary: "AI API成本过高"
          description: "过去1小时AI API成本超过100元，当前值: {{ $value }}元"
```

#### 3. Grafana仪表板配置

```json
{
  "dashboard": {
    "title": "AI服务监控仪表板",
    "panels": [
      {
        "title": "API请求量",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "响应时间分布",
        "type": "heatmap",
        "targets": [
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          },
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "AI推理指标",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(ai_inference_requests_total[5m])",
            "legendFormat": "推理请求/秒"
          },
          {
            "expr": "avg(ai_inference_confidence)",
            "legendFormat": "平均置信度"
          }
        ]
      },
      {
        "title": "系统资源使用",
        "type": "graph",
        "targets": [
          {
            "expr": "cpu_usage_percent",
            "legendFormat": "CPU使用率"
          },
          {
            "expr": "memory_usage_percent",
            "legendFormat": "内存使用率"
          }
        ]
      }
    ]
  }
}
```

#### 4. 日志管理配置

```yaml
# logging/docker-compose.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - monitoring-network

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: logstash
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline:ro
      - ./logstash/config:/usr/share/logstash/config:ro
      - ../logs:/var/log/ai-service:ro
    ports:
      - "5044:5044"
    networks:
      - monitoring-network
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    networks:
      - monitoring-network
    depends_on:
      - elasticsearch

volumes:
  elasticsearch_data:
```

### 🔐 安全认证和权限控制

#### 1. JWT认证中间件

```typescript
// src/middleware/auth.middleware.ts
import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    accountId: string;
    role: string;
    permissions: string[];
  };
}

export class AuthMiddleware {
  static authenticate(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'MISSING_TOKEN',
          message: '缺少认证令牌'
        }
      });
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      req.user = decoded;
      next();
    } catch (error) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: '无效的认证令牌'
        }
      });
    }
  }

  static authorize(permissions: string[]) {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未认证用户'
          }
        });
      }

      const hasPermission = permissions.some(permission =>
        req.user!.permissions.includes(permission)
      );

      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: '权限不足'
          }
        });
      }

      next();
    };
  }
}
```

#### 2. API密钥管理

```typescript
// src/services/api-key.service.ts
import crypto from 'crypto';
import { DatabaseService } from './database.service';

export class APIKeyService {
  constructor(private db: DatabaseService) {}

  async generateAPIKey(accountId: string, name: string, permissions: string[]): Promise<string> {
    const apiKey = `ak_${crypto.randomBytes(32).toString('hex')}`;
    const hashedKey = crypto.createHash('sha256').update(apiKey).digest('hex');

    await this.db.query(
      'INSERT INTO api_keys (account_id, name, key_hash, permissions, created_at) VALUES ($1, $2, $3, $4, NOW())',
      [accountId, name, hashedKey, JSON.stringify(permissions)]
    );

    return apiKey;
  }

  async validateAPIKey(apiKey: string): Promise<any> {
    const hashedKey = crypto.createHash('sha256').update(apiKey).digest('hex');

    const result = await this.db.query(
      'SELECT * FROM api_keys WHERE key_hash = $1 AND is_active = true',
      [hashedKey]
    );

    if (result.rows.length === 0) {
      throw new Error('Invalid API key');
    }

    // 更新最后使用时间
    await this.db.query(
      'UPDATE api_keys SET last_used_at = NOW() WHERE key_hash = $1',
      [hashedKey]
    );

    return result.rows[0];
  }

  async revokeAPIKey(apiKey: string): Promise<void> {
    const hashedKey = crypto.createHash('sha256').update(apiKey).digest('hex');

    await this.db.query(
      'UPDATE api_keys SET is_active = false, revoked_at = NOW() WHERE key_hash = $1',
      [hashedKey]
    );
  }
}
```

#### 3. 数据加密配置

```typescript
// src/utils/encryption.ts
import crypto from 'crypto';

export class EncryptionService {
  private algorithm = 'aes-256-gcm';
  private secretKey: Buffer;

  constructor() {
    this.secretKey = crypto.scryptSync(process.env.ENCRYPTION_KEY!, 'salt', 32);
  }

  encrypt(text: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.secretKey);
    cipher.setAAD(Buffer.from('ai-service', 'utf8'));

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
  }

  decrypt(encryptedText: string): string {
    const [ivHex, authTagHex, encrypted] = encryptedText.split(':');

    const iv = Buffer.from(ivHex, 'hex');
    const authTag = Buffer.from(authTagHex, 'hex');

    const decipher = crypto.createDecipher(this.algorithm, this.secretKey);
    decipher.setAAD(Buffer.from('ai-service', 'utf8'));
    decipher.setAuthTag(authTag);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }
}
```

---

## 服务架构设计

### 🏗️ 微服务架构模式

#### 独立部署架构

```mermaid
graph TB
    subgraph "Chatwoot主应用集群"
        A[Chatwoot Web App] --> B[Rails API Server]
        B --> C[PostgreSQL主库]
        B --> D[Redis缓存]

        E[Nginx负载均衡器] --> A
        F[Chatwoot Worker] --> B
    end

    subgraph "AI服务集群"
        G[AI Service Gateway] --> H[AI Service Instance 1]
        G --> I[AI Service Instance 2]
        G --> J[AI Service Instance N]

        H --> K[Claude-Code Engine]
        I --> L[Claude-Code Engine]
        J --> M[Claude-Code Engine]

        N[AI Redis Cluster] --> H
        N --> I
        N --> J
    end

    subgraph "共享基础设施"
        O[PostgreSQL读写分离]
        P[消息队列 RabbitMQ]
        Q[监控系统 Prometheus]
        R[日志系统 ELK]
    end

    B -->|HTTP/gRPC| G
    G -->|响应| B

    H --> O
    I --> O
    J --> O

    B --> P
    G --> P

    style G fill:#e1f5fe
    style H fill:#f3e5f5
    style I fill:#f3e5f5
    style J fill:#f3e5f5
```

#### 架构设计原则

1. **服务自治**：AI服务完全独立部署，拥有独立的生命周期
2. **数据隔离**：AI服务拥有独立的数据存储，通过API与主应用交互
3. **故障隔离**：AI服务故障不影响Chatwoot核心功能
4. **弹性扩展**：支持根据负载动态扩缩容

### 🔗 服务间通信协议

#### 1. 同步通信 - REST API

**协议规范**：
- **传输协议**：HTTPS (TLS 1.3)
- **数据格式**：JSON
- **字符编码**：UTF-8
- **压缩算法**：gzip
- **超时设置**：连接超时5s，读取超时30s

**请求头规范**：
```http
Content-Type: application/json; charset=utf-8
Accept: application/json
Authorization: Bearer <JWT_TOKEN>
X-Request-ID: <UUID>
X-API-Version: v1
X-Client-Version: chatwoot-2.0.0
User-Agent: Chatwoot-AI-Client/1.0
```

#### 2. 异步通信 - 消息队列

**消息格式规范**：
```json
{
  "message_id": "uuid",
  "timestamp": "2025-08-18T10:30:00Z",
  "event_type": "conversation.message.created",
  "source": "chatwoot",
  "target": "ai-service",
  "data": {
    "conversation_id": "12345",
    "message": {
      "id": 67890,
      "content": "用户消息内容",
      "message_type": "incoming",
      "created_at": "2025-08-18T10:29:55Z"
    },
    "context": {
      "account_id": 1,
      "inbox_id": 2,
      "contact_id": 3
    }
  },
  "retry_count": 0,
  "max_retries": 3
}
```

#### 3. 实时通信 - WebSocket

**连接建立**：
```javascript
// WebSocket连接URL格式
wss://ai-service.domain.com/ws/v1/conversations/{conversation_id}
  ?token={jwt_token}
  &client_id={client_id}
```

**消息协议**：
```json
{
  "type": "ai_status_update",
  "conversation_id": "12345",
  "data": {
    "agent_status": "thinking",
    "confidence": 0.85,
    "estimated_completion": "2025-08-18T10:30:15Z"
  }
}
```

### ⚖️ 负载均衡和容错

#### 负载均衡策略

1. **API网关层**：
   - 使用Nginx或HAProxy进行L7负载均衡
   - 支持加权轮询、最少连接、IP哈希等算法
   - 健康检查：每30秒检查一次服务状态

2. **服务发现**：
   - 使用Consul或etcd进行服务注册和发现
   - 支持动态服务注册和注销
   - 自动故障检测和流量切换

#### 容错机制

```yaml
容错策略:
  熔断器:
    失败阈值: 50%
    最小请求数: 20
    熔断时间: 60秒
    半开状态请求数: 5

  重试策略:
    最大重试次数: 3
    重试间隔: 指数退避 (1s, 2s, 4s)
    重试条件: 5xx错误或网络超时

  降级策略:
    AI服务不可用: 返回预设回复模板
    响应超时: 切换到简化处理模式
    负载过高: 启用请求限流
```

### 📈 扩展性设计

#### 水平扩展

```mermaid
graph LR
    subgraph "扩展前"
        A[AI Service × 2] --> B[处理能力: 1000 req/min]
    end

    subgraph "扩展后"
        C[AI Service × 6] --> D[处理能力: 3000 req/min]
    end

    subgraph "自动扩展触发条件"
        E[CPU使用率 > 70%]
        F[内存使用率 > 80%]
        G[响应时间 > 5s]
        H[队列长度 > 100]
    end

    E --> C
    F --> C
    G --> C
    H --> C
```

#### 垂直扩展

```yaml
资源配置规格:
  小型实例:
    CPU: 2核
    内存: 4GB
    并发处理: 50个请求
    适用场景: 开发测试环境

  中型实例:
    CPU: 4核
    内存: 8GB
    并发处理: 100个请求
    适用场景: 中小型生产环境

  大型实例:
    CPU: 8核
    内存: 16GB
    并发处理: 200个请求
    适用场景: 大型生产环境
```

---

## API接口规范

### 📋 标准化REST API接口

#### 1. 消息处理接口

**接口定义**：
```http
POST /api/v1/conversations/{conversation_id}/process
```

**请求参数**：
```json
{
  "message": {
    "id": 67890,
    "content": "用户询问内容",
    "message_type": "incoming|outgoing",
    "sender": {
      "type": "contact|user",
      "id": 12345,
      "name": "张三",
      "email": "<EMAIL>"
    },
    "created_at": "2025-08-18T10:29:55Z",
    "attachments": [
      {
        "type": "image|file|audio",
        "url": "https://example.com/file.jpg",
        "filename": "screenshot.jpg",
        "size": 1024000
      }
    ]
  },
  "context": {
    "account_id": 1,
    "inbox_id": 2,
    "contact_id": 3,
    "agent_id": 4,
    "conversation_status": "open|resolved|pending",
    "priority": "low|medium|high|urgent",
    "labels": ["vip", "technical-support"],
    "custom_attributes": {
      "customer_tier": "premium",
      "product_interest": "enterprise"
    }
  },
  "options": {
    "enable_ai_response": true,
    "response_mode": "auto|suggest|manual",
    "max_response_time": 30,
    "preferred_language": "zh-CN"
  }
}
```

**响应格式**：
```json
{
  "success": true,
  "request_id": "req_uuid_12345",
  "processing_time": 2.5,
  "actions": [
    {
      "type": "send_reply",
      "priority": 1,
      "data": {
        "content": "AI生成的回复内容",
        "content_type": "text|markdown",
        "confidence": 0.92,
        "reasoning": "基于用户历史和当前问题的分析"
      }
    },
    {
      "type": "add_labels",
      "priority": 2,
      "data": {
        "labels": ["ai-processed", "product-inquiry"]
      }
    },
    {
      "type": "update_contact",
      "priority": 3,
      "data": {
        "custom_attributes": {
          "last_ai_interaction": "2025-08-18T10:30:00Z",
          "interaction_count": 5
        }
      }
    }
  ],
  "ai_insights": {
    "intent": {
      "primary": "product_inquiry",
      "secondary": "pricing_question",
      "confidence": 0.89
    },
    "sentiment": {
      "polarity": "positive",
      "score": 0.7,
      "emotions": ["curiosity", "interest"]
    },
    "suggested_responses": [
      {
        "type": "efficient",
        "content": "直接回答用户问题的简洁回复",
        "estimated_satisfaction": 0.85
      },
      {
        "type": "empathetic",
        "content": "富有同理心的详细回复",
        "estimated_satisfaction": 0.92
      },
      {
        "type": "exploratory",
        "content": "引导用户深入交流的开放性问题",
        "estimated_satisfaction": 0.78
      }
    ],
    "next_best_actions": [
      "schedule_demo",
      "send_pricing_info",
      "transfer_to_sales"
    ]
  },
  "context_updates": {
    "conversation_stage": "product_evaluation",
    "user_journey_position": "consideration",
    "engagement_score": 8.5,
    "predicted_conversion_probability": 0.65
  }
}
```

#### 2. 策略执行接口

**接口定义**：
```http
POST /api/v1/strategies/{strategy_id}/execute
```

**请求参数**：
```json
{
  "trigger_event": {
    "type": "message_received|status_changed|time_based|behavior_detected",
    "timestamp": "2025-08-18T10:30:00Z",
    "data": {
      "conversation_id": "12345",
      "trigger_conditions": {
        "keywords": ["价格", "费用"],
        "sentiment_threshold": 0.5,
        "time_since_last_message": 300
      }
    }
  },
  "execution_context": {
    "conversation_id": "12345",
    "user_profile": {
      "id": 67890,
      "segment": "enterprise",
      "lifecycle_stage": "prospect",
      "engagement_history": {
        "total_interactions": 15,
        "avg_response_time": 120,
        "satisfaction_score": 4.2
      }
    },
    "current_strategy_state": {
      "active_node": "price_inquiry_handling",
      "completed_steps": ["initial_greeting", "needs_assessment"],
      "pending_actions": ["send_pricing_doc", "schedule_call"]
    }
  }
}
```

**响应格式**：
```json
{
  "success": true,
  "execution_id": "exec_uuid_67890",
  "strategy_result": {
    "executed_actions": [
      {
        "action_type": "send_message",
        "status": "completed",
        "execution_time": 1.2,
        "result": {
          "message_id": 98765,
          "delivery_status": "sent"
        }
      },
      {
        "action_type": "update_user_tags",
        "status": "completed",
        "execution_time": 0.3,
        "result": {
          "added_tags": ["price_inquirer", "high_intent"]
        }
      }
    ],
    "next_strategy_node": "pricing_follow_up",
    "estimated_completion_time": "2025-08-18T11:00:00Z",
    "success_probability": 0.78
  },
  "performance_metrics": {
    "execution_time": 2.1,
    "resource_usage": {
      "cpu_time": 0.5,
      "memory_peak": "128MB",
      "api_calls": 3
    },
    "cost_breakdown": {
      "ai_inference": 0.02,
      "api_calls": 0.001,
      "total": 0.021
    }
  }
}
```

#### 3. 实时状态查询接口

**接口定义**：
```http
GET /api/v1/conversations/{conversation_id}/ai-status
```

**响应格式**：
```json
{
  "conversation_id": "12345",
  "ai_agent_status": {
    "current_state": "processing|idle|thinking|responding",
    "last_activity": "2025-08-18T10:29:55Z",
    "processing_queue_position": 3,
    "estimated_response_time": 15,
    "confidence_level": 0.87,
    "active_tasks": [
      {
        "task_id": "task_uuid_111",
        "task_type": "intent_analysis",
        "progress": 0.8,
        "estimated_completion": "2025-08-18T10:30:10Z"
      },
      {
        "task_id": "task_uuid_222",
        "task_type": "response_generation",
        "progress": 0.3,
        "estimated_completion": "2025-08-18T10:30:25Z"
      }
    ]
  },
  "strategy_status": {
    "active_strategy": "customer_onboarding_v2",
    "current_node": "needs_assessment",
    "completion_percentage": 0.45,
    "next_milestone": "product_demo_scheduling",
    "predicted_outcomes": {
      "conversion_probability": 0.72,
      "estimated_deal_value": 15000,
      "timeline_to_close": "14 days"
    }
  },
  "real_time_insights": {
    "user_engagement_score": 8.2,
    "conversation_momentum": "increasing",
    "optimal_response_window": "2-5 minutes",
    "recommended_tone": "professional_friendly"
  }
}
```

### 🚨 错误处理和状态码规范

#### HTTP状态码使用规范

```yaml
成功响应:
  200 OK: 请求成功处理
  201 Created: 资源创建成功
  202 Accepted: 请求已接受，异步处理中
  204 No Content: 请求成功，无返回内容

客户端错误:
  400 Bad Request: 请求参数错误
  401 Unauthorized: 认证失败
  403 Forbidden: 权限不足
  404 Not Found: 资源不存在
  409 Conflict: 资源冲突
  422 Unprocessable Entity: 业务逻辑错误
  429 Too Many Requests: 请求频率超限

服务端错误:
  500 Internal Server Error: 服务器内部错误
  502 Bad Gateway: 上游服务错误
  503 Service Unavailable: 服务暂时不可用
  504 Gateway Timeout: 上游服务超时
```

#### 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "AI_PROCESSING_FAILED",
    "message": "AI处理请求时发生错误",
    "details": "Claude API调用超时，请稍后重试",
    "timestamp": "2025-08-18T10:30:00Z",
    "request_id": "req_uuid_12345",
    "trace_id": "trace_uuid_67890"
  },
  "retry_info": {
    "retryable": true,
    "retry_after": 30,
    "max_retries": 3,
    "backoff_strategy": "exponential"
  },
  "support_info": {
    "documentation_url": "https://docs.ai-service.com/errors/AI_PROCESSING_FAILED",
    "contact_support": "<EMAIL>"
  }
}
```

### 📝 API版本管理

#### 版本控制策略

1. **URL路径版本控制**：`/api/v1/`, `/api/v2/`
2. **向后兼容性**：新版本保持至少6个月的向后兼容
3. **废弃通知**：通过响应头提前3个月通知API废弃

```http
# 响应头示例
API-Version: v1
API-Deprecated: false
API-Sunset-Date: 2025-12-31
API-Migration-Guide: https://docs.ai-service.com/migration/v1-to-v2
```

#### 版本升级路径

```mermaid
graph LR
    A[v1.0 - 基础功能] --> B[v1.1 - 功能增强]
    B --> C[v1.2 - 性能优化]
    C --> D[v2.0 - 架构升级]

    A -.->|兼容6个月| D
    B -.->|兼容6个月| D
    C -.->|兼容6个月| D
```

---

## 集成通信机制

### 🔗 Webhook配置和消息推送

#### Webhook注册机制

**注册接口**：
```http
POST /api/v1/webhooks/register
```

**请求参数**：
```json
{
  "webhook_url": "https://ai-service.domain.com/webhooks/chatwoot",
  "events": [
    "conversation.created",
    "conversation.status_changed",
    "message.created",
    "message.updated",
    "contact.created",
    "contact.updated"
  ],
  "secret": "webhook_secret_key",
  "retry_policy": {
    "max_retries": 3,
    "retry_interval": 5,
    "backoff_multiplier": 2
  },
  "filters": {
    "account_ids": [1, 2, 3],
    "inbox_ids": [10, 20, 30],
    "message_types": ["incoming"]
  }
}
```

#### Webhook消息格式

```json
{
  "webhook_id": "webhook_uuid_123",
  "event_type": "conversation.message.created",
  "timestamp": "2025-08-18T10:30:00Z",
  "account_id": 1,
  "data": {
    "conversation": {
      "id": 12345,
      "status": "open",
      "inbox_id": 2,
      "contact_id": 3,
      "assignee_id": 4,
      "created_at": "2025-08-18T09:00:00Z",
      "updated_at": "2025-08-18T10:30:00Z"
    },
    "message": {
      "id": 67890,
      "content": "用户消息内容",
      "message_type": "incoming",
      "created_at": "2025-08-18T10:29:55Z",
      "sender": {
        "type": "contact",
        "id": 3,
        "name": "张三"
      }
    }
  },
  "signature": "sha256=webhook_signature_hash"
}
```

#### Webhook安全验证

```javascript
// 签名验证示例
const crypto = require('crypto');

function verifyWebhookSignature(payload, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');

  return `sha256=${expectedSignature}` === signature;
}
```

### 🔄 实时通信WebSocket

#### 连接管理

```mermaid
sequenceDiagram
    participant Client as Chatwoot客户端
    participant Gateway as WebSocket网关
    participant AI as AI服务
    participant Auth as 认证服务

    Client->>Gateway: 建立WebSocket连接
    Gateway->>Auth: 验证JWT Token
    Auth->>Gateway: 返回用户信息
    Gateway->>AI: 注册客户端连接
    AI->>Gateway: 确认注册成功
    Gateway->>Client: 连接建立成功

    Note over Client,AI: 实时消息交换
    AI->>Gateway: 推送AI状态更新
    Gateway->>Client: 转发状态更新

    Client->>Gateway: 发送用户操作
    Gateway->>AI: 转发用户操作
    AI->>Gateway: 返回处理结果
    Gateway->>Client: 返回处理结果
```

#### 消息类型定义

```json
{
  "ai_status_update": {
    "type": "ai_status_update",
    "conversation_id": "12345",
    "data": {
      "status": "thinking|processing|responding|idle",
      "progress": 0.65,
      "estimated_completion": "2025-08-18T10:30:15Z"
    }
  },

  "suggestion_ready": {
    "type": "suggestion_ready",
    "conversation_id": "12345",
    "data": {
      "suggestions": [
        {
          "type": "reply",
          "content": "建议的回复内容",
          "confidence": 0.89
        }
      ]
    }
  },

  "strategy_trigger": {
    "type": "strategy_trigger",
    "conversation_id": "12345",
    "data": {
      "strategy_id": "customer_onboarding",
      "trigger_reason": "用户询问产品价格",
      "recommended_actions": ["send_pricing_doc", "schedule_demo"]
    }
  }
}
```

### 📨 消息队列和异步处理

#### 消息队列架构

```mermaid
graph TB
    subgraph "消息生产者"
        A[Chatwoot应用] --> B[消息发布器]
        B --> C[RabbitMQ Exchange]
    end

    subgraph "消息路由"
        C --> D[对话处理队列]
        C --> E[策略执行队列]
        C --> F[批量分析队列]
        C --> G[通知推送队列]
    end

    subgraph "消息消费者"
        D --> H[AI消息处理器]
        E --> I[策略执行器]
        F --> J[批量分析器]
        G --> K[通知服务]
    end

    subgraph "结果处理"
        H --> L[结果回调]
        I --> L
        J --> L
        K --> L
        L --> M[Chatwoot回调接口]
    end
```

#### 消息队列配置

```yaml
队列配置:
  对话处理队列:
    名称: ai.conversation.process
    持久化: true
    优先级支持: true
    最大长度: 10000
    TTL: 300秒
    死信队列: ai.conversation.dlq

  策略执行队列:
    名称: ai.strategy.execute
    持久化: true
    优先级支持: false
    最大长度: 5000
    TTL: 600秒
    死信队列: ai.strategy.dlq

  批量分析队列:
    名称: ai.batch.analyze
    持久化: true
    优先级支持: false
    最大长度: 1000
    TTL: 3600秒
    死信队列: ai.batch.dlq
```

#### 异步处理策略

```json
{
  "processing_modes": {
    "real_time": {
      "description": "实时处理，立即响应",
      "max_processing_time": 5,
      "queue_priority": "high",
      "use_cases": ["用户消息回复", "紧急状态变更"]
    },
    "near_real_time": {
      "description": "准实时处理，短延迟",
      "max_processing_time": 30,
      "queue_priority": "medium",
      "use_cases": ["策略触发", "意图分析"]
    },
    "batch": {
      "description": "批量处理，定时执行",
      "max_processing_time": 3600,
      "queue_priority": "low",
      "use_cases": ["数据分析", "报告生成"]
    }
  }
}
```

### 🔄 数据同步和一致性保证

#### 数据同步策略

```mermaid
graph TB
    subgraph "Chatwoot数据"
        A[用户数据] --> D[数据同步服务]
        B[对话数据] --> D
        C[配置数据] --> D
    end

    subgraph "AI服务数据"
        E[用户画像] --> D
        F[对话上下文] --> D
        G[策略状态] --> D
    end

    subgraph "同步机制"
        D --> H[增量同步]
        D --> I[全量同步]
        D --> J[冲突解决]
        D --> K[一致性检查]
    end

    subgraph "一致性保证"
        L[分布式锁] --> D
        M[事务日志] --> D
        N[补偿机制] --> D
    end
```

#### 一致性保证机制

1. **最终一致性**：
   - 使用事件溯源模式记录所有数据变更
   - 通过补偿事务处理数据不一致问题
   - 定期执行一致性检查和修复

2. **分布式锁**：
   - 使用Redis实现分布式锁
   - 防止并发修改导致的数据冲突
   - 支持锁超时和自动释放

3. **数据版本控制**：
   - 为关键数据添加版本号
   - 使用乐观锁处理并发更新
   - 记录数据变更历史

```json
{
  "data_consistency_config": {
    "sync_interval": 60,
    "conflict_resolution": "last_write_wins",
    "consistency_check_interval": 3600,
    "max_sync_retries": 3,
    "sync_timeout": 30,
    "enable_compensation": true
  }
}
```

---

## 总结

本开发指南为独立AI服务团队提供了基于Claude-Code集成Chatwoot的完整技术路线。通过三阶段渐进式开发，最终实现PRD文档中规划的智能私域运营平台功能。

### 🎯 关键成功因素
1. **深入理解Claude-Code架构**，充分利用其AI代理能力
2. **设计清晰的集成接口**，确保与Chatwoot的稳定通信
3. **采用渐进式开发策略**，降低技术风险和实施复杂度
4. **建立完善的监控体系**，保证服务质量和用户体验

---

## 集成验证

### 🧪 集成测试用例和验收标准

#### 1. 功能测试用例

```typescript
// tests/integration/chatwoot-integration.test.ts
import { TestClient } from '../utils/test-client';
import { MockChatwootAPI } from '../mocks/chatwoot-api.mock';

describe('Chatwoot集成测试', () => {
  let testClient: TestClient;
  let mockChatwoot: MockChatwootAPI;

  beforeAll(async () => {
    testClient = new TestClient();
    mockChatwoot = new MockChatwootAPI();
    await testClient.setup();
    await mockChatwoot.setup();
  });

  afterAll(async () => {
    await testClient.teardown();
    await mockChatwoot.teardown();
  });

  describe('消息处理集成', () => {
    test('应该成功处理来自Chatwoot的消息', async () => {
      // 准备测试数据
      const testMessage = {
        conversation_id: '12345',
        message: {
          id: 67890,
          content: '我想了解产品价格',
          message_type: 'incoming',
          sender: {
            type: 'contact',
            id: 123,
            name: '测试用户'
          }
        },
        context: {
          account_id: 1,
          inbox_id: 2,
          contact_id: 123
        }
      };

      // 发送请求
      const response = await testClient.post('/api/v1/conversations/12345/process', testMessage);

      // 验证响应
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.actions).toHaveLength(1);
      expect(response.body.actions[0].type).toBe('send_reply');
      expect(response.body.ai_insights.intent.primary).toBe('pricing_inquiry');
      expect(response.body.processing_time).toBeLessThan(5);
    });

    test('应该正确处理多媒体消息', async () => {
      const testMessage = {
        conversation_id: '12345',
        message: {
          id: 67891,
          content: '这是我的问题截图',
          message_type: 'incoming',
          attachments: [
            {
              type: 'image',
              url: 'https://example.com/screenshot.jpg',
              filename: 'screenshot.jpg'
            }
          ]
        }
      };

      const response = await testClient.post('/api/v1/conversations/12345/process', testMessage);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.ai_insights.content_analysis).toBeDefined();
    });

    test('应该处理消息处理失败的情况', async () => {
      const invalidMessage = {
        conversation_id: '12345',
        message: {
          id: 67892,
          content: '', // 空消息
          message_type: 'incoming'
        }
      };

      const response = await testClient.post('/api/v1/conversations/12345/process', invalidMessage);

      expect(response.status).toBe(422);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INVALID_MESSAGE_CONTENT');
    });
  });

  describe('策略执行集成', () => {
    test('应该成功执行自动化策略', async () => {
      // 创建测试策略
      const strategy = await testClient.createTestStrategy({
        name: '价格咨询自动回复',
        trigger_rules: [
          {
            type: 'keyword',
            conditions: ['价格', '费用', '多少钱'],
            operator: 'OR'
          }
        ],
        action_definitions: [
          {
            type: 'send_reply',
            template: '感谢您的咨询，我们的产品价格如下...',
            priority: 1
          }
        ]
      });

      const executionRequest = {
        trigger_event: {
          type: 'message_received',
          data: {
            conversation_id: '12345',
            trigger_conditions: {
              keywords: ['价格']
            }
          }
        },
        execution_context: {
          conversation_id: '12345',
          user_profile: {
            id: 123,
            segment: 'prospect'
          }
        }
      };

      const response = await testClient.post(`/api/v1/strategies/${strategy.id}/execute`, executionRequest);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.strategy_result.executed_actions).toHaveLength(1);
      expect(response.body.strategy_result.executed_actions[0].status).toBe('completed');
    });
  });

  describe('实时通信集成', () => {
    test('应该建立WebSocket连接并接收状态更新', async () => {
      const wsClient = await testClient.connectWebSocket('/ws/v1/conversations/12345');

      // 发送消息触发AI处理
      await testClient.post('/api/v1/conversations/12345/process', {
        message: { content: '测试消息' }
      });

      // 等待WebSocket消息
      const statusUpdate = await wsClient.waitForMessage('ai_status_update', 5000);

      expect(statusUpdate.type).toBe('ai_status_update');
      expect(statusUpdate.data.status).toBeOneOf(['thinking', 'processing', 'responding']);
      expect(statusUpdate.data.progress).toBeGreaterThanOrEqual(0);

      await wsClient.close();
    });
  });

  describe('Webhook集成', () => {
    test('应该正确处理Chatwoot Webhook', async () => {
      const webhookPayload = {
        event_type: 'conversation.message.created',
        account_id: 1,
        data: {
          conversation: {
            id: 12345,
            status: 'open'
          },
          message: {
            id: 67890,
            content: '用户消息',
            message_type: 'incoming'
          }
        }
      };

      const signature = mockChatwoot.generateWebhookSignature(webhookPayload);

      const response = await testClient.post('/webhooks/chatwoot', webhookPayload, {
        headers: {
          'X-Chatwoot-Signature': signature
        }
      });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    test('应该拒绝无效签名的Webhook', async () => {
      const webhookPayload = {
        event_type: 'conversation.message.created',
        data: {}
      };

      const response = await testClient.post('/webhooks/chatwoot', webhookPayload, {
        headers: {
          'X-Chatwoot-Signature': 'invalid_signature'
        }
      });

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe('INVALID_WEBHOOK_SIGNATURE');
    });
  });
});
```

#### 2. 性能测试用例

```typescript
// tests/performance/load-test.ts
import { performance } from 'perf_hooks';
import { TestClient } from '../utils/test-client';

describe('性能测试', () => {
  let testClient: TestClient;

  beforeAll(async () => {
    testClient = new TestClient();
    await testClient.setup();
  });

  test('并发消息处理性能测试', async () => {
    const concurrentRequests = 50;
    const testMessage = {
      conversation_id: '12345',
      message: {
        content: '性能测试消息',
        message_type: 'incoming'
      }
    };

    const startTime = performance.now();

    const promises = Array(concurrentRequests).fill(null).map(() =>
      testClient.post('/api/v1/conversations/12345/process', testMessage)
    );

    const responses = await Promise.all(promises);
    const endTime = performance.now();

    const totalTime = endTime - startTime;
    const avgResponseTime = totalTime / concurrentRequests;

    // 验证性能指标
    expect(avgResponseTime).toBeLessThan(3000); // 平均响应时间 < 3秒
    expect(responses.every(r => r.status === 200)).toBe(true); // 所有请求成功

    console.log(`并发测试结果: ${concurrentRequests}个请求，总耗时${totalTime}ms，平均响应时间${avgResponseTime}ms`);
  });

  test('内存使用测试', async () => {
    const initialMemory = process.memoryUsage();

    // 执行大量请求
    for (let i = 0; i < 1000; i++) {
      await testClient.post('/api/v1/conversations/12345/process', {
        message: { content: `测试消息 ${i}` }
      });
    }

    const finalMemory = process.memoryUsage();
    const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;

    // 验证内存增长在合理范围内
    expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024); // < 100MB
  });
});
```

### 📊 性能基准和SLA要求

#### 1. 性能基准指标

```yaml
性能基准:
  API响应时间:
    消息处理: < 3秒 (95th percentile)
    策略执行: < 5秒 (95th percentile)
    状态查询: < 500ms (95th percentile)
    健康检查: < 100ms (99th percentile)

  吞吐量:
    并发请求: 100 req/s (单实例)
    消息处理: 50 msg/s (单实例)
    WebSocket连接: 1000 concurrent connections

  资源使用:
    CPU使用率: < 70% (正常负载)
    内存使用: < 2GB (单实例)
    磁盘I/O: < 100MB/s
    网络带宽: < 50MB/s

  可用性:
    服务可用性: 99.9%
    API成功率: 99.5%
    数据一致性: 99.99%
```

#### 2. SLA服务等级协议

```yaml
SLA要求:
  可用性保证:
    月度可用性: 99.9% (最多43.2分钟停机)
    计划维护窗口: 每月第一个周日 02:00-04:00
    紧急维护: 提前24小时通知

  性能保证:
    API响应时间: 95%的请求 < 3秒
    错误率: < 0.5%
    数据丢失率: 0%

  支持响应:
    P0 (系统完全不可用): 15分钟内响应，2小时内解决
    P1 (核心功能受影响): 1小时内响应，8小时内解决
    P2 (部分功能受影响): 4小时内响应，24小时内解决
    P3 (一般问题): 24小时内响应，72小时内解决

  补偿机制:
    可用性 < 99.9%: 服务费用10%折扣
    可用性 < 99.5%: 服务费用25%折扣
    可用性 < 99.0%: 服务费用50%折扣
```

### 🛡️ 故障恢复和降级策略

#### 1. 故障检测机制

```typescript
// src/services/health-check.service.ts
export class HealthCheckService {
  private checks: Map<string, HealthCheck> = new Map();

  constructor() {
    this.registerChecks();
  }

  private registerChecks() {
    // 数据库连接检查
    this.checks.set('database', {
      name: 'Database Connection',
      check: async () => {
        try {
          await this.db.query('SELECT 1');
          return { status: 'healthy', latency: 0 };
        } catch (error) {
          return { status: 'unhealthy', error: error.message };
        }
      },
      timeout: 5000,
      critical: true
    });

    // Redis连接检查
    this.checks.set('redis', {
      name: 'Redis Connection',
      check: async () => {
        try {
          const start = Date.now();
          await this.redis.ping();
          const latency = Date.now() - start;
          return { status: 'healthy', latency };
        } catch (error) {
          return { status: 'unhealthy', error: error.message };
        }
      },
      timeout: 3000,
      critical: true
    });

    // AI服务检查
    this.checks.set('ai_service', {
      name: 'AI Service',
      check: async () => {
        try {
          const start = Date.now();
          await this.aiService.healthCheck();
          const latency = Date.now() - start;
          return { status: 'healthy', latency };
        } catch (error) {
          return { status: 'unhealthy', error: error.message };
        }
      },
      timeout: 10000,
      critical: false
    });

    // 外部API检查
    this.checks.set('chatwoot_api', {
      name: 'Chatwoot API',
      check: async () => {
        try {
          const response = await this.chatwootClient.healthCheck();
          return { status: 'healthy', response_time: response.time };
        } catch (error) {
          return { status: 'unhealthy', error: error.message };
        }
      },
      timeout: 5000,
      critical: false
    });
  }

  async runHealthChecks(): Promise<HealthCheckResult> {
    const results: Record<string, any> = {};
    let overallStatus = 'healthy';

    for (const [name, check] of this.checks) {
      try {
        const result = await Promise.race([
          check.check(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Timeout')), check.timeout)
          )
        ]);

        results[name] = result;

        if (result.status === 'unhealthy' && check.critical) {
          overallStatus = 'unhealthy';
        } else if (result.status === 'unhealthy' && overallStatus === 'healthy') {
          overallStatus = 'degraded';
        }
      } catch (error) {
        results[name] = { status: 'unhealthy', error: error.message };
        if (check.critical) {
          overallStatus = 'unhealthy';
        }
      }
    }

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      checks: results,
      uptime: process.uptime(),
      version: process.env.SERVICE_VERSION || '1.0.0'
    };
  }
}
```

#### 2. 降级策略实现

```typescript
// src/services/circuit-breaker.service.ts
export class CircuitBreakerService {
  private breakers: Map<string, CircuitBreaker> = new Map();

  constructor() {
    this.initializeBreakers();
  }

  private initializeBreakers() {
    // AI服务熔断器
    this.breakers.set('ai_service', new CircuitBreaker({
      name: 'AI Service',
      failureThreshold: 5,
      recoveryTimeout: 60000,
      monitoringPeriod: 10000,
      fallback: async (error) => {
        console.warn('AI服务不可用，启用降级模式:', error.message);
        return this.getDefaultAIResponse();
      }
    }));

    // 外部API熔断器
    this.breakers.set('chatwoot_api', new CircuitBreaker({
      name: 'Chatwoot API',
      failureThreshold: 3,
      recoveryTimeout: 30000,
      monitoringPeriod: 5000,
      fallback: async (error) => {
        console.warn('Chatwoot API不可用，启用缓存模式:', error.message);
        return this.getCachedResponse();
      }
    }));
  }

  async executeWithBreaker<T>(
    breakerName: string,
    operation: () => Promise<T>
  ): Promise<T> {
    const breaker = this.breakers.get(breakerName);
    if (!breaker) {
      throw new Error(`Circuit breaker ${breakerName} not found`);
    }

    return breaker.execute(operation);
  }

  private async getDefaultAIResponse(): Promise<any> {
    return {
      success: true,
      actions: [
        {
          type: 'send_reply',
          data: {
            content: '抱歉，AI服务暂时不可用，请稍后再试或联系人工客服。',
            confidence: 0.5
          }
        }
      ],
      ai_insights: {
        intent: { primary: 'unknown', confidence: 0.5 },
        sentiment: { polarity: 'neutral', score: 0 }
      },
      degraded: true
    };
  }

  private async getCachedResponse(): Promise<any> {
    // 从缓存获取最近的响应模板
    const cachedTemplate = await this.cache.get('fallback_response_template');
    return cachedTemplate || this.getDefaultAIResponse();
  }
}
```

#### 3. 自动恢复机制

```typescript
// src/services/auto-recovery.service.ts
export class AutoRecoveryService {
  private recoveryStrategies: Map<string, RecoveryStrategy> = new Map();

  constructor() {
    this.initializeStrategies();
    this.startMonitoring();
  }

  private initializeStrategies() {
    // 数据库连接恢复
    this.recoveryStrategies.set('database_connection', {
      detect: () => this.healthCheck.isDatabaseUnhealthy(),
      recover: async () => {
        console.log('尝试重新连接数据库...');
        await this.database.reconnect();
        await this.database.testConnection();
      },
      maxRetries: 5,
      retryInterval: 10000
    });

    // Redis连接恢复
    this.recoveryStrategies.set('redis_connection', {
      detect: () => this.healthCheck.isRedisUnhealthy(),
      recover: async () => {
        console.log('尝试重新连接Redis...');
        await this.redis.reconnect();
        await this.redis.ping();
      },
      maxRetries: 3,
      retryInterval: 5000
    });

    // 内存泄漏恢复
    this.recoveryStrategies.set('memory_leak', {
      detect: () => this.getMemoryUsage() > 0.9, // 90%内存使用率
      recover: async () => {
        console.log('检测到内存使用过高，执行垃圾回收...');
        global.gc && global.gc();
        await this.clearCaches();
      },
      maxRetries: 1,
      retryInterval: 60000
    });
  }

  private startMonitoring() {
    setInterval(async () => {
      for (const [name, strategy] of this.recoveryStrategies) {
        try {
          if (await strategy.detect()) {
            await this.executeRecovery(name, strategy);
          }
        } catch (error) {
          console.error(`恢复策略 ${name} 执行失败:`, error);
        }
      }
    }, 30000); // 每30秒检查一次
  }

  private async executeRecovery(name: string, strategy: RecoveryStrategy) {
    let retries = 0;

    while (retries < strategy.maxRetries) {
      try {
        console.log(`执行恢复策略: ${name} (尝试 ${retries + 1}/${strategy.maxRetries})`);
        await strategy.recover();

        // 验证恢复是否成功
        if (!(await strategy.detect())) {
          console.log(`恢复策略 ${name} 执行成功`);
          return;
        }
      } catch (error) {
        console.error(`恢复策略 ${name} 执行失败:`, error);
      }

      retries++;
      if (retries < strategy.maxRetries) {
        await this.delay(strategy.retryInterval);
      }
    }

    console.error(`恢复策略 ${name} 最终失败，已达到最大重试次数`);
    await this.notifyAdministrators(name, strategy);
  }

  private async notifyAdministrators(strategyName: string, strategy: RecoveryStrategy) {
    // 发送告警通知
    await this.alertService.sendAlert({
      level: 'critical',
      title: `自动恢复失败: ${strategyName}`,
      message: `恢复策略 ${strategyName} 执行失败，需要人工干预`,
      timestamp: new Date().toISOString()
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### 📋 验收标准清单

#### 1. 功能验收标准

```yaml
基础功能验收:
  ✅ 消息处理:
    - 能够接收和处理Chatwoot消息
    - 支持文本、图片、文件等多种消息类型
    - 正确解析消息上下文和元数据
    - 生成符合格式的响应

  ✅ 策略执行:
    - 能够根据触发条件执行策略
    - 支持多种触发器类型（关键词、情感、行为）
    - 正确执行配置的动作序列
    - 记录执行日志和结果

  ✅ 实时通信:
    - WebSocket连接稳定建立和维护
    - 实时推送AI状态更新
    - 支持多客户端并发连接
    - 连接异常自动重连

  ✅ API接口:
    - 所有API接口响应正确
    - 错误处理和状态码规范
    - 请求验证和参数校验
    - API文档完整准确

集成功能验收:
  ✅ Chatwoot集成:
    - Webhook接收和处理正常
    - API调用Chatwoot服务成功
    - 数据同步准确无误
    - 权限验证正确

  ✅ Claude-Code集成:
    - AI代理功能正常工作
    - 工具调用框架运行稳定
    - Hook系统扩展有效
    - 上下文管理准确

  ✅ 数据存储:
    - 数据库连接稳定
    - 数据读写操作正确
    - 缓存机制有效
    - 数据一致性保证
```

#### 2. 性能验收标准

```yaml
性能指标验收:
  ✅ 响应时间:
    - 消息处理 < 3秒 (95th percentile)
    - API查询 < 500ms (95th percentile)
    - WebSocket延迟 < 100ms
    - 健康检查 < 50ms

  ✅ 吞吐量:
    - 支持100并发请求/秒
    - 处理50条消息/秒
    - 维持1000个WebSocket连接
    - 数据库查询 < 100ms

  ✅ 资源使用:
    - CPU使用率 < 70%
    - 内存使用 < 2GB
    - 磁盘I/O < 50MB/s
    - 网络带宽 < 20MB/s

  ✅ 稳定性:
    - 连续运行24小时无崩溃
    - 内存使用稳定无泄漏
    - 错误率 < 0.1%
    - 自动恢复机制有效
```

#### 3. 安全验收标准

```yaml
安全功能验收:
  ✅ 认证授权:
    - JWT令牌验证正确
    - API密钥管理安全
    - 权限控制有效
    - 会话管理安全

  ✅ 数据安全:
    - 敏感数据加密存储
    - 传输层TLS加密
    - 日志脱敏处理
    - 数据备份恢复

  ✅ 网络安全:
    - 防止SQL注入
    - XSS攻击防护
    - CSRF保护
    - 请求限流防护

  ✅ 合规要求:
    - 数据隐私保护
    - 审计日志完整
    - 访问控制记录
    - 安全事件响应
```

---

### 🚀 预期价值
- **技术价值**：构建可扩展的AI服务架构
- **业务价值**：实现智能化客服和私域运营
- **创新价值**：探索人机协同的新模式
