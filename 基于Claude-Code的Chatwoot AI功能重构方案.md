# 基于Claude-Code的Chatwoot AI功能重构方案

版本: 1.0  
日期: 2025-08-18  
作者: AI重构方案设计团队

## 目录
- [项目概述和目标](#项目概述和目标)
- [现状分析](#现状分析)
- [功能重构策略](#功能重构策略)
- [集成架构设计](#集成架构设计)
- [开发实施方案](#开发实施方案)
- [技术实现要点](#技术实现要点)
- [风险评估和应对](#风险评估和应对)

---

## 项目概述和目标

### 🎯 重构目标

**完全替换Chatwoot现有AI功能**，集成Claude-Code项目的实际已实现功能作为新的AI基础架构，然后基于PRD文档规划的功能理念，删除冗余功能并开发新的智能化功能。

### 🔄 核心策略

```mermaid
mindmap
  root((AI功能重构策略))
    移除现有AI功能
      删除OpenAI集成模块
      移除Captain模块
      清理AgentBot框架
      保留基础业务模型
    集成Claude-Code能力
      AI代理系统
      Hook扩展机制
      工具调用框架
      自然语言处理
    基于PRD理念开发
      策略工作台
      AI策略副驾面板
      目标导向智能代理
      人机协同最大化
```

### 📋 重构原则

1. **彻底替换**：完全移除现有AI功能，避免技术债务
2. **能力复用**：充分利用Claude-Code的成熟AI代理能力
3. **理念驱动**：严格按照PRD文档的产品理念设计新功能
4. **渐进实施**：分阶段实施，确保业务连续性

---

## 现状分析

### 📊 Chatwoot现有AI功能评估

#### 需要完全移除的功能模块

```mermaid
graph TB
    subgraph "待移除的AI功能"
        A[OpenAI集成模块] --> A1[reply_suggestion_message]
        A --> A2[summarize_message]
        A --> A3[rephrase_message]
        A --> A4[fix_spelling_grammar]
        A --> A5[make_friendly/formal]
        A --> A6[shorten/expand_message]
        A --> A7[simplify_message]
        
        B[Captain模块] --> B1[AI助手配置管理]
        B --> B2[知识库集成]
        B --> B3[响应模板管理]
        B --> B4[向量搜索能力]
        B --> B5[场景化配置]
        
        C[AgentBot框架] --> C1[外部AI服务集成]
        C --> C2[基于规则的消息路由]
        C --> C3[手动编码业务逻辑]
    end
    
    subgraph "保留的基础能力"
        D[核心业务模型] --> D1[Account/Inbox/Conversation]
        D --> D2[Message/Contact模型]
        D --> D3[多渠道支持]
        D --> D4[用户权限系统]
        
        E[基础设施] --> E1[PostgreSQL数据库]
        E --> E2[Redis缓存]
        E --> E3[Vue.js前端框架]
        E --> E4[Rails后端框架]
    end
    
    style A fill:#ffebee
    style B fill:#ffebee
    style C fill:#ffebee
    style D fill:#e8f5e8
    style E fill:#e8f5e8
```

#### 移除理由分析

| 现有功能 | 移除理由 | 替代方案 |
|---------|---------|---------|
| **OpenAI集成** | 功能单一，缺乏策略化能力 | Claude-Code的智能代理系统 |
| **Captain模块** | 配置复杂，扩展性差 | 基于PRD理念的策略工作台 |
| **AgentBot框架** | 需要手动编码，不够智能 | Hook系统+工具调用框架 |
| **基础AI调用** | 缺乏上下文理解和学习能力 | 目标导向的智能代理 |

### 🔍 Claude-Code实际功能分析

#### 可直接集成的核心能力

```mermaid
graph LR
    subgraph "Claude-Code已实现功能"
        A[AI代理系统] --> A1[自然语言理解]
        A --> A2[任务规划和执行]
        A --> A3[上下文管理]
        A --> A4[多模型支持]
        
        B[工具调用框架] --> B1[工具注册和发现]
        B --> B2[参数验证]
        B --> B3[异步执行]
        B --> B4[结果处理]
        
        C[Hook扩展系统] --> C1[PreToolUse钩子]
        C --> C2[PostToolUse钩子]
        C --> C3[SessionStart钩子]
        C --> C4[自定义验证逻辑]
        
        D[MCP协议支持] --> D1[外部服务集成]
        D --> D2[多种传输方式]
        D --> D3[认证机制]
        D --> D4[服务发现]
    end
    
    subgraph "技术架构优势"
        E[模块化设计] --> E1[高度可扩展]
        E --> E2[松耦合架构]
        
        F[成熟的AI集成] --> F1[Claude/GPT模型支持]
        F --> F2[智能决策能力]
        
        G[企业级特性] --> G1[安全性保障]
        G --> G2[性能优化]
        G --> G3[监控和日志]
    end
```

#### 技术栈兼容性分析

```yaml
Claude-Code技术栈:
  核心语言: TypeScript/JavaScript
  运行环境: Node.js
  架构模式: 事件驱动 + 插件化
  AI集成: Anthropic Claude API
  扩展机制: Hook系统 + MCP协议

Chatwoot技术栈:
  后端: Ruby on Rails
  前端: Vue.js + JavaScript
  数据库: PostgreSQL + Redis
  架构: MVC + 微服务

集成策略:
  方案: Node.js微服务 + Rails主应用
  通信: REST API + WebSocket
  数据: 共享PostgreSQL数据库
  部署: Docker容器化
```

---

## 功能重构策略

### 🗑️ 功能移除计划

#### 阶段一：OpenAI集成模块移除

```ruby
# 需要移除的文件和代码
移除文件:
  - lib/integrations/openai/processor_service.rb
  - lib/integrations/openai/openai_prompts/
  - app/javascript/dashboard/components/widgets/AIAssistant/
  
移除数据库表:
  - 无需移除，OpenAI集成未创建专用表
  
移除配置:
  - config/features.yml 中的 openai 相关配置
  - 环境变量 OPENAI_API_KEY 相关配置
```

#### 阶段二：Captain模块移除

```ruby
# Captain模块移除清单
移除文件:
  - app/controllers/api/v1/accounts/captain/
  - app/javascript/dashboard/api/captain/
  - app/javascript/dashboard/routes/dashboard/captain/
  - lib/integrations/captain/
  
移除数据库表:
  - captain_assistants
  - captain_assistant_responses  
  - captain_documents
  - captain_assistant_inboxes
  
移除配置:
  - config/features.yml 中的 captain_integration
  - app/helpers/super_admin/features.yml 中的 captain
```

#### 阶段三：AgentBot框架清理

```ruby
# AgentBot相关清理
保留基础框架:
  - app/models/agent_bot.rb (保留模型，用于新的AI代理)
  - Webhook处理机制 (用于集成新的AI服务)
  
移除特定实现:
  - lib/integrations/captain/processor_service.rb
  - 特定的bot处理逻辑
```

### 🔧 Claude-Code集成方案

#### 核心集成架构

```mermaid
graph TB
    subgraph "Chatwoot Rails应用"
        A[Rails Controllers] --> B[AI代理调度器]
        B --> C[消息队列]
        C --> D[WebSocket通信]
    end
    
    subgraph "Claude-Code AI服务"
        E[AI代理引擎] --> F[工具调用框架]
        F --> G[Hook系统]
        G --> H[MCP服务集成]
        
        I[策略执行器] --> J[上下文管理器]
        J --> K[学习引擎]
    end
    
    subgraph "新增功能模块"
        L[策略工作台] --> M[多维表格界面]
        M --> N[触发器配置]
        N --> O[工具选择器]
        
        P[AI副驾面板] --> Q[智能建议生成]
        Q --> R[意图分析]
        R --> S[旅程可视化]
    end
    
    D --> E
    E --> I
    I --> L
    L --> P
    
    style E fill:#e1f5fe
    style I fill:#f3e5f5
    style L fill:#e8f5e8
    style P fill:#fff3e0
```

#### 数据模型重构

```sql
-- 新的AI功能数据模型
CREATE TABLE strategy_workbenches (
    id BIGSERIAL PRIMARY KEY,
    account_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    config JSONB NOT NULL DEFAULT '{}',
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

CREATE TABLE operational_nodes (
    id BIGSERIAL PRIMARY KEY,
    workbench_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    position INTEGER NOT NULL,
    triggers JSONB DEFAULT '[]',
    goals JSONB DEFAULT '[]',
    tools JSONB DEFAULT '[]',
    data_columns JSONB DEFAULT '[]',
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

CREATE TABLE ai_agent_sessions (
    id BIGSERIAL PRIMARY KEY,
    conversation_id BIGINT NOT NULL,
    workbench_id BIGINT,
    current_node_id BIGINT,
    context JSONB DEFAULT '{}',
    learning_data JSONB DEFAULT '{}',
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

CREATE TABLE agent_preferences (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    preferences JSONB DEFAULT '{}',
    learning_history JSONB DEFAULT '[]',
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);
```

### 🎯 基于PRD理念的新功能开发

#### 策略工作台实现

```mermaid
graph TB
    subgraph "策略工作台核心功能"
        A[多维表格界面] --> A1[行管理：运营节点]
        A --> A2[列管理：策略维度]
        A --> A3[单元格编辑器]
        A --> A4[数据可视化]
        
        B[触发器系统] --> B1[内容触发器]
        B --> B2[状态触发器]
        B --> B3[行为触发器]
        B --> B4[组合条件]
        
        C[工具箱系统] --> C1[沟通工具]
        C --> C2[运营工具]
        C --> C3[协同工具]
        C --> C4[数据工具]
        
        D[模板中心] --> D1[最佳实践模板]
        D --> D2[一键导入]
        D --> D3[版本管理]
        D --> D4[分享机制]
    end
```

#### AI策略副驾面板实现

```mermaid
graph TB
    subgraph "AI策略副驾面板功能"
        A[动态旅程罗盘] --> A1[用户位置可视化]
        A --> A2[触发原因透明化]
        A --> A3[路径预测]
        A --> A4[节点状态管理]
        
        B[智能回复建议] --> B1[效率最高选项]
        B --> B2[最富同理心选项]
        B --> B3[探索性提问选项]
        B --> B4[优化思路提示]
        
        C[实时意图透镜] --> C1[意图识别]
        C --> C2[置信度分析]
        C --> C3[关键信号高亮]
        C --> C4[历史意图流]
        
        D[任务执行清单] --> D1[自动化任务跟踪]
        D --> D2[人工接管机制]
        D --> D3[执行状态管理]
        D --> D4[智能上报]
    end
```

---

## 集成架构设计

### 🏗️ 整体技术架构

```mermaid
graph TB
    subgraph "前端层"
        A[Vue.js 3 主应用] --> A1[策略工作台组件]
        A --> A2[AI副驾面板组件]
        A --> A3[传统客服界面]
        A --> A4[管理后台界面]
    end

    subgraph "API网关层"
        B[Rails API Gateway] --> B1[认证中间件]
        B --> B2[路由分发]
        B --> B3[请求限流]
        B --> B4[日志记录]
    end

    subgraph "业务服务层"
        C[Rails核心服务] --> C1[用户管理]
        C --> C2[对话管理]
        C --> C3[渠道管理]
        C --> C4[权限控制]

        D[Claude-Code AI服务] --> D1[AI代理引擎]
        D --> D2[策略执行器]
        D --> D3[工具调用框架]
        D --> D4[学习引擎]
    end

    subgraph "数据存储层"
        E[PostgreSQL] --> E1[业务数据]
        E --> E2[策略配置]
        E --> E3[AI会话状态]

        F[Redis] --> F1[缓存数据]
        F --> F2[会话状态]
        F --> F3[消息队列]
    end

    A --> B
    B --> C
    B --> D
    C --> E
    D --> E
    C --> F
    D --> F

    style D fill:#e1f5fe
    style A1 fill:#f3e5f5
    style A2 fill:#e8f5e8
```

### 🔌 服务集成方案

#### Claude-Code服务适配

```typescript
// Claude-Code适配器设计
interface ChatwootAdapter {
  // 消息处理接口
  processMessage(message: ChatwootMessage): Promise<AIResponse>;

  // 策略执行接口
  executeStrategy(workbench: StrategyWorkbench, context: ConversationContext): Promise<StrategyResult>;

  // 工具调用接口
  invokeTool(toolName: string, parameters: any): Promise<ToolResult>;

  // Hook系统接口
  registerHook(hookType: HookType, handler: HookHandler): void;
}

class ChatwootClaudeCodeAdapter implements ChatwootAdapter {
  private claudeAgent: ClaudeAgent;
  private toolRegistry: ToolRegistry;
  private hookSystem: HookSystem;

  constructor() {
    this.claudeAgent = new ClaudeAgent({
      apiKey: process.env.ANTHROPIC_API_KEY,
      model: 'claude-3-sonnet-20240229'
    });

    this.toolRegistry = new ToolRegistry();
    this.hookSystem = new HookSystem();

    this.initializeChatwootTools();
    this.setupHooks();
  }

  async processMessage(message: ChatwootMessage): Promise<AIResponse> {
    // 1. 构建上下文
    const context = await this.buildContext(message);

    // 2. 执行策略评估
    const strategy = await this.evaluateStrategy(context);

    // 3. 调用Claude代理
    const response = await this.claudeAgent.process({
      message: message.content,
      context: context,
      strategy: strategy,
      tools: this.getAvailableTools(strategy)
    });

    return response;
  }

  private initializeChatwootTools(): void {
    // 注册Chatwoot特定工具
    this.toolRegistry.register('send_message', new SendMessageTool());
    this.toolRegistry.register('add_tag', new AddTagTool());
    this.toolRegistry.register('transfer_conversation', new TransferTool());
    this.toolRegistry.register('create_note', new CreateNoteTool());
    this.toolRegistry.register('search_knowledge', new KnowledgeSearchTool());
  }

  private setupHooks(): void {
    // 设置预处理Hook
    this.hookSystem.register('PreToolUse', async (context) => {
      // 权限检查
      await this.checkPermissions(context);

      // 参数验证
      await this.validateParameters(context);

      // 审计日志
      await this.logToolUsage(context);
    });

    // 设置后处理Hook
    this.hookSystem.register('PostToolUse', async (context, result) => {
      // 结果记录
      await this.recordResult(context, result);

      // 学习更新
      await this.updateLearning(context, result);

      // 性能监控
      await this.trackPerformance(context, result);
    });
  }
}
```

#### 数据同步机制

```ruby
# Rails端的AI服务集成
class AiServiceIntegration
  include HTTParty

  base_uri ENV['CLAUDE_CODE_SERVICE_URL']

  def initialize(conversation)
    @conversation = conversation
    @headers = {
      'Content-Type' => 'application/json',
      'Authorization' => "Bearer #{ENV['AI_SERVICE_TOKEN']}"
    }
  end

  def process_message(message)
    payload = {
      conversation_id: @conversation.id,
      message: {
        id: message.id,
        content: message.content,
        sender_type: message.message_type,
        created_at: message.created_at
      },
      context: build_context
    }

    response = self.class.post('/api/v1/process_message',
      body: payload.to_json,
      headers: @headers
    )

    handle_ai_response(response)
  end

  private

  def build_context
    {
      account_id: @conversation.account_id,
      inbox_id: @conversation.inbox_id,
      contact: @conversation.contact.as_json(only: [:id, :name, :email]),
      conversation_history: recent_messages,
      workbench_config: active_workbench&.config,
      agent_preferences: current_agent&.ai_preferences
    }
  end

  def handle_ai_response(response)
    return unless response.success?

    ai_result = JSON.parse(response.body)

    case ai_result['action']
    when 'send_reply'
      create_ai_reply(ai_result['content'])
    when 'transfer_conversation'
      transfer_to_agent(ai_result['agent_id'])
    when 'add_tags'
      add_conversation_tags(ai_result['tags'])
    when 'create_note'
      create_private_note(ai_result['note'])
    end
  end
end
```

### 🎨 前端界面集成

#### 策略工作台界面

```vue
<!-- StrategyWorkbench.vue -->
<template>
  <div class="strategy-workbench">
    <!-- 工作台头部 -->
    <div class="workbench-header">
      <h2>{{ workbench.name }}</h2>
      <div class="header-actions">
        <button @click="saveWorkbench" class="btn-primary">保存策略</button>
        <button @click="testWorkbench" class="btn-secondary">测试运行</button>
        <button @click="showTemplates" class="btn-outline">模板中心</button>
      </div>
    </div>

    <!-- 多维表格 -->
    <div class="strategy-table-container">
      <table class="strategy-table">
        <thead>
          <tr>
            <th class="node-column">运营节点</th>
            <th v-for="dimension in dimensions" :key="dimension.id"
                class="dimension-column">
              {{ dimension.name }}
              <button @click="editDimension(dimension)" class="edit-btn">✏️</button>
            </th>
            <th class="actions-column">
              <button @click="addDimension" class="add-btn">+ 添加列</button>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="node in nodes" :key="node.id" class="node-row">
            <td class="node-cell">
              <div class="node-info">
                <input v-model="node.name" class="node-name-input" />
                <div class="node-stats">
                  <span class="user-count">{{ node.currentUsers || 0 }}人</span>
                  <span class="conversion-rate">{{ node.conversionRate || 0 }}%</span>
                </div>
              </div>
            </td>
            <td v-for="dimension in dimensions" :key="`${node.id}-${dimension.id}`"
                class="strategy-cell">
              <StrategyCellEditor
                :node="node"
                :dimension="dimension"
                @update="updateCell" />
            </td>
            <td class="actions-cell">
              <button @click="deleteNode(node)" class="delete-btn">🗑️</button>
            </td>
          </tr>
          <tr class="add-node-row">
            <td colspan="100%">
              <button @click="addNode" class="add-node-btn">+ 添加运营节点</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 工具箱面板 -->
    <ToolboxPanel
      v-if="showToolbox"
      :available-tools="availableTools"
      @select-tool="selectTool"
      @close="showToolbox = false" />

    <!-- 触发器配置面板 -->
    <TriggerConfigPanel
      v-if="showTriggerConfig"
      :triggers="currentTriggers"
      @update="updateTriggers"
      @close="showTriggerConfig = false" />
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useStrategyWorkbench } from '@/composables/useStrategyWorkbench'
import StrategyCellEditor from './components/StrategyCellEditor.vue'
import ToolboxPanel from './components/ToolboxPanel.vue'
import TriggerConfigPanel from './components/TriggerConfigPanel.vue'

export default {
  name: 'StrategyWorkbench',
  components: {
    StrategyCellEditor,
    ToolboxPanel,
    TriggerConfigPanel
  },
  setup() {
    const {
      workbench,
      nodes,
      dimensions,
      availableTools,
      saveWorkbench,
      testWorkbench,
      addNode,
      deleteNode,
      addDimension,
      updateCell
    } = useStrategyWorkbench()

    const showToolbox = ref(false)
    const showTriggerConfig = ref(false)
    const currentTriggers = ref([])

    return {
      workbench,
      nodes,
      dimensions,
      availableTools,
      showToolbox,
      showTriggerConfig,
      currentTriggers,
      saveWorkbench,
      testWorkbench,
      addNode,
      deleteNode,
      addDimension,
      updateCell
    }
  }
}
</script>
```

#### AI副驾面板界面

```vue
<!-- AICopilotPanel.vue -->
<template>
  <div class="ai-copilot-panel">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="ai-status" :class="aiStatus">
        <span class="status-indicator"></span>
        AI {{ aiStatusText }}
      </div>
      <div class="confidence-score">
        置信度: {{ Math.round(currentConfidence * 100) }}%
      </div>
    </div>

    <!-- 动态旅程罗盘 -->
    <div class="journey-compass">
      <h3>用户旅程</h3>
      <div class="journey-visualization">
        <div class="journey-path">
          <div v-for="(node, index) in journeyNodes"
               :key="node.id"
               :class="['journey-node', {
                 'current': node.id === currentNodeId,
                 'completed': index < currentNodeIndex,
                 'predicted': node.predicted
               }]">
            <div class="node-name">{{ node.name }}</div>
            <div v-if="node.id === currentNodeId" class="trigger-reason">
              触发原因: {{ node.triggerReason }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能回复建议 -->
    <div class="reply-suggestions">
      <h3>智能建议</h3>
      <div class="suggestion-options">
        <div v-for="suggestion in suggestions"
             :key="suggestion.type"
             class="suggestion-card">
          <div class="suggestion-header">
            <span class="suggestion-type">{{ suggestion.typeLabel }}</span>
            <span class="suggestion-score">{{ suggestion.score }}%</span>
          </div>
          <div class="suggestion-content">{{ suggestion.content }}</div>
          <div class="suggestion-actions">
            <button @click="applySuggestion(suggestion)" class="apply-btn">
              应用
            </button>
            <button @click="optimizeSuggestion(suggestion)" class="optimize-btn">
              优化
            </button>
          </div>
          <div v-if="suggestion.optimizationTip" class="optimization-tip">
            💡 {{ suggestion.optimizationTip }}
          </div>
        </div>
      </div>
    </div>

    <!-- 实时意图透镜 -->
    <div class="intent-lens">
      <h3>意图分析</h3>
      <div class="current-intent">
        <div class="intent-primary">
          <span class="intent-label">{{ currentIntent.label }}</span>
          <span class="intent-confidence">{{ currentIntent.confidence }}%</span>
        </div>
        <div class="intent-signals">
          <span v-for="signal in currentIntent.signals"
                :key="signal"
                class="signal-highlight">{{ signal }}</span>
        </div>
      </div>
      <div class="intent-history">
        <h4>意图变化</h4>
        <div class="intent-timeline">
          <div v-for="intent in intentHistory"
               :key="intent.timestamp"
               class="intent-item">
            <span class="intent-time">{{ formatTime(intent.timestamp) }}</span>
            <span class="intent-name">{{ intent.label }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务执行清单 -->
    <div class="task-execution">
      <h3>执行任务</h3>
      <div class="task-list">
        <div v-for="task in executionTasks"
             :key="task.id"
             :class="['task-item', task.status]">
          <div class="task-info">
            <span class="task-name">{{ task.name }}</span>
            <span class="task-status">{{ getStatusText(task.status) }}</span>
          </div>
          <div class="task-actions">
            <button v-if="task.status === 'pending'"
                    @click="executeTask(task)"
                    class="execute-btn">执行</button>
            <button v-if="task.status === 'failed'"
                    @click="retryTask(task)"
                    class="retry-btn">重试</button>
            <button v-if="task.canHandover"
                    @click="handoverTask(task)"
                    class="handover-btn">转人工</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 个性化设置 -->
    <div class="personalization-settings">
      <h3>个性化设置</h3>
      <div class="settings-grid">
        <div class="setting-item">
          <label>沟通风格</label>
          <select v-model="personalSettings.communicationStyle">
            <option value="professional">专业严谨</option>
            <option value="friendly">亲切活泼</option>
            <option value="empathetic">同理心强</option>
          </select>
        </div>
        <div class="setting-item">
          <label>建议频率</label>
          <select v-model="personalSettings.suggestionFrequency">
            <option value="high">高频建议</option>
            <option value="medium">适中建议</option>
            <option value="low">仅重要建议</option>
          </select>
        </div>
        <div class="setting-item">
          <label>显示密度</label>
          <select v-model="personalSettings.displayDensity">
            <option value="detailed">详细模式</option>
            <option value="compact">简洁模式</option>
          </select>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useAICopilot } from '@/composables/useAICopilot'
import { useWebSocket } from '@/composables/useWebSocket'

export default {
  name: 'AICopilotPanel',
  props: {
    conversationId: {
      type: Number,
      required: true
    }
  },
  setup(props) {
    const {
      aiStatus,
      currentConfidence,
      journeyNodes,
      currentNodeId,
      currentNodeIndex,
      suggestions,
      currentIntent,
      intentHistory,
      executionTasks,
      personalSettings,
      applySuggestion,
      optimizeSuggestion,
      executeTask,
      retryTask,
      handoverTask
    } = useAICopilot(props.conversationId)

    const aiStatusText = computed(() => {
      switch (aiStatus.value) {
        case 'active': return '活跃'
        case 'thinking': return '思考中'
        case 'idle': return '待机'
        case 'error': return '异常'
        default: return '未知'
      }
    })

    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString()
    }

    const getStatusText = (status) => {
      const statusMap = {
        'pending': '待执行',
        'executing': '执行中',
        'completed': '已完成',
        'failed': '失败'
      }
      return statusMap[status] || status
    }

    return {
      aiStatus,
      aiStatusText,
      currentConfidence,
      journeyNodes,
      currentNodeId,
      currentNodeIndex,
      suggestions,
      currentIntent,
      intentHistory,
      executionTasks,
      personalSettings,
      applySuggestion,
      optimizeSuggestion,
      executeTask,
      retryTask,
      handoverTask,
      formatTime,
      getStatusText
    }
  }
}
</script>
```

---

## 开发实施方案

### 📅 分阶段实施计划

```mermaid
gantt
    title AI功能重构实施时序图
    dateFormat  YYYY-MM-DD
    section 阶段一：移除现有功能
    OpenAI集成移除          :a1, 2025-08-18, 1w
    Captain模块移除         :a2, after a1, 2w
    AgentBot框架清理        :a3, after a2, 1w
    数据库清理和迁移        :a4, after a3, 1w

    section 阶段二：Claude-Code集成
    服务适配器开发          :b1, after a4, 3w
    API接口集成            :b2, after b1, 2w
    数据同步机制           :b3, after b2, 2w
    基础功能测试           :b4, after b3, 1w

    section 阶段三：策略工作台开发
    多维表格界面           :c1, after b4, 4w
    触发器系统             :c2, after c1, 3w
    工具箱系统             :c3, after c2, 3w
    模板中心               :c4, after c3, 2w

    section 阶段四：AI副驾面板开发
    旅程罗盘组件           :d1, after c4, 3w
    智能建议系统           :d2, after d1, 3w
    意图分析引擎           :d3, after d2, 3w
    个性化学习             :d4, after d3, 2w

    section 阶段五：系统集成测试
    端到端测试             :e1, after d4, 2w
    性能优化               :e2, after e1, 2w
    用户验收测试           :e3, after e2, 1w
    生产部署               :e4, after e3, 1w
```

### 👥 团队配置和资源分配

```mermaid
graph TB
    subgraph "核心开发团队"
        A[项目经理 1人] --> A1[整体进度管控]
        B[技术架构师 1人] --> B1[架构设计和技术决策]
        C[前端开发 2人] --> C1[Vue.js界面开发]
        D[后端开发 2人] --> D1[Rails API和集成]
        E[AI工程师 2人] --> E1[Claude-Code适配和AI功能]
        F[测试工程师 1人] --> F1[质量保证和测试]
    end

    subgraph "预算分配"
        G[总预算: 280万元] --> G1[人力成本: 200万 71%]
        G --> G2[基础设施: 40万 14%]
        G --> G3[第三方服务: 40万 15%]
    end

    subgraph "时间安排"
        H[总周期: 32周] --> H1[移除阶段: 5周]
        H --> H2[集成阶段: 8周]
        H --> H3[开发阶段: 12周]
        H --> H4[测试部署: 6周]
        H --> H5[缓冲时间: 1周]
    end
```

### 🔧 技术实现要点

#### Claude-Code服务部署

```yaml
# docker-compose.yml
version: '3.8'
services:
  chatwoot-rails:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=**************************************/chatwoot
      - REDIS_URL=redis://redis:6379
      - AI_SERVICE_URL=http://claude-code-service:4000
    depends_on:
      - db
      - redis
      - claude-code-service

  claude-code-service:
    build: ./claude-code-service
    ports:
      - "4000:4000"
    environment:
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - DATABASE_URL=**************************************/chatwoot
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=chatwoot
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

#### 环境配置

```bash
# .env 配置
# Anthropic API配置
ANTHROPIC_API_KEY=your_anthropic_api_key
CLAUDE_MODEL=claude-3-sonnet-20240229

# AI服务配置
AI_SERVICE_URL=http://localhost:4000
AI_SERVICE_TOKEN=your_service_token

# 功能开关
ENABLE_STRATEGY_WORKBENCH=true
ENABLE_AI_COPILOT=true
ENABLE_LEGACY_AI=false

# 性能配置
AI_REQUEST_TIMEOUT=30
AI_CACHE_TTL=3600
MAX_CONCURRENT_AI_REQUESTS=10
```

---

## 技术实现要点

### 🔒 安全性保障

```mermaid
graph TB
    subgraph "安全防护体系"
        A[API安全] --> A1[JWT认证]
        A --> A2[请求签名验证]
        A --> A3[IP白名单]
        A --> A4[请求限流]

        B[数据安全] --> B1[敏感数据加密]
        B --> B2[传输层TLS]
        B --> B3[数据库加密]
        B --> B4[审计日志]

        C[AI安全] --> C1[提示注入防护]
        C --> C2[输出内容过滤]
        C --> C3[模型调用监控]
        C --> C4[异常检测]

        D[权限控制] --> D1[角色权限管理]
        D --> D2[功能级权限]
        D --> D3[数据级权限]
        D --> D4[操作审计]
    end
```

### ⚡ 性能优化策略

```typescript
// AI请求优化
class AIRequestOptimizer {
  private cache: Map<string, CachedResponse> = new Map();
  private requestQueue: RequestQueue = new RequestQueue();

  async optimizeRequest(request: AIRequest): Promise<AIResponse> {
    // 1. 缓存检查
    const cacheKey = this.generateCacheKey(request);
    const cached = this.cache.get(cacheKey);
    if (cached && !this.isCacheExpired(cached)) {
      return cached.response;
    }

    // 2. 请求去重
    if (this.requestQueue.hasPendingRequest(cacheKey)) {
      return await this.requestQueue.waitForResult(cacheKey);
    }

    // 3. 批量处理
    if (request.canBatch) {
      return await this.batchProcessor.addToBatch(request);
    }

    // 4. 执行请求
    const response = await this.executeRequest(request);

    // 5. 缓存结果
    this.cache.set(cacheKey, {
      response,
      timestamp: Date.now(),
      ttl: this.calculateTTL(request)
    });

    return response;
  }

  private calculateTTL(request: AIRequest): number {
    // 根据请求类型动态计算缓存时间
    switch (request.type) {
      case 'intent_analysis': return 300; // 5分钟
      case 'reply_suggestion': return 60;  // 1分钟
      case 'strategy_evaluation': return 600; // 10分钟
      default: return 180; // 3分钟
    }
  }
}
```

### 📊 监控和日志

```typescript
// 监控系统
class AIMonitoringSystem {
  private metrics: MetricsCollector;
  private logger: Logger;

  constructor() {
    this.metrics = new MetricsCollector();
    this.logger = new Logger('ai-system');
  }

  trackAIRequest(request: AIRequest, response: AIResponse, duration: number) {
    // 性能指标
    this.metrics.histogram('ai_request_duration', duration, {
      type: request.type,
      model: request.model
    });

    // 成功率指标
    this.metrics.counter('ai_request_total', {
      type: request.type,
      status: response.success ? 'success' : 'error'
    });

    // 成本指标
    this.metrics.counter('ai_cost_total', response.cost || 0, {
      model: request.model
    });

    // 详细日志
    this.logger.info('AI request processed', {
      requestId: request.id,
      type: request.type,
      duration,
      success: response.success,
      cost: response.cost,
      tokensUsed: response.tokensUsed
    });
  }

  trackUserInteraction(interaction: UserInteraction) {
    // 用户行为指标
    this.metrics.counter('user_interaction_total', {
      type: interaction.type,
      feature: interaction.feature
    });

    // 满意度指标
    if (interaction.feedback) {
      this.metrics.histogram('user_satisfaction', interaction.feedback.score, {
        feature: interaction.feature
      });
    }
  }
}
```

---

## 风险评估和应对

### ⚠️ 主要风险识别

```mermaid
graph TB
    subgraph "技术风险"
        A[Claude-Code集成复杂度] --> A1[API兼容性问题]
        A --> A2[性能瓶颈]
        A --> A3[稳定性风险]

        B[数据迁移风险] --> B1[数据丢失]
        B --> B2[迁移失败]
        B --> B3[业务中断]
    end

    subgraph "业务风险"
        C[用户接受度] --> C1[学习成本高]
        C --> C2[功能复杂度]
        C --> C3[使用习惯改变]

        D[功能完整性] --> D1[PRD理念实现偏差]
        D --> D2[核心功能缺失]
        D --> D3[用户体验不佳]
    end

    subgraph "项目风险"
        E[开发进度] --> E1[技术难度超预期]
        E --> E2[团队能力不足]
        E --> E3[需求变更频繁]

        F[成本控制] --> F1[开发成本超支]
        F --> F2[AI调用成本过高]
        F --> F3[基础设施成本]
    end
```

### 🛡️ 风险应对策略

#### 技术风险应对

```yaml
Claude-Code集成风险:
  风险等级: 高
  应对措施:
    - 建立完整的适配器抽象层
    - 实施渐进式集成策略
    - 建立完善的回滚机制
    - 进行充分的兼容性测试

数据迁移风险:
  风险等级: 中
  应对措施:
    - 制定详细的数据迁移计划
    - 实施分批次迁移策略
    - 建立数据备份和恢复机制
    - 进行迁移演练和验证

性能风险:
  风险等级: 中
  应对措施:
    - 实施AI请求缓存策略
    - 建立请求队列和限流机制
    - 进行性能压测和优化
    - 监控系统资源使用情况
```

#### 业务风险应对

```yaml
用户接受度风险:
  风险等级: 高
  应对措施:
    - 实施渐进式功能发布
    - 提供详细的用户培训
    - 建立用户反馈收集机制
    - 保留传统功能作为过渡

功能完整性风险:
  风险等级: 中
  应对措施:
    - 建立PRD理念验证机制
    - 实施用户故事驱动开发
    - 进行原型验证和用户测试
    - 建立功能完整性检查清单
```

### 📈 成功指标定义

```mermaid
graph LR
    subgraph "技术指标"
        A[系统性能] --> A1[响应时间 < 2秒]
        A --> A2[可用性 > 99.5%]
        A --> A3[AI准确率 > 85%]

        B[集成质量] --> B1[API成功率 > 99%]
        B --> B2[数据一致性 100%]
        B --> B3[错误率 < 1%]
    end

    subgraph "业务指标"
        C[用户体验] --> C1[用户满意度 > 4.0]
        C --> C2[功能使用率 > 60%]
        C --> C3[学习曲线 < 2周]

        D[运营效果] --> D1[客服效率提升 > 40%]
        D --> D2[响应时间缩短 > 50%]
        D --> D3[用户转化率提升 > 20%]
    end

    subgraph "项目指标"
        E[交付质量] --> E1[按时交付率 > 90%]
        E --> E2[预算控制 < 110%]
        E --> E3[缺陷密度 < 0.1/KLOC]

        F[团队效能] --> F1[代码质量 > 8.0]
        F --> F2[测试覆盖率 > 80%]
        F --> F3[文档完整性 > 90%]
    end
```

---

## 总结与建议

### 🎯 方案核心优势

1. **彻底重构**：完全移除现有AI功能，避免技术债务和兼容性问题
2. **能力复用**：充分利用Claude-Code的成熟AI代理能力和扩展机制
3. **理念驱动**：严格按照PRD文档的先进产品理念设计新功能
4. **架构清晰**：微服务架构确保系统的可扩展性和可维护性

### 🚀 实施建议

#### 立即行动项
1. **组建专项团队**：配置AI工程师和架构师，确保技术能力
2. **环境准备**：搭建Claude-Code服务的开发和测试环境
3. **风险评估**：深入评估Claude-Code集成的技术风险
4. **用户调研**：验证PRD理念的市场需求和用户接受度

#### 关键成功因素
1. **技术团队能力**：确保团队具备AI代理系统开发经验
2. **产品理念理解**：深入理解PRD文档的产品哲学和设计理念
3. **用户参与度**：在开发过程中持续收集用户反馈
4. **质量控制**：建立严格的测试和质量保证机制

### 💡 创新价值

本方案将实现以下创新突破：

1. **业界首创的策略工作台**：让用户像搭建乐高一样组合AI能力
2. **人机协同新模式**：AI策略副驾面板重新定义客服工作方式
3. **目标导向智能代理**：从规则驱动转向目标驱动的AI范式
4. **开放式AI平台**：为用户提供无限可能性的AI能力组合

通过这个重构方案，Chatwoot将从传统客服工具升级为真正的智能私域运营平台，为用户创造前所未有的价值。
